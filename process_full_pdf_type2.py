#!/usr/bin/env python3
"""
Script to process all pages of a PDF file as type2 documents.
"""
import os
import sys
import logging
import argparse
import subprocess
import pandas as pd
from pathlib import Path
import PyPDF2

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def get_pdf_page_count(pdf_path):
    """Get the number of pages in a PDF file."""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            return len(pdf_reader.pages)
    except Exception as e:
        logger.error(f"Error getting page count: {e}")
        return 0

def process_page(pdf_path, page_num, output_dir):
    """Process a single page of a PDF file."""
    pdf_path = Path(pdf_path)
    logger.info(f"Processing page {page_num} of {pdf_path}")

    # Create output directory for this page
    page_output_dir = output_dir / f"page{page_num}"
    page_output_dir.mkdir(parents=True, exist_ok=True)

    # Run the generate_subtables script
    generate_cmd = [
        "python3", "Subprojects/JORTPROC_GenerateSubtacles/run_standalone.py",
        str(pdf_path), str(page_num), "type2", "--output_dir", str(page_output_dir)
    ]

    try:
        logger.info(f"Running command: {' '.join(generate_cmd)}")
        subprocess.run(generate_cmd, check=True)

        # Check if the output files exist
        # Try to find column files with the correct page number pattern
        column_files = list(page_output_dir.glob(f"page{page_num}/page{page_num}_table1_column*_inverted_lined.png"))

        # If not found, check if they're directly in the page directory without the subfolder
        if not column_files:
            column_files = list(page_output_dir.glob(f"page{page_num}_table1_column*_inverted_lined.png"))

        if column_files:
            logger.info(f"Found {len(column_files)} column files for page {page_num}")

            # Get the PDF file name without extension
            pdf_name = Path(pdf_path).stem

            # Run the OCR.space script
            ocr_output_file = page_output_dir / f"{pdf_name}_page{page_num}_ocr_results.csv"

            # Determine the correct input directory based on where the column files were found
            if "page" in str(column_files[0]):
                # If files are in a subfolder like page1/page1_table1_column*
                input_dir = page_output_dir / f"page{page_num}"
            else:
                # If files are directly in the page directory
                input_dir = page_output_dir

            ocr_cmd = [
                "python3", "Subprojects/JORTPROC_OCRSpace/run_standalone.py",
                str(input_dir), "--headers", "NOMS & PRENOMS,CIN,ADRESSE",
                "--output_file", str(ocr_output_file)
            ]

            logger.info(f"Running command: {' '.join(ocr_cmd)}")
            subprocess.run(ocr_cmd, check=True)

            # Check if the OCR results file exists
            if ocr_output_file.exists():
                logger.info(f"OCR results file exists for page {page_num}: {ocr_output_file}")

                # Read the OCR results
                df = pd.read_csv(ocr_output_file)
                logger.info(f"OCR results for page {page_num} have {len(df)} rows")

                return df
            else:
                logger.error(f"OCR results file does not exist for page {page_num}: {ocr_output_file}")
        else:
            logger.error(f"No column files found for page {page_num}")

    except subprocess.CalledProcessError as e:
        logger.error(f"Error processing page {page_num}: {e}")

    return None

def process_pdf(pdf_path, output_dir, start_page=1, end_page=None):
    """Process all pages of a PDF file."""
    # Get the PDF file name without extension to use in output paths
    pdf_path = Path(pdf_path)
    pdf_name = pdf_path.stem

    # Create output directory with the PDF name
    output_dir = Path(output_dir)
    if not output_dir.name.startswith(pdf_name):
        output_dir = output_dir / pdf_name.replace(" ", "_")
    output_dir.mkdir(parents=True, exist_ok=True)

    # Get the number of pages in the PDF
    num_pages = get_pdf_page_count(pdf_path)
    logger.info(f"PDF has {num_pages} pages")

    if end_page is None:
        end_page = num_pages

    # Validate page range
    start_page = max(1, start_page)
    end_page = min(num_pages, end_page)

    # Process each page
    all_results = []
    for page_num in range(start_page, end_page + 1):
        df = process_page(pdf_path, page_num, output_dir)
        if df is not None:
            all_results.append(df)

    # Combine all results
    if all_results:
        combined_df = pd.concat(all_results, ignore_index=True)

        # Save the combined results with the PDF name
        combined_output_file = output_dir / f"{pdf_name}_combined_ocr_results.csv"
        combined_df.to_csv(combined_output_file, index=False)
        logger.info(f"Saved combined OCR results with {len(combined_df)} rows to {combined_output_file}")
    else:
        logger.error("No OCR results to combine")

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Process all pages of a PDF file as type2 documents")
    parser.add_argument("pdf_path", help="Path to the PDF file")
    parser.add_argument("--output_dir", default="output", help="Output directory (PDF name will be appended)")
    parser.add_argument("--start_page", type=int, default=1, help="Start page number (1-based)")
    parser.add_argument("--end_page", type=int, default=None, help="End page number (1-based)")

    args = parser.parse_args()

    process_pdf(args.pdf_path, args.output_dir, args.start_page, args.end_page)

if __name__ == "__main__":
    main()
