#!/usr/bin/env python3
"""
Comprehensive test script for testing the "#NA#" text in empty cells feature.
Tests both type1 and type2 document processing.
"""
import os
import sys
import logging
import shutil
import cv2
import numpy as np
import pandas as pd
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def test_type1_document():
    """Test the "#NA#" text in empty cells feature with a type1 document."""
    logger.info("=== Testing Type1 Document ===")

    # Define the paths
    pdf_path = "data/examples/type1-information-in-two-comulns-hidden-table-separator.pdf"
    output_dir = Path("test_output/na_hash_cells_test/type1")
    output_dir.mkdir(parents=True, exist_ok=True)

    # Run the generate_subtables script
    logger.info(f"Running generate_subtables on {pdf_path}")
    os.system(f"python3 Subprojects/JORTPROC_GenerateSubtacles/run_standalone.py {pdf_path} 1 type1 --output_dir {output_dir}")

    # Check if the output files exist
    subtable_files = list(output_dir.glob("page1/page1_type1_sub_table_*_inverted_lined.png"))
    logger.info(f"Found {len(subtable_files)} subtable files")

    # Check if the "#NA#" text is present in the images
    for subtable_file in subtable_files:
        logger.info(f"Checking {subtable_file}")
        img = cv2.imread(str(subtable_file))
        if img is None:
            logger.error(f"Failed to load image: {subtable_file}")
            continue

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Apply thresholding to get a binary image
        _, binary = cv2.threshold(gray, 128, 255, cv2.THRESH_BINARY)

        # Use template matching to find "#NA#" text
        # Create a template with "#NA#" text
        template = np.zeros((50, 100), dtype=np.uint8)
        cv2.putText(template, "#NA#", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, 255, 2)

        # Apply template matching
        result = cv2.matchTemplate(binary, template, cv2.TM_CCOEFF_NORMED)

        # Find locations where the template matches
        locations = np.where(result >= 0.5)
        num_matches = len(locations[0])

        logger.info(f"Found {num_matches} '#NA#' instances in {subtable_file}")

    # Run the OCR.space script on the subtables
    logger.info(f"Running OCR.space on {output_dir}/page1")
    ocr_output_file = output_dir / "ocr_results.csv"
    os.system(f"python3 Subprojects/JORTPROC_OCRSpace/run_standalone.py {output_dir}/page1 --headers 'Nom et prénom,Identifiant' --output_file {ocr_output_file}")

    # Check if the OCR results file exists
    if ocr_output_file.exists():
        logger.info(f"OCR results file exists: {ocr_output_file}")

        # Read the OCR results
        df = pd.read_csv(ocr_output_file)
        logger.info(f"OCR results have {len(df)} rows")

        # Check if there are any "#NA#" values in the results
        na_values = df.isin(["#NA#", "#NA", "NA#"]).sum().sum()
        logger.info(f"Found {na_values} '#NA#' values in the OCR results")

        return True
    else:
        logger.error(f"OCR results file does not exist: {ocr_output_file}")
        return False

def test_type2_document():
    """Test the "#NA#" text in empty cells feature with a type2 document."""
    logger.info("=== Testing Type2 Document ===")

    # Define the paths
    pdf_path = "data/examples/type2-information-in-multiple-columns.pdf"
    output_dir = Path("test_output/na_hash_cells_test/type2")
    output_dir.mkdir(parents=True, exist_ok=True)

    # Run the generate_subtables script
    logger.info(f"Running generate_subtables on {pdf_path}")
    os.system(f"python3 Subprojects/JORTPROC_GenerateSubtacles/run_standalone.py {pdf_path} 1 type2 --output_dir {output_dir}")

    # Check if the output files exist
    column_files = list(output_dir.glob("page1/page1_table1_column*_inverted_lined.png"))
    logger.info(f"Found {len(column_files)} column files")

    # Check if the "#NA#" text is present in the images
    for column_file in column_files:
        logger.info(f"Checking {column_file}")
        img = cv2.imread(str(column_file))
        if img is None:
            logger.error(f"Failed to load image: {column_file}")
            continue

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Apply thresholding to get a binary image
        _, binary = cv2.threshold(gray, 128, 255, cv2.THRESH_BINARY)

        # Use template matching to find "#NA#" text
        # Create a template with "#NA#" text
        template = np.zeros((50, 100), dtype=np.uint8)
        cv2.putText(template, "#NA#", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, 255, 2)

        # Apply template matching
        result = cv2.matchTemplate(binary, template, cv2.TM_CCOEFF_NORMED)

        # Find locations where the template matches
        locations = np.where(result >= 0.5)
        num_matches = len(locations[0])

        logger.info(f"Found {num_matches} '#NA#' instances in {column_file}")

    # Run the OCR.space script on the columns
    logger.info(f"Running OCR.space on {output_dir}/page1")
    ocr_output_file = output_dir / "ocr_results.csv"
    os.system(f"python3 Subprojects/JORTPROC_OCRSpace/run_standalone.py {output_dir}/page1 --headers 'NOMS & PRENOMS,CIN,ADRESSE' --output_file {ocr_output_file}")

    # Check if the OCR results file exists
    if ocr_output_file.exists():
        logger.info(f"OCR results file exists: {ocr_output_file}")

        # Read the OCR results
        df = pd.read_csv(ocr_output_file)
        logger.info(f"OCR results have {len(df)} rows")

        # Compare with the ground truth
        ground_truth_file = "data/examples/type2-information-in-multiple-columns-manually-extracted-table-full.csv"
        if os.path.exists(ground_truth_file):
            gt_df = pd.read_csv(ground_truth_file)
            logger.info(f"Ground truth has {len(gt_df)} rows")

            # Check if the number of rows is the same
            if len(df) == len(gt_df):
                logger.info("Number of rows matches the ground truth")
            else:
                logger.warning(f"Number of rows does not match the ground truth: {len(df)} vs {len(gt_df)}")

        return True
    else:
        logger.error(f"OCR results file does not exist: {ocr_output_file}")
        return False

def main():
    """Main entry point for the test script."""
    # Test type1 document
    type1_result = test_type1_document()

    # Test type2 document
    type2_result = test_type2_document()

    # Print summary
    logger.info("=== Test Summary ===")
    logger.info(f"Type1 Document: {'PASSED' if type1_result else 'FAILED'}")
    logger.info(f"Type2 Document: {'PASSED' if type2_result else 'FAILED'}")

    # Return overall result
    return type1_result and type2_result

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
