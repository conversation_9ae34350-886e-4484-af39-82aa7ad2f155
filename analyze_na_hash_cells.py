#!/usr/bin/env python3
"""
Script to analyze the number of "#NA#" instances in column images.
"""
import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def count_na_hash_instances(image_path):
    """Count the number of "#NA#" instances in an image."""
    # Load the image
    img = cv2.imread(str(image_path))
    if img is None:
        logger.error(f"Failed to load image: {image_path}")
        return 0

    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Apply thresholding to get a binary image
    _, binary = cv2.threshold(gray, 128, 255, cv2.THRESH_BINARY)

    # Use template matching to find "#NA#" text
    # Create a template with "#NA#" text
    template = np.zeros((50, 100), dtype=np.uint8)
    cv2.putText(template, "#NA#", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, 255, 2)

    # Apply template matching
    result = cv2.matchTemplate(binary, template, cv2.TM_CCOEFF_NORMED)

    # Find locations where the template matches
    locations = np.where(result >= 0.5)
    num_matches = len(locations[0])

    return num_matches

def visualize_na_hash_instances(image_path, output_path=None):
    """Visualize the "#NA#" instances in an image."""
    # Load the image
    img = cv2.imread(str(image_path))
    if img is None:
        logger.error(f"Failed to load image: {image_path}")
        return

    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Apply thresholding to get a binary image
    _, binary = cv2.threshold(gray, 128, 255, cv2.THRESH_BINARY)

    # Use template matching to find "#NA#" text
    # Create a template with "#NA#" text
    template = np.zeros((50, 100), dtype=np.uint8)
    cv2.putText(template, "#NA#", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, 255, 2)

    # Apply template matching
    result = cv2.matchTemplate(binary, template, cv2.TM_CCOEFF_NORMED)

    # Find locations where the template matches
    threshold = 0.5
    locations = np.where(result >= threshold)

    # Create a copy of the image for visualization
    vis_img = img.copy()

    # Draw rectangles around the matches
    w, h = template.shape[::-1]
    for pt in zip(*locations[::-1]):
        cv2.rectangle(vis_img, pt, (pt[0] + w, pt[1] + h), (0, 0, 255), 2)

    # Save the visualization
    if output_path:
        cv2.imwrite(str(output_path), vis_img)

    # Display the visualization
    plt.figure(figsize=(15, 10))
    plt.imshow(cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB))
    plt.title(f"#NA# Instances in {Path(image_path).name}")
    plt.axis('off')

    if output_path:
        plt.savefig(str(output_path))
    else:
        plt.show()

def main():
    """Main entry point for the script."""
    # Define the paths
    input_dir = Path("test_output/045Journal_type2_all_pages/subtables")
    output_dir = Path("test_output/045Journal_type2_all_pages/na_hash_analysis")
    output_dir.mkdir(parents=True, exist_ok=True)

    # Find all column images for all pages
    column_files = []
    for page_num in range(1, 5):  # Pages 1 to 4
        page_dir = input_dir / f"page{page_num}" / f"page{page_num}"
        page_column_files = list(page_dir.glob(f"page{page_num}_table1_column*_inverted_lined.png"))
        column_files.extend(page_column_files)

    logger.info(f"Found {len(column_files)} column files")

    # Count and visualize "#NA#" instances in each column
    for column_file in column_files:
        logger.info(f"Analyzing {column_file}")

        # Count "#NA#" instances
        num_matches = count_na_hash_instances(column_file)
        logger.info(f"Found {num_matches} '#NA#' instances in {column_file}")

        # Visualize "#NA#" instances
        output_path = output_dir / f"{column_file.stem}_na_hash_visualization.png"
        visualize_na_hash_instances(column_file, output_path)
        logger.info(f"Saved visualization to {output_path}")

if __name__ == "__main__":
    main()
