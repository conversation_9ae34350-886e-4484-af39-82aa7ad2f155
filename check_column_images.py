#!/usr/bin/env python3
"""
<PERSON><PERSON>t to check the column images for type2 documents.
"""
import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# Define the paths to the column images
column_paths = [
    "test_output/type2_test/na_hash_cells/subtables/page1/page1_table1_column1_inverted_lined.png",
    "test_output/type2_test/na_hash_cells/subtables/page1/page1_table1_column2_inverted_lined.png",
    "test_output/type2_test/na_hash_cells/subtables/page1/page1_table1_column3_inverted_lined.png"
]

# Load the images
column_images = []
for path in column_paths:
    img = cv2.imread(path)
    if img is None:
        print(f"Failed to load image: {path}")
        continue
    column_images.append(img)

# Print image dimensions
for i, img in enumerate(column_images):
    print(f"Column {i+1} dimensions: {img.shape}")

# Save a visualization of the columns
output_dir = Path("test_output/type2_test/na_hash_cells_analysis")
output_dir.mkdir(parents=True, exist_ok=True)

# Create a visualization of the columns
fig, axes = plt.subplots(1, 3, figsize=(15, 10))
for i, img in enumerate(column_images):
    # Convert from BGR to RGB for matplotlib
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    axes[i].imshow(img_rgb)
    axes[i].set_title(f"Column {i+1}")
    axes[i].axis('off')

plt.tight_layout()
plt.savefig(str(output_dir / "column_comparison.png"))
print(f"Saved column comparison to {output_dir / 'column_comparison.png'}")

# Analyze the vertical projection of each column
for i, img in enumerate(column_images):
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Calculate vertical projection
    v_projection = np.sum(gray, axis=0)

    # Normalize projection
    if np.max(v_projection) > 0:
        v_projection = v_projection / np.max(v_projection)

    # Plot the projection
    plt.figure(figsize=(10, 5))
    plt.plot(v_projection)
    plt.title(f"Vertical Projection for Column {i+1}")
    plt.xlabel("X-coordinate")
    plt.ylabel("Normalized Projection Value")
    plt.grid(True)
    plt.savefig(str(output_dir / f"column{i+1}_projection.png"))
    print(f"Saved projection for column {i+1} to {output_dir / f'column{i+1}_projection.png'}")

print("Analysis complete.")
