# JortExtractor Features

## Core Features

### 1. PDF Processing
- **Description**: Converts PDF documents to images and processes them for text extraction.
- **Why Added**: To handle PDF documents containing tables of person names.
- **Dependencies**: pdf2image, OpenCV, NumPy

### 2. Document Classification
- **Description**: Automatically detects the document type based on layout and content with confidence scores.
- **Why Added**: To apply appropriate processing strategies for different document formats.
- **Dependencies**: OpenCV, pytesseract
- **Recent Enhancements**: Added rotation detection and correction, confidence scoring, and improved handling of mixed formats.

### 3. Image Processing
- **Description**: Applies various image processing techniques to optimize images for OCR.
- **Why Added**: To improve OCR accuracy by enhancing image quality.
- **Dependencies**: OpenCV, NumPy
- **Recent Enhancements**:
  - Improved error handling for empty pages and corrupted images
  - Better handling of OpenCV errors
  - Enhanced image quality preservation during cropping operations
  - Multiple preprocessing techniques (grayscale, adaptive thresholding, color inversion, denoising)
  - High-quality PNG output for intermediate images

### 4. OCR Processing
- **Description**: Extracts text from images using Optical Character Recognition.
- **Why Added**: To convert image-based text to machine-readable format.
- **Dependencies**: pytesseract, Tesseract OCR
- **Recent Enhancements**:
  - Multi-version OCR that tries different preprocessing techniques
  - Multiple PSM modes (6, 4, 11) for better text recognition
  - Improved handling of poor quality images
  - Enhanced OCR accuracy for Type1 documents

### 5. Name Validation
- **Description**: Validates and corrects extracted names to improve accuracy.
- **Why Added**: To filter out non-name text and correct OCR errors.
- **Dependencies**: Regular expressions, custom validation rules

### 6. Parallel Processing
- **Description**: Processes multiple documents or pages in parallel.
- **Why Added**: To improve performance for batch processing.
- **Dependencies**: Python multiprocessing

## Testing Features

### 7. Test Evaluation
- **Description**: Evaluates test quality and generates detailed reports.
- **Why Added**: To identify issues in the test suite and prioritize enhancements.
- **Dependencies**: unittest, JSON, CSV

### 8. Ground Truth Validation
- **Description**: Validates OCR results against ground truth data with visual processing steps report.
- **Why Added**: To measure accuracy metrics (precision, recall, F1 score) and visualize the processing pipeline.
- **Dependencies**: pandas, matplotlib, HTML
- **Recent Enhancements**: Added HTML report showing consecutive images of the processing steps (original pages, detected tables, pre-OCR tables, pre-OCR columns)

### 9. Test Tracking
- **Description**: Tracks test status and enhancements over time.
- **Why Added**: To monitor progress in improving test coverage and quality.
- **Dependencies**: CSV

### 10. OCR Benchmarking
- **Description**: Benchmarks our OCR implementation and compares with OCR.space API (for benchmarking only).
- **Why Added**: To evaluate our OCR performance and identify areas for improvement.
- **Dependencies**: pytesseract, OpenCV, pandas
- **Note**: OCR.space API is used ONLY for benchmarking, NEVER as an alternative to our own OCR implementation.

## Document Type Support

### 11. Type1 Document Support
- **Description**: Processes documents with two columns and hidden table separators.
- **Why Added**: To handle a common document format in the dataset.
- **Dependencies**: Image processing, OCR
- **Recent Enhancements**:
  - Improved image quality preservation during cropping
  - Multiple image preprocessing techniques for better OCR
  - Enhanced handling of poor quality cropped images
  - Color inversion to improve text detection
  - Multi-version OCR with different preprocessing techniques
  - Added one-line-per-subtable flag for isolating OCR quality issues during testing
  - Completely removed text processing to preserve raw OCR output
  - Eliminated all filtering and cleaning steps for transparent results
  - Optimized OCR settings with LSTM-only engine and single column mode
  - Added adaptive thresholding and morphological operations for better text recognition
  - Implemented comprehensive OCR optimization with 8 preprocessing techniques
  - Added automatic selection from 48 different OCR configurations per image
  - Dynamic evaluation and selection of best preprocessing and OCR settings
  - Added column separation detection for improved table structure recognition
  - Focused exclusively on vertical column separation for Type1 documents
  - Implemented right-to-left analysis for accurate column boundary detection
  - Added horizontal projection analysis with visualization for debugging
  - Created minimum value detection for optimal column separation
  - Fixed misplaced vertical lines in subtable images
  - Added detailed projection analysis plots for visual inspection
  - Implemented automatic text splitting based on detected column boundaries
  - Added character position estimation from pixel coordinates
  - Implemented intelligent space-aware text splitting to avoid breaking words
  - Properly separates name and identifier columns in OCR results
  - Added detection of horizontal lines using morphological operations
  - Implemented row boundary detection to properly segment rows
  - Added word-level OCR with bounding box information
  - Grouped words into rows based on detected row boundaries
  - Fixed handling of multi-line names by respecting row boundaries
  - Standardized line colors for better visual consistency
  - Improved readability of the lined image for debugging
  - Created OCR-friendly lined images with thin lines for better recognition
  - Added table structure information to help Tesseract recognize rows and columns
  - Implemented Hough Line Transform for better table structure detection
  - Added advanced OCR configuration with table detection parameters
  - Expanded PSM modes to improve text recognition in different contexts
  - Implemented techniques from fazlurnu/Text-Extraction-Table-Image repository
  - Added detailed documentation for OCR configuration parameters
  - Optimized image processing pipeline for better performance
  - Added borders to close tables (left, right, and bottom)
  - Simplified table structure detection by combining best approaches
  - Reduced complexity by using only the best-performing techniques
  - Eliminated unnecessary intermediate image processing steps
  - Fixed OCR configuration to use only the best-performing settings
  - Enhanced identifier formatting to extract only numbers from the second column
  - Added standardized formatting with "*****" prefix followed by 3 digits
  - Implemented digit extraction and padding to ensure consistent 3-digit format
  - Added fallback to "*****000" when no digits are found in the identifier
  - Enhanced OCR.space API integration with table recognition
  - Modified OCR.space to use the same inverted images as local OCR
  - Added orientation detection for OCR.space API
  - Ensured fair benchmarking by using identical images for both OCR methods
  - Refined OCR.space API integration with proper multipart/form-data POST requests
  - Fixed file parameter name and MIME type for optimal API interaction
  - Improved OCR.space API logging for better debugging
  - Enhanced OCR.space API integration to preserve full image quality
  - Used direct file upload with multipart/form-data to preserve image quality
  - Maintained proper MIME type 'image/png' for uploaded files
  - Optimized OCR.space API integration with exact parameters from successful browser requests
  - Switched to OCREngine 5 for better accuracy with table documents
  - Changed language to 'fre' (French) for better recognition of French names
  - Added additional parameters for optimal OCR performance
  - Improved error handling and cleanup of temporary files
  - Enhanced OCR.space API integration to use lined table images
  - Used the same lined images that show table structure for better OCR results
  - Improved comparison between local OCR and OCR.space by using structured images
  - Fixed OCR.space column detection for proper table structure recognition
  - Modified extract_text method to properly handle tab-separated columns
  - Directly extracted name-identifier pairs from OCR.space results
  - Simplified the processing pipeline for OCR.space results
  - Improved OCR.space identifier formatting by removing spaces between asterisks and numbers
  - Skipped header lines in OCR.space results to avoid processing column names
  - Added regex pattern to ensure consistent identifier formatting
  - Enhanced OCR.space multi-line name handling
  - Implemented logic to detect and merge multi-line names in OCR.space results
  - Removed default placeholder identifiers for empty cells
  - Combined names from consecutive rows when the first row has no identifier
  - Implemented strict comparison for ground truth testing
  - Created a new StrictComparison class for exact matching against ground truth
  - Added detailed error reporting with Levenshtein distance calculations
  - Generated comprehensive comparison reports for both local OCR and OCR.space
  - Improved image quality preservation in OCR pipeline
  - Reduced aggressive image resizing to preserve quality for OCR.space
  - Optimized image processing for OCR engines
  - Used inverted images for local OCR and lined images for OCR.space
  - Optimized Tesseract configuration based on expert recommendations
  - Implemented deskewing to improve alignment of text in tables
  - Added adaptive thresholding for better binarization
  - Applied specific table recognition parameters for Tesseract
  - Added Levenshtein Distance 1 metrics to OCR results
  - Implemented counting of near matches with Levenshtein Distance 1
  - Enhanced comparison reports with near match statistics
  - Improved evaluation of OCR quality by tracking almost-correct matches
  - Added preprocessing image saving for inspection and debugging
  - Optimized PSM mode for column-based table recognition
  - Used PSM 4 (single column) for better column recognition in local OCR
  - Maintained different optimal image processing for each OCR engine
  - Excluded lined images from OCR processing to prevent interference
  - Ensured clean images are used for text recognition

### 12. Type2 Document Support
- **Description**: Processes documents with multiple columns and explicit headers.
- **Why Added**: To handle a common document format in the dataset.
- **Dependencies**: Image processing, OCR

### 13. Type3 Document Support
- **Description**: Processes documents with a single column format.
- **Why Added**: To handle a common document format in the dataset.
- **Dependencies**: Image processing, OCR

### 14. Type4 Document Support
- **Description**: Processes documents with three columns of names without headers.
- **Why Added**: To handle a common document format in the dataset.
- **Dependencies**: Image processing, OCR

## Utility Features

### 15. Pre-OCR Image Saving
- **Description**: Saves images just before OCR processing for debugging, with improved table boundary detection to exclude titles and descriptions.
- **Why Added**: To analyze OCR failures and improve processing, ensuring only relevant table content is included.
- **Dependencies**: OpenCV, file system
- **Recent Enhancements**: Improved filtering of non-table elements like titles and descriptions, enhanced table boundary detection to focus on actual table structures.

### 16. Memory Usage Monitoring
- **Description**: Monitors memory usage during processing.
- **Why Added**: To identify memory leaks and optimize resource usage.
- **Dependencies**: psutil

### 17. Logging and Monitoring
- **Description**: Provides detailed logging of processing steps and errors.
- **Why Added**: To facilitate debugging and performance analysis.
- **Dependencies**: Python logging

### 18. Robustness Enhancements
- **Description**: Improves system robustness for handling edge cases and errors.
- **Why Added**: To ensure the system can handle corrupted images, empty pages, and other edge cases.
- **Dependencies**: Exception handling, defensive programming
- **Recent Enhancements**: Added comprehensive error handling for empty pages, rotated documents, and corrupted images.

### 19. Required Document Type Specification
- **Description**: Requires users to specify the document type via command-line argument, removing automatic detection.
- **Why Added**: To ensure proper image processing for OCR by knowing the document type in advance.
- **Dependencies**: Command-line interface

### 20. Type-Specific Processing Pipelines
- **Description**: Specialized processing pipelines for each document type, starting with Type 1.
- **Why Added**: To improve extraction accuracy by applying document-specific processing techniques.
- **Dependencies**: Document type specification, image processing, OCR
- **Type 1 Pipeline**:
  - Implemented a text-based approach instead of table detection (no solid borders)
  - First detect and crop the big table boundaries in the page using projection profiles
  - Optimally remove headers, footers, and excessive margins while preserving all content
  - Process the cropped big table with OCR using multiple approaches
  - Use text layout analysis to identify names and identifiers
  - Detect white separator spaces between columns using vertical projection profiles
  - Divide the table at the exact positions of white spaces (628px, 520px, 596px)
  - Ensure no letters are cut off at sub-table boundaries by using natural white spaces
  - Detect the natural column boundary within each sub-table to properly identify the 2-column structure
  - Group names and identifiers into logical pairs based on vertical alignment within each sub-table
  - Added minimal post-processing:
    - Basic name cleaning (removing extra spaces only)
    - Preserving original OCR output for accurate evaluation
    - Identifier standardization for consistent format (5 asterisks followed by digits)
    - Deduplication of entries with the same name but different identifiers
    - Achieved 100% recall with 58.6% precision (F1 score: 73.9%)
  - Save both columns in CSV output with sub-table information
  - Save pre-OCR images of the big table and each cropped sub-table for better debugging
  - Enhanced image quality preservation during cropping operations:
    - Using high-quality PNG format with no compression
    - Applying multiple preprocessing techniques to each cropped image
    - Generating multiple versions of each image (original, grayscale, adaptive threshold, inverted, denoised)
    - Performing OCR on all image versions with different PSM modes
    - Combining results for maximum text extraction
  - Save all preprocessed versions of each sub-table for analysis

### 21. Code Organization
- **Description**: Proper package structure with clear separation of concerns
- **Why Added**: To improve code maintainability and readability
- **Dependencies**: Python modules and packages
- **Recent Enhancements**:
  - Reorganized codebase structure to follow proper package organization
  - Moved core functionality to jortextractor/core/ directory
  - Moved utility functions to jortextractor/utils/ directory
  - Moved benchmarking tools to jortextractor/benchmarks/ directory
  - Moved parallel processing to jortextractor/parallel/ directory
  - Fixed imports in all files to use the new module structure
  - Removed duplicate files and modules
  - Improved code organization and maintainability
