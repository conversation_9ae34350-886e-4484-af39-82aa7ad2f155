# JortExtractor Configuration

# Input/output paths
input_dir: "./input_pdfs"
output_dir: "./output"
temp_dir: "./temp_images"  # For intermediate table images

# PDF processing settings
dpi: 300  # Resolution for PDF to image conversion
image_format: "png"  # Format for intermediate images
poppler_path: null  # Optional path to poppler binaries

# Table detection settings
table_min_width: 0.3  # Min width as fraction of page width (reduced for single-column tables)
table_min_height: 0.1  # Min height as fraction of page height (reduced for short tables)
min_text_density: 0.2  # Min text density to consider as table (reduced for sparse tables)
line_threshold: 0.8  # Confidence threshold for line detection

# OCR settings
ocr_languages: "eng+fra"  # Languages for Tesseract OCR
ocr_engine_mode: 3  # Tesseract OEM_LSTM_ONLY mode
min_confidence: 70  # Minimum confidence score for OCR results

# Debug settings
save_pre_ocr_tables: true  # Save pre-OCR table images
save_pre_ocr_columns: true  # Save pre-OCR column images
pre_ocr_tables_dir: "./pre-ocr-tables"  # Directory to save pre-OCR table images
pre_ocr_columns_dir: "./pre-ocr-columns"  # Directory to save pre-OCR column images
compare_with_ocr_space: true  # Compare our OCR results with OCR.space

# Data processing
required_fields: ["Name"]  # Fields that must be present
optional_fields: ["Identifier", "Address"]  # Fields to extract if present
min_name_words: 1  # Minimum words in a valid name
max_name_words: 4  # Maximum words in a valid name
skip_patterns:  # Regex patterns to exclude from results
  - "(?i)^(liste|titulaire|compte|banque|droit|union|informe|conformément|loi|article|finances|journal officiel|république tunisienne|annonces|réglementaires|judiciaires|page|\\d{4}|n°|\\d+|pour|sur|et|que|de|la|le|les)$"
  - "(?i).*(avis|notification|déclaration|loi n°|article \\d+).*"
  - "^[^A-Za-zÀ-ÿ]*$"  # Lines with no letters
valid_name_pattern: "^[A-Za-zÀ-ÿ-' ]+$"  # Regex for valid name characters
min_name_length: 3  # Minimum characters for a valid name
min_name_confidence: 80  # Minimum OCR confidence score for names
name_patterns:  # Patterns to identify name columns
  - "nom"
  - "prénom"
  - "name"
identifier_patterns:  # Patterns to identify ID columns
  - "cin"
  - "id"
  - "identifier"

# Performance
max_workers: 4  # Max parallel processes
batch_size: 10  # Max PDFs to process in one batch

# Logging
log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR
log_file: "jortextractor.log"  # Path to log file
