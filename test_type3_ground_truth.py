#!/usr/bin/env python3
"""
Test Type3 multiline detection results against ground truth.
"""

import pandas as pd
import sys
import os
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from tests.benchmarks.ocr_benchmark import compare_with_ground_truth


def load_ground_truth(ground_truth_path: Path) -> list:
    """Load ground truth data from CSV file.

    Args:
        ground_truth_path: Path to ground truth CSV file

    Returns:
        List of ground truth names
    """
    print(f"Loading ground truth from: {ground_truth_path}")

    # Read the CSV file
    df = pd.read_csv(ground_truth_path)

    # Check if this is a simple single-column format (like type3 ground truth)
    if len(df.columns) == 1:
        # Use the first (and only) column
        name_column = df.columns[0]
        print(f"Single column format detected, using column: '{name_column}'")
    else:
        # Get the name column (should be "Nom et prénom")
        name_column = None
        for col in df.columns:
            if 'nom' in col.lower() and 'prénom' in col.lower():
                name_column = col
                break

        if name_column is None:
            # Try alternative column names
            for col in df.columns:
                if 'name' in col.lower() or col.strip() == 'Nom et prénom':
                    name_column = col
                    break

        if name_column is None:
            print(f"Available columns: {list(df.columns)}")
            raise ValueError("Could not find name column in ground truth CSV")

        print(f"Using column: '{name_column}'")

    # Extract names and clean them
    names = df[name_column].dropna().astype(str).str.strip()
    names = names[names != ''].tolist()

    print(f"Loaded {len(names)} ground truth names")
    return names


def load_ocr_results(ocr_results_path: Path) -> list:
    """Load OCR results from CSV file.
    
    Args:
        ocr_results_path: Path to OCR results CSV file
        
    Returns:
        List of dictionaries with 'text' key
    """
    print(f"Loading OCR results from: {ocr_results_path}")
    
    # Read the CSV file
    df = pd.read_csv(ocr_results_path)
    
    # Get the name column (should be "Nom et prénom")
    name_column = None
    for col in df.columns:
        if 'nom' in col.lower() and 'prénom' in col.lower():
            name_column = col
            break
    
    if name_column is None:
        # Try alternative column names
        for col in df.columns:
            if 'name' in col.lower() or col.strip() == 'Nom et prénom':
                name_column = col
                break
    
    if name_column is None:
        print(f"Available columns: {list(df.columns)}")
        raise ValueError("Could not find name column in OCR results CSV")
    
    print(f"Using column: '{name_column}'")
    
    # Extract names and convert to the format expected by compare_with_ground_truth
    names = df[name_column].dropna().astype(str).str.strip()
    names = names[names != ''].tolist()
    
    # Convert to list of dictionaries with 'text' key
    ocr_results = [{'text': name, 'identifier': ''} for name in names]
    
    print(f"Loaded {len(ocr_results)} OCR results")
    return ocr_results


def print_comparison_details(comparison_result: dict, ground_truth: list, ocr_results: list):
    """Print detailed comparison results.
    
    Args:
        comparison_result: Result from compare_with_ground_truth
        ground_truth: List of ground truth names
        ocr_results: List of OCR result dictionaries
    """
    print("\n" + "="*80)
    print("DETAILED COMPARISON RESULTS")
    print("="*80)
    
    # Extract OCR text values for analysis
    ocr_texts = [item['text'] for item in ocr_results]
    matched_texts = set(comparison_result['matched_texts'])
    
    # Find missed names (in ground truth but not in OCR)
    missed_names = []
    for gt_name in ground_truth:
        if gt_name not in ocr_texts:
            missed_names.append(gt_name)
    
    # Find false positives (in OCR but not in ground truth)
    false_positives = []
    for ocr_text in ocr_texts:
        if ocr_text not in ground_truth:
            false_positives.append(ocr_text)
    
    print(f"\n📊 METRICS:")
    print(f"   Precision: {comparison_result['precision']:.3f} ({comparison_result['matched']}/{comparison_result['total_ocr']})")
    print(f"   Recall:    {comparison_result['recall']:.3f} ({comparison_result['matched']}/{comparison_result['total_gt']})")
    print(f"   F1 Score:  {comparison_result['f1_score']:.3f}")
    
    print(f"\n✅ MATCHED NAMES ({len(matched_texts)}):")
    for i, name in enumerate(sorted(matched_texts)[:10], 1):
        print(f"   {i:2d}. {name}")
    if len(matched_texts) > 10:
        print(f"   ... and {len(matched_texts) - 10} more")
    
    print(f"\n❌ MISSED NAMES ({len(missed_names)}):")
    for i, name in enumerate(missed_names[:10], 1):
        print(f"   {i:2d}. {name}")
    if len(missed_names) > 10:
        print(f"   ... and {len(missed_names) - 10} more")
    
    print(f"\n⚠️  FALSE POSITIVES ({len(false_positives)}):")
    for i, name in enumerate(false_positives[:10], 1):
        print(f"   {i:2d}. {name}")
    if len(false_positives) > 10:
        print(f"   ... and {len(false_positives) - 10} more")


def main():
    """Main function to compare Type3 results with ground truth."""
    
    # Define file paths
    ground_truth_path = Path("data/examples/type3-information-in-one-column-manually-extracted-full.csv")
    ocr_results_path = Path("output/type3_test_finetuned/type3-information-in-one-column_page1_ocr_space.csv")
    
    print("🔍 TYPE3 MULTILINE DETECTION - GROUND TRUTH COMPARISON")
    print("="*60)
    
    # Check if files exist
    if not ground_truth_path.exists():
        print(f"❌ Ground truth file not found: {ground_truth_path}")
        return
    
    if not ocr_results_path.exists():
        print(f"❌ OCR results file not found: {ocr_results_path}")
        return
    
    try:
        # Load data
        ground_truth = load_ground_truth(ground_truth_path)
        ocr_results = load_ocr_results(ocr_results_path)
        
        # Compare results
        print(f"\n🔄 Comparing OCR results with ground truth...")
        comparison_result = compare_with_ground_truth(ocr_results, ground_truth)
        
        # Print detailed results
        print_comparison_details(comparison_result, ground_truth, ocr_results)
        
        # Summary
        print(f"\n🎯 SUMMARY:")
        print(f"   Type3 multiline detection extracted {comparison_result['total_ocr']} names")
        print(f"   Ground truth contains {comparison_result['total_gt']} names")
        print(f"   Successfully matched {comparison_result['matched']} names")
        print(f"   Precision: {comparison_result['precision']:.1%}")
        print(f"   Recall: {comparison_result['recall']:.1%}")
        print(f"   F1 Score: {comparison_result['f1_score']:.1%}")
        
        # Quality assessment
        if comparison_result['f1_score'] >= 0.8:
            print(f"   🎉 EXCELLENT performance!")
        elif comparison_result['f1_score'] >= 0.6:
            print(f"   ✅ GOOD performance!")
        elif comparison_result['f1_score'] >= 0.4:
            print(f"   ⚠️  MODERATE performance - needs improvement")
        else:
            print(f"   ❌ POOR performance - significant improvements needed")
            
    except Exception as e:
        print(f"❌ Error during comparison: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
