"""Security tests for the JortExtractor system."""
import unittest
import os
import tempfile
import logging
import cv2
import numpy as np
import pandas as pd
from pathlib import Path
from unittest.mock import patch, MagicMock

from jortextractor.core.pipeline import JortPDFProcessor
from tests.test_utils import create_temp_directory, cleanup_temp_directory

class TestSecurity(unittest.TestCase):
    """Security tests for the JortExtractor system."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()
        cls.test_data_dir = cls.temp_dir / "test_data"
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

        # Path to example PDFs
        cls.example_pdfs_dir = Path("ProjectContext/DataExamples")

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a logger for testing
        self.logger = logging.getLogger("test_security")
        self.logger.setLevel(logging.DEBUG)

        # Create a processor instance
        self.processor = JortPDFProcessor()

    def test_path_traversal_prevention(self):
        """Test prevention of path traversal attacks."""
        # Create a mock PDF path with path traversal attempt
        traversal_path = Path("../../../etc/passwd")

        # Process the PDF (should fail safely)
        result = self.processor.process_pdf(traversal_path)

        # Check that processing failed
        self.assertIn("error", result["status"])
        self.assertIn("File not found", result["status"])

        # Check that no data was extracted
        self.assertTrue(result["extracted_data"].empty)

    def test_invalid_input_handling(self):
        """Test handling of invalid inputs."""
        # Test with None
        result = self.processor.process_pdf(None)
        self.assertIn("error", result["status"])

        # Test with invalid path type
        result = self.processor.process_pdf(123)
        self.assertIn("error", result["status"])

        # Test with empty string
        result = self.processor.process_pdf("")
        self.assertIn("error", result["status"])

        # Test with directory instead of file
        result = self.processor.process_pdf(self.test_data_dir)
        self.assertIn("error", result["status"])

    def test_malformed_pdf_handling(self):
        """Test handling of malformed PDF files."""
        # Create a malformed PDF file (just random bytes)
        malformed_pdf_path = self.test_data_dir / "malformed.pdf"
        with open(malformed_pdf_path, "wb") as f:
            f.write(os.urandom(1024))

        # Process the malformed PDF (should fail safely)
        result = self.processor.process_pdf(malformed_pdf_path)

        # Check that processing failed
        self.assertIn("error", result["status"])

        # Check that no data was extracted
        self.assertTrue(result["extracted_data"].empty)

    def test_very_large_input_handling(self):
        """Test handling of very large inputs."""
        # Create a mock very large PDF path
        large_pdf_path = self.test_data_dir / "very_large.pdf"

        # Mock the convert_from_path function to simulate a very large PDF
        def mock_convert_large(*args, **kwargs):
            # Create a list of 1000 large images
            return [np.ones((10000, 10000, 3), dtype=np.uint8) for _ in range(1000)]

        # Patch the convert_from_path function
        with patch("jortextractor.pipeline.convert_from_path", side_effect=mock_convert_large):
            # Process the PDF (should handle the large input safely)
            try:
                result = self.processor.process_pdf(large_pdf_path)

                # If it completes, check that processing was successful or failed safely
                if result["status"] == "success":
                    self.assertIsInstance(result["extracted_data"], pd.DataFrame)
                else:
                    self.assertIn("error", result["status"])

            except MemoryError:
                # If a MemoryError occurs, that's acceptable
                # The important thing is that it doesn't crash the program
                pass
            except Exception as e:
                # Other exceptions should be caught and handled properly
                self.fail(f"Processing very large input raised unexpected exception: {e}")

    def test_malicious_image_handling(self):
        """Test handling of potentially malicious images."""
        # Create a mock malicious image (very large dimensions but small file size)
        malicious_img = np.ones((1, 1, 3), dtype=np.uint8)

        # Mock the image shape to be extremely large
        malicious_img_path = self.test_data_dir / "malicious.png"
        cv2.imwrite(str(malicious_img_path), malicious_img)

        # Mock the cv2.imread function to return an image with extreme dimensions
        def mock_imread(*args, **kwargs):
            img = np.ones((1, 1, 3), dtype=np.uint8)
            # Trick the system by reporting extreme dimensions
            img.shape = (1000000, 1000000, 3)
            return img

        # Patch the cv2.imread function
        with patch("cv2.imread", side_effect=mock_imread):
            # Mock the convert_from_path function to return our malicious image
            def mock_convert(*args, **kwargs):
                return [malicious_img]

            # Patch the convert_from_path function
            with patch("jortextractor.pipeline.convert_from_path", side_effect=mock_convert):
                # Process the PDF (should handle the malicious image safely)
                try:
                    result = self.processor.process_pdf(Path("malicious.pdf"))

                    # If it completes, check that processing failed safely
                    self.assertIn("error", result["status"])

                except MemoryError:
                    # If a MemoryError occurs, that's acceptable
                    # The important thing is that it doesn't crash the program
                    pass
                except Exception as e:
                    # Other exceptions should be caught and handled properly
                    self.fail(f"Processing malicious image raised unexpected exception: {e}")

    def test_input_validation(self):
        """Test validation of inputs."""
        # Test with valid inputs
        valid_path = next((p for p in self.example_pdfs_dir.glob("*.pdf")), None)
        if valid_path:
            result = self.processor.process_pdf(valid_path)
            self.assertEqual(result["status"], "success")

        # Test with invalid file extension
        invalid_ext_path = self.test_data_dir / "invalid.txt"
        with open(invalid_ext_path, "w") as f:
            f.write("This is not a PDF file")

        result = self.processor.process_pdf(invalid_ext_path)
        self.assertIn("error", result["status"])

        # Test with non-existent file
        non_existent_path = self.test_data_dir / "non_existent.pdf"
        result = self.processor.process_pdf(non_existent_path)
        self.assertIn("error", result["status"])

        # Test with invalid ground truth path
        if valid_path:
            invalid_ground_truth_path = self.test_data_dir / "invalid_ground_truth.txt"
            with open(invalid_ground_truth_path, "w") as f:
                f.write("This is not a CSV file")

            result = self.processor.process_pdf(valid_path, invalid_ground_truth_path)
            self.assertEqual(result["status"], "success")  # Should still process the PDF
            self.assertNotIn("comparison_stats", result)  # But not compare with ground truth

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory and all its contents
        cleanup_temp_directory(cls.temp_dir)

if __name__ == "__main__":
    unittest.main()
