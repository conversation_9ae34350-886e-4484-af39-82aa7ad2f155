"""Integration tests for ground truth comparison."""
import unittest
import os
import shutil
import tempfile
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import cv2
from pathlib import Path
from unittest.mock import patch, MagicMock
from pdf2image import convert_from_path
import logging

from jortextractor.core import JortPDFProcessor, normalize_text, optimize_for_name_ocr
from tests.test_utils import create_temp_directory, cleanup_temp_directory

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ground_truth_comparison_test")

class TestGroundTruthComparison(unittest.TestCase):
    """Integration tests for ground truth comparison."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()
        cls.test_data_dir = cls.temp_dir / "test_data"
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

        # Create a directory for test results
        cls.results_dir = Path("results/ground_truth")
        cls.results_dir.mkdir(parents=True, exist_ok=True)

        # Create directories for processing step images
        cls.original_pages_dir = cls.results_dir / "original_pages"
        cls.original_pages_dir.mkdir(parents=True, exist_ok=True)

        cls.detected_tables_dir = cls.results_dir / "detected_tables"
        cls.detected_tables_dir.mkdir(parents=True, exist_ok=True)

        cls.pre_ocr_tables_dir = cls.results_dir / "pre_ocr_tables"
        cls.pre_ocr_tables_dir.mkdir(parents=True, exist_ok=True)

        cls.pre_ocr_columns_dir = cls.results_dir / "pre_ocr_columns"
        cls.pre_ocr_columns_dir.mkdir(parents=True, exist_ok=True)

        # Path to example PDFs and ground truth files
        cls.example_pdfs_dir = Path("data/examples")

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a processor instance with debug output
        self.processor = JortPDFProcessor()

        # Configure the processor to save pre-OCR images
        self.processor.pre_ocr_tables_dir = self.pre_ocr_tables_dir
        self.processor.pre_ocr_columns_dir = self.pre_ocr_columns_dir
        self.processor.temp_dir = self.detected_tables_dir

    def test_ground_truth_comparison_type1(self):
        """Test comparison with ground truth for type1 format."""
        # Path to type1 example PDF and ground truth
        pdf_path = self.example_pdfs_dir / "type1-information-in-two-comulns-hidden-table-separator.pdf"
        ground_truth_path = self.example_pdfs_dir / "type1-information-in-two-comulns-hidden-table-separator-manually-extracted-column-1-and-2-of-6.csv"

        # Skip if files don't exist
        if not pdf_path.exists() or not ground_truth_path.exists():
            self.skipTest(f"Test files not found: {pdf_path} or {ground_truth_path}")

        # Save original pages
        self.save_original_pages(pdf_path, 'type1')

        # Process the PDF
        result = self.processor.process_pdf(pdf_path, ground_truth_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Check that data was extracted
        self.assertIsInstance(result['extracted_data'], pd.DataFrame)
        self.assertGreater(len(result['extracted_data']), 0)

        # Check comparison stats
        self.assertIn('comparison_stats', result)
        stats = result['comparison_stats']

        # Check recall (most important metric)
        self.assertGreaterEqual(stats['recall'], 0.8, "Recall should be at least 80%")

        # Check precision
        self.assertGreaterEqual(stats['precision'], 0.7, "Precision should be at least 70%")

        # Check F1 score
        self.assertGreaterEqual(stats['f1_score'], 0.75, "F1 score should be at least 75%")

        # Save results
        self.save_comparison_results(result, 'type1')

    def test_ground_truth_comparison_type2(self):
        """Test comparison with ground truth for type2 format."""
        # Path to type2 example PDF and ground truth
        pdf_path = self.example_pdfs_dir / "type2-information-in-multiple-columns.pdf"
        ground_truth_path = self.example_pdfs_dir / "type2-information-in-multiple-columns-manually-extracted-table-with-columns-1-and-2-and-3.csv"

        # Skip if files don't exist
        if not pdf_path.exists() or not ground_truth_path.exists():
            self.skipTest(f"Test files not found: {pdf_path} or {ground_truth_path}")

        # Save original pages
        self.save_original_pages(pdf_path, 'type2')

        # Process the PDF
        result = self.processor.process_pdf(pdf_path, ground_truth_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Check that data was extracted
        self.assertIsInstance(result['extracted_data'], pd.DataFrame)
        self.assertGreater(len(result['extracted_data']), 0)

        # Check comparison stats
        self.assertIn('comparison_stats', result)
        stats = result['comparison_stats']

        # Check recall (most important metric)
        self.assertGreaterEqual(stats['recall'], 0.8, "Recall should be at least 80%")

        # Check precision
        self.assertGreaterEqual(stats['precision'], 0.7, "Precision should be at least 70%")

        # Check F1 score
        self.assertGreaterEqual(stats['f1_score'], 0.75, "F1 score should be at least 75%")

        # Save results
        self.save_comparison_results(result, 'type2')

    def test_ground_truth_comparison_type3(self):
        """Test comparison with ground truth for type3 format."""
        # Path to type3 example PDF and ground truth
        pdf_path = self.example_pdfs_dir / "type3-information-in-one-column.pdf"
        ground_truth_path = self.example_pdfs_dir / "type3-information-in-one-column-manually-extracted-table-1-of-4.csv"

        # Skip if files don't exist
        if not pdf_path.exists() or not ground_truth_path.exists():
            self.skipTest(f"Test files not found: {pdf_path} or {ground_truth_path}")

        # Save original pages
        self.save_original_pages(pdf_path, 'type3')

        # Process the PDF
        result = self.processor.process_pdf(pdf_path, ground_truth_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Check that data was extracted
        self.assertIsInstance(result['extracted_data'], pd.DataFrame)
        self.assertGreater(len(result['extracted_data']), 0)

        # Check comparison stats
        self.assertIn('comparison_stats', result)
        stats = result['comparison_stats']

        # Check recall (most important metric)
        self.assertGreaterEqual(stats['recall'], 0.8, "Recall should be at least 80%")

        # Check precision
        self.assertGreaterEqual(stats['precision'], 0.7, "Precision should be at least 70%")

        # Check F1 score
        self.assertGreaterEqual(stats['f1_score'], 0.75, "F1 score should be at least 75%")

        # Save results
        self.save_comparison_results(result, 'type3')

    def test_ground_truth_comparison_type4(self):
        """Test comparison with ground truth for type4 format."""
        # Path to type4 example PDF and ground truth
        pdf_path = self.example_pdfs_dir / "type4-information-in-three-columns.pdf"
        ground_truth_path = self.example_pdfs_dir / "type4-information-in-three-columns-manually-extracted-all-columns.csv"

        # Skip if files don't exist
        if not pdf_path.exists() or not ground_truth_path.exists():
            self.skipTest(f"Test files not found: {pdf_path} or {ground_truth_path}")

        # Save original pages
        self.save_original_pages(pdf_path, 'type4')

        # Process the PDF
        result = self.processor.process_pdf(pdf_path, ground_truth_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Check that data was extracted
        self.assertIsInstance(result['extracted_data'], pd.DataFrame)
        self.assertGreater(len(result['extracted_data']), 0)

        # Check comparison stats
        self.assertIn('comparison_stats', result)
        stats = result['comparison_stats']

        # Check recall (most important metric)
        self.assertGreaterEqual(stats['recall'], 0.8, "Recall should be at least 80%")

        # Check precision
        self.assertGreaterEqual(stats['precision'], 0.7, "Precision should be at least 70%")

        # Check F1 score
        self.assertGreaterEqual(stats['f1_score'], 0.75, "F1 score should be at least 75%")

        # Save results
        self.save_comparison_results(result, 'type4')

    def save_original_pages(self, pdf_path, doc_type):
        """Save the original PDF pages as images.

        Args:
            pdf_path: Path to the PDF file
            doc_type: Document type (e.g., 'type1', 'type2')

        Returns:
            List of paths to the saved images
        """
        # Convert PDF to images
        images = convert_from_path(pdf_path, dpi=300)

        # Save each page as an image
        saved_paths = []
        for i, image in enumerate(images):
            # Convert PIL image to numpy array
            img_np = np.array(image)
            # Convert RGB to BGR (OpenCV format)
            img_cv = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)

            # Save the image
            output_path = self.original_pages_dir / f"{doc_type}_page_{i+1}.png"
            cv2.imwrite(str(output_path), img_cv)
            saved_paths.append(output_path)

            logger.info(f"Saved original page {i+1} to {output_path}")

        return saved_paths

    def save_comparison_results(self, result, doc_type):
        """Save comparison results to files."""
        # Extract data
        extracted_df = result['extracted_data']
        stats = result['comparison_stats']

        # Save extracted data to CSV
        extracted_csv_path = self.results_dir / f"{doc_type}_extracted_data.csv"
        extracted_df.to_csv(extracted_csv_path, index=False)

        # Save comparison stats to CSV
        stats_df = pd.DataFrame([stats])
        stats_csv_path = self.results_dir / f"{doc_type}_comparison_stats.csv"
        stats_df.to_csv(stats_csv_path, index=False)

        # Create a bar chart of comparison metrics
        plt.figure(figsize=(10, 6))
        metrics = ['precision', 'recall', 'f1_score']
        values = [stats[metric] for metric in metrics]
        plt.bar(metrics, values)
        plt.ylim(0, 1)
        plt.xlabel('Metric')
        plt.ylabel('Value')
        plt.title(f'Comparison Metrics for {doc_type}')
        plt.tight_layout()

        # Save the chart
        chart_path = self.results_dir / f"{doc_type}_comparison_metrics.png"
        plt.savefig(chart_path)
        plt.close()

        # Create a report with processing step images
        self.create_processing_steps_report(doc_type)

    def create_processing_steps_report(self, doc_type):
        """Create a report with images of the processing steps.

        Args:
            doc_type: Document type (e.g., 'type1', 'type2')
        """
        # Get all images for this document type
        original_pages = sorted(list(self.original_pages_dir.glob(f"{doc_type}_*.png")))
        detected_tables = sorted(list(self.detected_tables_dir.glob(f"page*_{doc_type}*.png")))
        pre_ocr_tables = sorted(list(self.pre_ocr_tables_dir.glob(f"page*_{doc_type}*.png")))
        pre_ocr_columns = sorted(list(self.pre_ocr_columns_dir.glob(f"page*_{doc_type}*.png")))

        # Create HTML report
        html_path = self.results_dir / f"{doc_type}_processing_steps.html"

        with open(html_path, 'w') as f:
            f.write(f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Processing Steps for {doc_type}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1, h2 {{ color: #333; }}
                    .step {{ margin-bottom: 30px; }}
                    .image-container {{ display: flex; flex-wrap: wrap; gap: 10px; }}
                    .image-card {{ border: 1px solid #ddd; padding: 10px; margin-bottom: 10px; }}
                    img {{ max-width: 100%; height: auto; max-height: 500px; }}
                </style>
            </head>
            <body>
                <h1>Processing Steps for {doc_type}</h1>

                <div class="step">
                    <h2>Step 1: Original Pages</h2>
                    <div class="image-container">
            """)

            # Add original pages
            for img_path in original_pages:
                rel_path = img_path.relative_to(self.results_dir)
                f.write(f"""
                        <div class="image-card">
                            <h3>{img_path.name}</h3>
                            <img src="../{rel_path}" alt="{img_path.name}">
                        </div>
                """)

            f.write("""
                    </div>
                </div>

                <div class="step">
                    <h2>Step 2: Detected Tables</h2>
                    <div class="image-container">
            """)

            # Add detected tables
            for img_path in detected_tables:
                rel_path = img_path.relative_to(self.results_dir)
                f.write(f"""
                        <div class="image-card">
                            <h3>{img_path.name}</h3>
                            <img src="../{rel_path}" alt="{img_path.name}">
                        </div>
                """)

            f.write("""
                    </div>
                </div>

                <div class="step">
                    <h2>Step 3: Pre-OCR Tables</h2>
                    <div class="image-container">
            """)

            # Add pre-OCR tables
            for img_path in pre_ocr_tables:
                rel_path = img_path.relative_to(self.results_dir)
                f.write(f"""
                        <div class="image-card">
                            <h3>{img_path.name}</h3>
                            <img src="../{rel_path}" alt="{img_path.name}">
                        </div>
                """)

            f.write("""
                    </div>
                </div>

                <div class="step">
                    <h2>Step 4: Pre-OCR Columns</h2>
                    <div class="image-container">
            """)

            # Add pre-OCR columns
            for img_path in pre_ocr_columns:
                rel_path = img_path.relative_to(self.results_dir)
                f.write(f"""
                        <div class="image-card">
                            <h3>{img_path.name}</h3>
                            <img src="../{rel_path}" alt="{img_path.name}">
                        </div>
                """)

            f.write("""
                    </div>
                </div>
            </body>
            </html>
            """)

        logger.info(f"Created processing steps report at {html_path}")

    def test_comparison_with_partial_ground_truth(self):
        """Test comparison with partial ground truth data."""
        # Create a mock extracted DataFrame
        extracted_data = pd.DataFrame({
            'text': [
                'MOHAMED BEN ALI',
                'FATIMA BENT AHMED',
                'ABDEL KARIM EL MANSOURI',
                'LEILA TRABELSI',
                'YOUSSEF OULD MOHAMED',
                'AMINA BINT IBRAHIM',
                'SAMIR AIT AHMED',
                'NADIA EPOUSE KARIM',
                'JAMAL ABDUL NASIR',
                'SAMIRA BENT MOHAMED'
            ]
        })

        # Create a mock ground truth CSV file with a subset of the names
        ground_truth_data = pd.DataFrame({
            0: [
                'MOHAMED BEN ALI',
                'FATIMA BENT AHMED',
                'ABDEL KARIM EL MANSOURI',
                'LEILA TRABELSI',
                'YOUSSEF OULD MOHAMED'
            ]
        })

        # Save the ground truth to a temporary CSV file
        ground_truth_path = self.test_data_dir / "partial_ground_truth.csv"
        ground_truth_data.to_csv(ground_truth_path, index=False, header=False)

        # Mock the _compare_with_ground_truth method to use our data
        with patch.object(self.processor, '_compare_with_ground_truth') as mock_compare:
            # Call the actual method with our data
            comparison_result = self.processor._compare_with_ground_truth(extracted_data, ground_truth_path)

            # Use the actual result
            mock_compare.return_value = comparison_result

            # Process with mocked comparison
            result = {
                'extracted_data': extracted_data,
                'status': 'success',
                **comparison_result
            }

            # Check comparison stats
            self.assertIn('comparison_stats', result)
            stats = result['comparison_stats']

            # All ground truth names should be found (recall = 1.0)
            self.assertEqual(stats['recall'], 1.0, "Recall should be 100% for partial ground truth")

            # But precision will be lower because we extracted more names than in the ground truth
            self.assertLess(stats['precision'], 1.0, "Precision should be less than 100% for partial ground truth")

            # Save results
            self.save_comparison_results(result, 'partial_ground_truth')

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory
        cleanup_temp_directory(cls.temp_dir)

        # Keep results directory for analysis

if __name__ == "__main__":
    unittest.main()
