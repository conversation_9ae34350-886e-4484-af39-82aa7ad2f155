"""Integration tests for the complete extraction pipeline."""
import unittest
import os
import shutil
import tempfile
import pandas as pd
import numpy as np
import cv2
from pathlib import Path
from jortextractor.core.pipeline import JortPDFProcessor
from pdf2image import convert_from_path

class TestPipelineIntegration(unittest.TestCase):
    """Integration tests for the complete extraction pipeline."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = Path(tempfile.mkdtemp())

        # Create a directory for test data
        cls.test_data_dir = Path("tests/test_data")
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

        # Path to example PDFs
        cls.example_pdfs_dir = Path("ProjectContext/DataExamples")

        # Create a processor instance with debug output
        cls.processor = JortPDFProcessor(temp_dir=cls.temp_dir)

    def test_type1_extraction(self):
        """Test extraction from type1 format PDF."""
        # Path to type1 example PDF
        pdf_path = self.example_pdfs_dir / "type1-information-in-two-comulns-hidden-table-separator.pdf"
        ground_truth_path = self.example_pdfs_dir / "type1-information-in-two-comulns-hidden-table-separator-manually-extracted-column-1-and-2-of-6.csv"

        # Skip if files don't exist
        if not pdf_path.exists() or not ground_truth_path.exists():
            self.skipTest(f"Test files not found: {pdf_path} or {ground_truth_path}")

        # Process the PDF
        result = self.processor.process_pdf(pdf_path, ground_truth_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Check that data was extracted
        self.assertIsInstance(result['extracted_data'], pd.DataFrame)
        self.assertGreater(len(result['extracted_data']), 0)

        # Check comparison stats
        self.assertIn('comparison_stats', result)
        stats = result['comparison_stats']

        # Check recall (most important metric)
        self.assertGreaterEqual(stats['recall'], 0.9, "Recall should be at least 90%")

        # Check precision
        self.assertGreaterEqual(stats['precision'], 0.8, "Precision should be at least 80%")

        # Check F1 score
        self.assertGreaterEqual(stats['f1_score'], 0.85, "F1 score should be at least 85%")

    def test_type2_extraction(self):
        """Test extraction from type2 format PDF."""
        # Path to type2 example PDF
        pdf_path = self.example_pdfs_dir / "type2-information-in-multiple-columns.pdf"
        ground_truth_path = self.example_pdfs_dir / "type2-information-in-multiple-columns-manually-extracted-table-with-columns-1-and-2-and-3.csv"

        # Skip if files don't exist
        if not pdf_path.exists() or not ground_truth_path.exists():
            self.skipTest(f"Test files not found: {pdf_path} or {ground_truth_path}")

        # Process the PDF
        result = self.processor.process_pdf(pdf_path, ground_truth_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Check that data was extracted
        self.assertIsInstance(result['extracted_data'], pd.DataFrame)
        self.assertGreater(len(result['extracted_data']), 0)

        # Check comparison stats
        self.assertIn('comparison_stats', result)
        stats = result['comparison_stats']

        # Check recall (most important metric)
        self.assertGreaterEqual(stats['recall'], 0.9, "Recall should be at least 90%")

        # Check precision
        self.assertGreaterEqual(stats['precision'], 0.8, "Precision should be at least 80%")

        # Check F1 score
        self.assertGreaterEqual(stats['f1_score'], 0.85, "F1 score should be at least 85%")

    def test_type3_extraction(self):
        """Test extraction from type3 format PDF."""
        # Path to type3 example PDF
        pdf_path = self.example_pdfs_dir / "type3-information-in-one-column.pdf"
        ground_truth_path = self.example_pdfs_dir / "type3-information-in-one-column-manually-extracted-table-1-of-4.csv"

        # Skip if files don't exist
        if not pdf_path.exists() or not ground_truth_path.exists():
            self.skipTest(f"Test files not found: {pdf_path} or {ground_truth_path}")

        # Process the PDF
        result = self.processor.process_pdf(pdf_path, ground_truth_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Check that data was extracted
        self.assertIsInstance(result['extracted_data'], pd.DataFrame)
        self.assertGreater(len(result['extracted_data']), 0)

        # Check comparison stats
        self.assertIn('comparison_stats', result)
        stats = result['comparison_stats']

        # Check recall (most important metric)
        self.assertGreaterEqual(stats['recall'], 0.85, "Recall should be at least 85%")

        # Check precision
        self.assertGreaterEqual(stats['precision'], 0.8, "Precision should be at least 80%")

        # Check F1 score
        self.assertGreaterEqual(stats['f1_score'], 0.8, "F1 score should be at least 80%")

    def test_document_type_detection(self):
        """Test automatic document type detection."""
        # Test with all example PDFs
        pdf_files = [
            ("type1-information-in-two-comulns-hidden-table-separator.pdf", "type1"),
            ("type2-information-in-multiple-columns.pdf", "type2"),
            ("type3-information-in-one-column.pdf", "type3")
        ]

        for filename, expected_type in pdf_files:
            pdf_path = self.example_pdfs_dir / filename

            # Skip if file doesn't exist
            if not pdf_path.exists():
                self.skipTest(f"Test file not found: {pdf_path}")
                continue

            # Convert first page to image
            images = convert_from_path(pdf_path, first_page=1, last_page=1)
            if not images:
                self.skipTest(f"Could not convert PDF to image: {pdf_path}")
                continue

            # Get the first page image
            first_page_img = np.array(images[0])
            first_page_cv = cv2.cvtColor(first_page_img, cv2.COLOR_RGB2BGR)

            # Detect document type
            document_type = self.processor.document_classifier.classify_document(
                first_page_cv,
                filename=str(pdf_path)
            )

            # Check that the detected type matches the expected type
            self.assertEqual(document_type, expected_type,
                            f"Failed to detect correct document type for {filename}")

    def test_empty_page_handling(self):
        """Test handling of PDFs with empty pages."""
        # Create a temporary PDF with an empty page
        empty_pdf_path = self.test_data_dir / "empty_page.pdf"

        # Skip this test if we can't create test files
        if not self.test_data_dir.exists():
            self.skipTest("Test data directory not available")

        # Process the PDF (should not raise exceptions)
        try:
            result = self.processor.process_pdf(empty_pdf_path)
            # Should return empty DataFrame
            self.assertEqual(len(result['extracted_data']), 0)
        except Exception as e:
            self.fail(f"Processing empty PDF raised exception: {e}")

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory
        shutil.rmtree(cls.temp_dir)

        # Try to remove test data directory (will only succeed if empty)
        try:
            cls.test_data_dir.rmdir()
        except:
            pass

if __name__ == "__main__":
    unittest.main()
