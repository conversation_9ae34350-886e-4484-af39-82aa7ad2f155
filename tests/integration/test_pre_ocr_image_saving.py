"""Integration tests for pre-OCR image saving."""
import unittest
import os
import shutil
import tempfile
import pandas as pd
import numpy as np
import cv2
from pathlib import Path
from unittest.mock import patch, MagicMock

from jortextractor.core.pipeline import JortPDFProcessor
from tests.test_utils import create_temp_directory, cleanup_temp_directory

class TestPreOCRImageSaving(unittest.TestCase):
    """Integration tests for pre-OCR image saving."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()
        cls.test_data_dir = cls.temp_dir / "test_data"
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

        # Create a directory for pre-OCR images
        cls.pre_ocr_dir = cls.temp_dir / "pre_ocr_images"
        cls.pre_ocr_dir.mkdir(parents=True, exist_ok=True)

        # Path to example PDFs
        cls.example_pdfs_dir = Path("ProjectContext/DataExamples")

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a config with pre-OCR image saving enabled
        self.config = {
            'save_pre_ocr_images': True,
            'pre_ocr_tables_dir': str(self.pre_ocr_dir / "tables"),
            'pre_ocr_columns_dir': str(self.pre_ocr_dir / "columns")
        }

        # Create subdirectories for tables and columns
        (self.pre_ocr_dir / "tables").mkdir(parents=True, exist_ok=True)
        (self.pre_ocr_dir / "columns").mkdir(parents=True, exist_ok=True)

        # Create a processor with pre-OCR image saving enabled
        with patch('jortextractor.pipeline.load_config', return_value=self.config):
            self.processor = JortPDFProcessor()

    def _check_pre_ocr_images(self):
        """Helper method to check pre-OCR images in subdirectories."""
        # Check that pre-OCR images were saved in either tables or columns directory
        pre_ocr_table_images = list((self.pre_ocr_dir / "tables").glob("*.png"))
        pre_ocr_column_images = list((self.pre_ocr_dir / "columns").glob("*.png"))
        pre_ocr_images = pre_ocr_table_images + pre_ocr_column_images
        self.assertGreater(len(pre_ocr_images), 0, "No pre-OCR images were saved")

        # Check that the images are valid
        for image_path in pre_ocr_images:
            img = cv2.imread(str(image_path))
            self.assertIsNotNone(img, f"Failed to read image: {image_path}")
            self.assertGreater(img.shape[0], 0, f"Image has zero height: {image_path}")
            self.assertGreater(img.shape[1], 0, f"Image has zero width: {image_path}")

        # Clean up pre-OCR images
        for image_path in pre_ocr_images:
            image_path.unlink()

        return pre_ocr_images

    def test_pre_ocr_image_saving_type1(self):
        """Test pre-OCR image saving for type1 format."""
        # Path to type1 example PDF
        pdf_path = self.example_pdfs_dir / "type1-information-in-two-comulns-hidden-table-separator.pdf"

        # Skip if file doesn't exist
        if not pdf_path.exists():
            self.skipTest(f"Test file not found: {pdf_path}")

        # Process the PDF
        result = self.processor.process_pdf(pdf_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Check pre-OCR images
        self._check_pre_ocr_images()

    def test_pre_ocr_image_saving_type2(self):
        """Test pre-OCR image saving for type2 format."""
        # Path to type2 example PDF
        pdf_path = self.example_pdfs_dir / "type2-information-in-multiple-columns.pdf"

        # Skip if file doesn't exist
        if not pdf_path.exists():
            self.skipTest(f"Test file not found: {pdf_path}")

        # Process the PDF
        result = self.processor.process_pdf(pdf_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Check pre-OCR images
        self._check_pre_ocr_images()

    def test_pre_ocr_image_saving_type3(self):
        """Test pre-OCR image saving for type3 format."""
        # Path to type3 example PDF
        pdf_path = self.example_pdfs_dir / "type3-information-in-one-column.pdf"

        # Skip if file doesn't exist
        if not pdf_path.exists():
            self.skipTest(f"Test file not found: {pdf_path}")

        # Process the PDF
        result = self.processor.process_pdf(pdf_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Check pre-OCR images
        self._check_pre_ocr_images()

    def test_pre_ocr_image_saving_type4(self):
        """Test pre-OCR image saving for type4 format."""
        # Path to type4 example PDF
        pdf_path = self.example_pdfs_dir / "type4-information-in-three-columns.pdf"

        # Skip if file doesn't exist
        if not pdf_path.exists():
            self.skipTest(f"Test file not found: {pdf_path}")

        # Process the PDF
        result = self.processor.process_pdf(pdf_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Check pre-OCR images
        self._check_pre_ocr_images()

    def test_pre_ocr_image_saving_disabled(self):
        """Test that pre-OCR images are not saved when disabled."""
        # Create a config with pre-OCR image saving disabled
        config = {
            'save_pre_ocr_images': False
        }

        # Create a processor with pre-OCR image saving disabled
        with patch('jortextractor.pipeline.load_config', return_value=config):
            processor = JortPDFProcessor()

        # Path to example PDF
        pdf_path = next((p for p in self.example_pdfs_dir.glob("*.pdf")), None)

        # Skip if no files found
        if not pdf_path:
            self.skipTest("No PDF files found for testing")

        # Process the PDF
        result = processor.process_pdf(pdf_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Check that no pre-OCR images were saved in either subdirectory
        pre_ocr_table_images = list((self.pre_ocr_dir / "tables").glob("*.png"))
        pre_ocr_column_images = list((self.pre_ocr_dir / "columns").glob("*.png"))
        pre_ocr_images = pre_ocr_table_images + pre_ocr_column_images
        self.assertEqual(len(pre_ocr_images), 0, "Pre-OCR images were saved when disabled")

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory and all its contents
        cleanup_temp_directory(cls.temp_dir)

if __name__ == "__main__":
    unittest.main()
