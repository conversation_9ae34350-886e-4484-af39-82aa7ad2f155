"""Integration tests for comparing our OCR with OCR.space API."""
import unittest
import os
import json
import logging
import tempfile
import time
from pathlib import Path
import cv2
import numpy as np
import pandas as pd
import requests
import pytesseract
from unittest.mock import patch, MagicMock

from jortextractor.core.pipeline import JortPDFProcessor
from jortextractor.core.image_processor import optimize_for_name_ocr
from jortextractor.core.name_validator import normalize_text
from jortextractor.benchmarks.ocr_space_client import OCRSpaceClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ocr_comparison_test")

class TestOCRComparison(unittest.TestCase):
    """Test case for comparing our OCR with OCR.space API."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = Path(tempfile.mkdtemp())

        # Create a directory for test data
        cls.test_data_dir = Path("tests/test_data")
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

        # Path to example PDFs
        cls.example_pdfs_dir = Path("ProjectContext/DataExamples")

        # Create a processor instance
        cls.processor = JortPDFProcessor()

        # Create OCR.space client
        cls.ocr_space_client = OCRSpaceClient()

        # Create directories for pre-OCR images
        cls.pre_ocr_tables_dir = cls.temp_dir / "pre-ocr-tables"
        cls.pre_ocr_tables_dir.mkdir(exist_ok=True)
        cls.pre_ocr_columns_dir = cls.temp_dir / "pre-ocr-columns"
        cls.pre_ocr_columns_dir.mkdir(exist_ok=True)

        # Set up the processor to save pre-OCR images
        cls.processor.pre_ocr_tables_dir = cls.pre_ocr_tables_dir
        cls.processor.pre_ocr_columns_dir = cls.pre_ocr_columns_dir

    @classmethod
    def tearDownClass(cls):
        """Clean up after all tests have run."""
        # Remove temporary directory
        import shutil
        shutil.rmtree(cls.temp_dir)

    def compare_ocr_results(self, our_texts, ocr_space_texts):
        """Compare OCR results from our system and OCR.space.

        Args:
            our_texts: List of text strings from our OCR
            ocr_space_texts: List of text strings from OCR.space

        Returns:
            Dictionary with comparison metrics
        """
        # Normalize all texts for comparison
        our_texts_norm = [normalize_text(text) for text in our_texts]
        ocr_space_texts_norm = [normalize_text(text) for text in ocr_space_texts]

        # Find matches
        matched = 0
        for text in our_texts_norm:
            if text in ocr_space_texts_norm:
                matched += 1

        # Calculate metrics
        total_ours = len(our_texts)
        total_ocr_space = len(ocr_space_texts)

        precision = matched / total_ours if total_ours > 0 else 0
        recall = matched / total_ocr_space if total_ocr_space > 0 else 0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

        return {
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'total_ours': total_ours,
            'total_ocr_space': total_ocr_space,
            'matched': matched
        }

    def test_ocr_comparison_with_pre_ocr_images(self):
        """Test OCR comparison using pre-OCR images."""
        # Process a sample PDF to generate pre-OCR images
        pdf_path = next(self.example_pdfs_dir.glob("*.pdf"))
        ground_truth_path = next(self.example_pdfs_dir.glob(f"{pdf_path.stem}*.csv"))

        # Process the PDF to generate pre-OCR images
        result = self.processor.process_pdf(pdf_path, ground_truth_path)

        # Check that processing was successful
        self.assertEqual(result['status'], 'success')

        # Get all pre-OCR images
        pre_ocr_images = list(self.pre_ocr_tables_dir.glob("*.png"))

        # Ensure we have some pre-OCR images
        self.assertGreater(len(pre_ocr_images), 0)

        # Compare OCR results for each pre-OCR image
        comparison_results = []

        for img_path in pre_ocr_images[:3]:  # Limit to first 3 images to avoid API rate limits
            logger.info(f"Comparing OCR for image: {img_path}")

            # Get OCR.space result
            ocr_space_result = self.ocr_space_client.process_file(img_path)
            ocr_space_texts = self.ocr_space_client.extract_text(ocr_space_result)

            # Get our OCR result
            img = cv2.imread(str(img_path))
            # Apply OCR directly using pytesseract
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            optimized = optimize_for_name_ocr(gray)
            custom_config = f"--psm 6 --oem 3 -l eng+fra --dpi 300"
            text = pytesseract.image_to_string(optimized, config=custom_config).strip()
            our_texts = [line.strip() for line in text.split('\n') if line.strip()]

            # Compare results
            comparison = self.compare_ocr_results(our_texts, ocr_space_texts)
            comparison['image_path'] = str(img_path)
            comparison_results.append(comparison)

            # Log comparison
            logger.info(f"OCR Comparison for {img_path.name}:")
            logger.info(f"  Precision: {comparison['precision']:.2f}")
            logger.info(f"  Recall: {comparison['recall']:.2f}")
            logger.info(f"  F1 Score: {comparison['f1_score']:.2f}")
            logger.info(f"  Our texts: {len(our_texts)}, OCR.space texts: {len(ocr_space_texts)}, Matched: {comparison['matched']}")

            # Wait to avoid hitting API rate limits
            time.sleep(1)

        # Save comparison results
        results_df = pd.DataFrame(comparison_results)
        results_path = self.temp_dir / "ocr_comparison_results.csv"
        results_df.to_csv(results_path, index=False)

        logger.info(f"Saved comparison results to {results_path}")

        # Calculate average metrics
        avg_precision = results_df['precision'].mean()
        avg_recall = results_df['recall'].mean()
        avg_f1 = results_df['f1_score'].mean()

        logger.info(f"Average Precision: {avg_precision:.2f}")
        logger.info(f"Average Recall: {avg_recall:.2f}")
        logger.info(f"Average F1 Score: {avg_f1:.2f}")

        # We don't assert specific values here as this is a comparison test
        # The purpose is to gather data on how our OCR compares to OCR.space
