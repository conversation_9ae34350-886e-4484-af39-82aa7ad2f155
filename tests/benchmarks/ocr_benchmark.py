"""
OCR benchmarking module for evaluating OCR performance.

This module provides tools for benchmarking our OCR implementation against
ground truth data and optionally against OCR.space API for comparison purposes.

IMPORTANT: The OCR.space API is used ONLY for benchmarking and comparison.
It is NEVER used as an alternative to our own OCR implementation in the main
processing pipeline. The purpose of this comparison is solely to identify areas
for improvement in our own OCR implementation.
"""
import os
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Tuple

import cv2
import numpy as np
import pandas as pd
import pytesseract

from jortextractor.core.image_processor import optimize_for_name_ocr
from jortextractor.core.name_validator import normalize_text
from tests.benchmarks.ocr_space_client import OCRSpaceClient

logger = logging.getLogger(__name__)


def compare_with_ground_truth(ocr_texts: List[Dict], ground_truth_texts: List[str]) -> Dict[str, Any]:
    """Compare OCR results with ground truth.

    This is a general utility function that can be used to compare any OCR results
    (from our OCR or OCR.space) with ground truth data.

    Args:
        ocr_texts: List of dictionaries with 'text' and 'identifier' keys from OCR
        ground_truth_texts: List of ground truth text strings

    Returns:
        Dictionary with comparison metrics
    """
    # Find matches
    matched = 0
    matched_texts = []

    for gt_text in ground_truth_texts:
        # Extract just the text from OCR results for comparison
        ocr_text_values = [item.get('text', '') for item in ocr_texts]
        if gt_text in ocr_text_values:
            matched += 1
            matched_texts.append(gt_text)

    # Calculate metrics
    total_ocr = len(ocr_texts)
    total_gt = len(ground_truth_texts)

    precision = matched / total_ocr if total_ocr > 0 else 0
    recall = matched / total_gt if total_gt > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

    return {
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'total_ocr': total_ocr,
        'total_gt': total_gt,
        'matched': matched,
        'matched_texts': matched_texts,
        'ocr_texts': ocr_texts
    }


class OCRBenchmark:
    """Class for benchmarking OCR performance.

    This class provides methods for evaluating our OCR implementation against
    ground truth data and optionally against OCR.space API for comparison purposes.

    WARNING: The OCR.space API is NEVER used as an alternative to our own OCR implementation.
    It is used solely for benchmarking and comparison to identify areas for improvement.
    """

    def __init__(self,
                 output_dir: Union[str, Path] = "ocr_benchmark_results",
                 compare_with_ocr_space: bool = False,
                 ocr_space_api_key: str = "K888957"):
        """Initialize the OCR benchmark.

        Args:
            output_dir: Directory to store benchmark results
            compare_with_ocr_space: Whether to compare with OCR.space API
            ocr_space_api_key: OCR.space API key
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.compare_with_ocr_space = compare_with_ocr_space

        # Create OCR.space client if needed
        if compare_with_ocr_space:
            self.ocr_space_client = OCRSpaceClient(api_key=ocr_space_api_key)
            # Create directory for OCR.space comparison results
            self.ocr_space_dir = self.output_dir / "ocr_space_comparison"
            self.ocr_space_dir.mkdir(parents=True, exist_ok=True)

            logger.info("OCR.space comparison enabled (for benchmarking only)")
            logger.info("WARNING: OCR.space is NEVER used as an alternative to our own OCR")

    def extract_text_with_our_ocr(self, image_path: Union[str, Path]) -> List[str]:
        """Extract text from an image using our OCR implementation.

        Args:
            image_path: Path to the image

        Returns:
            List of extracted text strings
        """
        try:
            # Read image
            img = cv2.imread(str(image_path))
            if img is None:
                logger.error(f"Failed to read image: {image_path}")
                return []

            # Convert to grayscale if needed
            if len(img.shape) == 3:
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                gray = img.copy()

            # Optimize image for OCR
            optimized = optimize_for_name_ocr(gray)

            # Apply OCR
            custom_config = f"--psm 6 --oem 3 -l eng+fra --dpi 300"
            text = pytesseract.image_to_string(optimized, config=custom_config).strip()

            # Split by lines and filter out empty lines
            lines = [line.strip() for line in text.split('\n') if line.strip()]

            return lines
        except Exception as e:
            logger.error(f"Error extracting text with our OCR: {e}")
            return []

    def extract_text_with_ocr_space(self, image_path: Union[str, Path]) -> List[Dict]:
        """Extract text from an image using OCR.space API (for benchmarking only).

        This method is used solely for benchmarking and comparison. The OCR.space API
        is NEVER used as an alternative to our own OCR implementation in the main
        processing pipeline.

        Args:
            image_path: Path to the image

        Returns:
            List of dictionaries with extracted text and identifiers
        """
        if not self.compare_with_ocr_space:
            logger.warning("OCR.space comparison is disabled")
            return []

        try:
            # Process image with OCR.space
            ocr_space_result = self.ocr_space_client.process_file(image_path)

            # Extract text
            ocr_space_texts = self.ocr_space_client.extract_text(ocr_space_result)

            return ocr_space_texts
        except Exception as e:
            logger.error(f"Error extracting text with OCR.space: {e}")
            return []

    def compare_ocr_results(self, our_texts: List[str], ocr_space_texts: List[Dict]) -> Dict[str, Any]:
        """Compare OCR results from our system and OCR.space.

        Args:
            our_texts: List of text strings from our OCR
            ocr_space_texts: List of dictionaries with text and identifier from OCR.space

        Returns:
            Dictionary with comparison metrics
        """
        # Normalize all texts for comparison
        our_texts_norm = [normalize_text(text) for text in our_texts]
        ocr_space_texts_norm = [normalize_text(text.get('text', '')) for text in ocr_space_texts]

        # Find matches
        matched = 0
        matched_texts = []

        for i, text in enumerate(our_texts_norm):
            if text in ocr_space_texts_norm:
                matched += 1
                matched_texts.append(our_texts[i])

        # Calculate metrics
        total_ours = len(our_texts)
        total_ocr_space = len(ocr_space_texts)

        precision = matched / total_ours if total_ours > 0 else 0
        recall = matched / total_ocr_space if total_ocr_space > 0 else 0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

        return {
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'total_ours': total_ours,
            'total_ocr_space': total_ocr_space,
            'matched': matched,
            'matched_texts': matched_texts
        }

    def benchmark_image(self, image_path: Union[str, Path]) -> Dict[str, Any]:
        """Benchmark OCR performance on a single image.

        Args:
            image_path: Path to the image

        Returns:
            Dictionary with benchmark results
        """
        image_path = Path(image_path)
        logger.info(f"Benchmarking OCR for image: {image_path}")

        # Extract text with our OCR
        our_texts = self.extract_text_with_our_ocr(image_path)

        result = {
            'image_path': str(image_path),
            'our_texts': our_texts,
            'total_ours': len(our_texts)
        }

        # Compare with OCR.space if enabled
        if self.compare_with_ocr_space:
            # Extract text with OCR.space
            ocr_space_texts = self.extract_text_with_ocr_space(image_path)

            # Compare results
            comparison = self.compare_ocr_results(our_texts, ocr_space_texts)

            # Add OCR.space results to result
            result.update({
                'ocr_space_texts': ocr_space_texts,
                'total_ocr_space': len(ocr_space_texts),
                'precision': comparison['precision'],
                'recall': comparison['recall'],
                'f1_score': comparison['f1_score'],
                'matched': comparison['matched'],
                'matched_texts': comparison['matched_texts']
            })

            # Log comparison
            logger.info(f"OCR Comparison for {image_path.name}:")
            logger.info(f"  Precision: {comparison['precision']:.2f}")
            logger.info(f"  Recall: {comparison['recall']:.2f}")
            logger.info(f"  F1 Score: {comparison['f1_score']:.2f}")
            logger.info(f"  Our texts: {len(our_texts)}, OCR.space texts: {len(ocr_space_texts)}, Matched: {comparison['matched']}")

        # Save result
        self._save_benchmark_result(result)

        return result

    def benchmark_images(self, image_paths: List[Union[str, Path]], limit: Optional[int] = None,
                       generate_html: bool = True) -> List[Dict[str, Any]]:
        """Benchmark OCR performance on multiple images.

        Args:
            image_paths: List of paths to images
            limit: Maximum number of images to process
            generate_html: Whether to generate an HTML report

        Returns:
            List of benchmark result dictionaries
        """
        # Limit the number of images if specified
        if limit:
            image_paths = image_paths[:limit]

        logger.info(f"Benchmarking OCR for {len(image_paths)} images")

        # Benchmark each image
        results = []
        for img_path in image_paths:
            result = self.benchmark_image(img_path)
            results.append(result)

            # Wait to avoid hitting API rate limits if using OCR.space
            if self.compare_with_ocr_space:
                time.sleep(1)

        # Generate summary report
        self._generate_summary_report(results)

        # Generate HTML report if requested
        if generate_html and self.compare_with_ocr_space:
            self._generate_html_report(results)

        return results

    def _save_benchmark_result(self, result: Dict[str, Any]) -> None:
        """Save benchmark result to a JSON file.

        Args:
            result: Benchmark result dictionary
        """
        # Create a filename based on the image path
        image_path = Path(result['image_path'])
        result_filename = f"{image_path.stem}_benchmark.json"
        result_path = self.output_dir / result_filename

        # Save the result
        with open(result_path, 'w') as f:
            json.dump(result, f, indent=2)

        logger.info(f"Benchmark result saved to {result_path}")

    def _generate_summary_report(self, results: List[Dict[str, Any]]) -> None:
        """Generate a summary report of benchmark results.

        Args:
            results: List of benchmark result dictionaries
        """
        if not results:
            logger.warning("No results to generate summary report")
            return

        # Calculate averages
        total_our_texts = sum(r['total_ours'] for r in results)
        total_ocr_space_texts = sum(r.get('total_ocr_space', 0) for r in results)
        total_matched = sum(r.get('matched', 0) for r in results)

        avg_precision = sum(r.get('precision', 0) for r in results) / len(results) if results else 0
        avg_recall = sum(r.get('recall', 0) for r in results) / len(results) if results else 0
        avg_f1 = sum(r.get('f1_score', 0) for r in results) / len(results) if results else 0

        # Create summary
        summary = {
            'total_images': len(results),
            'total_our_texts': total_our_texts,
            'total_ocr_space_texts': total_ocr_space_texts,
            'total_matched': total_matched,
            'avg_precision': avg_precision,
            'avg_recall': avg_recall,
            'avg_f1': avg_f1
        }

        # Save summary
        summary_path = self.output_dir / "summary.json"
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)

        logger.info(f"Summary report saved to {summary_path}")
        logger.info(f"Summary: {len(results)} images, Precision: {avg_precision:.2f}, Recall: {avg_recall:.2f}, F1: {avg_f1:.2f}")

    def _get_metric_class(self, value: float) -> str:
        """Get CSS class for a metric value.

        Args:
            value: Metric value

        Returns:
            CSS class name
        """
        if value >= 0.7:
            return "good"
        elif value >= 0.4:
            return "medium"
        else:
            return "poor"

    def _generate_html_report(self, results: List[Dict[str, Any]]) -> None:
        """Generate an HTML report with detailed comparison.

        Args:
            results: List of benchmark result dictionaries
        """
        if not self.compare_with_ocr_space:
            return

        # Create images directory for the report
        images_dir = self.output_dir / "report_images"
        images_dir.mkdir(exist_ok=True)

        # Copy and prepare images for the report
        for result in results:
            image_path = Path(result['image_path'])
            if image_path.exists():
                # Copy the image to the report directory
                import shutil
                dest_path = images_dir / image_path.name
                shutil.copy2(image_path, dest_path)

                # Add the relative path to the result
                result['report_image_path'] = f"report_images/{image_path.name}"

        report_path = self.output_dir / "ocr_comparison_report.html"

        # Generate HTML report with comparison results
        # This is a simplified version - the full implementation would generate
        # a complete HTML report with detailed comparison tables and visualizations

        with open(report_path, 'w') as f:
            f.write(f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>OCR Comparison Report</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .warning {{ background-color: #fff3cd; padding: 10px; border-left: 5px solid #ffc107; margin-bottom: 20px; }}
                    .summary {{ background-color: #e9ecef; padding: 15px; margin-bottom: 20px; border-radius: 5px; }}
                </style>
            </head>
            <body>
                <h1>OCR Comparison Report</h1>
                <div class="warning">
                    <strong>IMPORTANT:</strong> OCR.space is used for BENCHMARKING ONLY, never as an alternative to our own OCR implementation.
                    It is used solely for comparison to identify areas for improvement.
                </div>
                <div class="summary">
                    <h2>Summary</h2>
                    <p>Total images processed: {len(results)}</p>
                </div>
                <p>See summary.json for detailed metrics.</p>
            </body>
            </html>
            """)

        logger.info(f"HTML report saved to {report_path}")
