"""Benchmarking tools for testing JortExtractor OCR performance.

This package contains tools for benchmarking and comparing OCR performance.
These tools are for testing purposes only and are not part of the main application.
"""

from tests.benchmarks.ocr_benchmark import OCRBenchmark, compare_with_ground_truth
from tests.benchmarks.ocr_space_client import OCRSpaceClient

# Create a simple function to benchmark OCR
def benchmark_ocr(image_path, output_dir="ocr_benchmark_results", compare_with_ocr_space=False):
    """Benchmark OCR performance on an image."""
    benchmark = OCRBenchmark(output_dir=output_dir, compare_with_ocr_space=compare_with_ocr_space)
    return benchmark.benchmark_image(image_path)

__all__ = [
    "benchmark_ocr",
    "OCRBenchmark",
    "OCRSpaceClient",
    "compare_with_ground_truth",
]
