#!/usr/bin/env python3
"""
Test script for OCR benchmarking.

This script demonstrates how to use the OCR benchmarking tools
for testing purposes only.
"""
import logging
import sys
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

# Import the benchmarking tools from the tests package
from tests.benchmarks import OCRSpaceClient, OCRBenchmark, compare_with_ground_truth

def test_ocr_space_client():
    """Test the OCR.space client."""
    print("Testing OCR.space client...")
    
    # Create OCR.space client
    client = OCRSpaceClient()
    
    # Find a test image
    test_images = list(Path('pre-ocr-tables').glob('*_inverted_lined.png'))
    if not test_images:
        print("No test images found in pre-ocr-tables directory")
        return
    
    test_image = test_images[0]
    print(f"Processing file: {test_image}")
    print(f"File exists: {test_image.exists()}")
    print(f"File size: {test_image.stat().st_size / 1024:.1f} KB")
    
    # Process the file
    result = client.process_file(test_image)
    
    # Print the result
    if result:
        print("OCR.space result:")
        if 'ParsedResults' in result and result['ParsedResults']:
            for i, parsed_result in enumerate(result['ParsedResults']):
                if 'ParsedText' in parsed_result:
                    print(f"Result {i+1} text preview: {parsed_result['ParsedText'][:100]}...")
        else:
            print("No parsed results")
    else:
        print("No result returned")
    
    # Extract text
    extracted_data = client.extract_text(result)
    print(f"Extracted {len(extracted_data)} text items")
    
    # Print the first few extracted items
    for i, item in enumerate(extracted_data[:5]):
        print(f"Item {i+1}: {item.get('text', '')} - {item.get('identifier', '')}")
    
    print("OCR.space client test completed")
    return extracted_data

def test_compare_with_ground_truth(ocr_texts):
    """Test the compare_with_ground_truth function."""
    print("\nTesting compare_with_ground_truth function...")
    
    # Create some sample ground truth data
    ground_truth = [
        "Sample Name 1",
        "Sample Name 2",
        "Sample Name 3"
    ]
    
    # Compare with OCR results
    comparison = compare_with_ground_truth(ocr_texts, ground_truth)
    
    # Print the comparison results
    print(f"Comparison results:")
    print(f"  Precision: {comparison['precision']:.2f}")
    print(f"  Recall: {comparison['recall']:.2f}")
    print(f"  F1 Score: {comparison['f1_score']:.2f}")
    print(f"  Total OCR: {comparison['total_ocr']}, Total GT: {comparison['total_gt']}, Matched: {comparison['matched']}")
    
    print("compare_with_ground_truth test completed")

def test_ocr_benchmark():
    """Test the OCR benchmark."""
    print("\nTesting OCR benchmark...")
    
    # Create OCR benchmark
    benchmark = OCRBenchmark(output_dir="ocr_benchmark_results", compare_with_ocr_space=True)
    
    # Find test images
    test_images = list(Path('pre-ocr-tables').glob('*_inverted_lined.png'))
    if not test_images:
        print("No test images found in pre-ocr-tables directory")
        return
    
    # Limit to 2 images for testing
    test_images = test_images[:2]
    
    # Benchmark the images
    results = benchmark.benchmark_images(test_images, generate_html=True)
    
    # Print the results
    print(f"Benchmarked {len(results)} images")
    for i, result in enumerate(results):
        print(f"Image {i+1}: {Path(result['image_path']).name}")
        print(f"  Our texts: {result['total_ours']}")
        if 'total_ocr_space' in result:
            print(f"  OCR.space texts: {result['total_ocr_space']}")
            print(f"  Matched: {result['matched']}")
            print(f"  Precision: {result['precision']:.2f}")
            print(f"  Recall: {result['recall']:.2f}")
            print(f"  F1 Score: {result['f1_score']:.2f}")
    
    print("OCR benchmark test completed")

if __name__ == "__main__":
    # Test the OCR.space client
    extracted_data = test_ocr_space_client()
    
    # Test the compare_with_ground_truth function
    if extracted_data:
        test_compare_with_ground_truth(extracted_data)
    
    # Test the OCR benchmark
    test_ocr_benchmark()
