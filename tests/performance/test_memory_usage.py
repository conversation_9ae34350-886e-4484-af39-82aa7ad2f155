"""Memory usage tests for the JortExtractor system."""
import unittest
import os
import time
import psutil
import tempfile
import gc
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor
from jortextractor.core.pipeline import JortPDFProcessor
from tests.test_utils import monitor_memory_usage, create_temp_directory, cleanup_temp_directory

class TestMemoryUsage(unittest.TestCase):
    """Memory usage tests for the JortExtractor system."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()

        # Create a directory for test results
        cls.results_dir = Path("tests/memory_results")
        cls.results_dir.mkdir(parents=True, exist_ok=True)

        # Path to example PDFs
        cls.example_pdfs_dir = Path("ProjectContext/DataExamples")

    def setUp(self):
        """Set up test fixtures for each test."""
        # Force garbage collection before each test
        gc.collect()

        # Create a processor instance
        self.processor = JortPDFProcessor()

    def test_memory_usage_during_processing(self):
        """Test memory usage during PDF processing."""
        # Get a sample PDF
        pdf_files = list(self.example_pdfs_dir.glob("*.pdf"))

        # Skip if no files found
        if not pdf_files:
            self.skipTest("No PDF files found for testing")

        pdf_path = pdf_files[0]

        # Get the current process
        process = psutil.Process(os.getpid())

        # Baseline memory usage
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Memory usage measurements
        memory_usage = [('Baseline', baseline_memory)]

        # Process the PDF and monitor memory usage
        try:
            # Start monitoring in a separate thread
            monitoring_start_time = time.time()

            # Process the PDF
            result = self.processor.process_pdf(pdf_path)

            # Memory usage after processing
            after_processing_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_usage.append(('After Processing', after_processing_memory))

            # Force garbage collection
            gc.collect()

            # Memory usage after garbage collection
            after_gc_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_usage.append(('After GC', after_gc_memory))

            # Calculate memory increases
            processing_increase = after_processing_memory - baseline_memory
            retained_after_gc = after_gc_memory - baseline_memory

            # Create a DataFrame with results
            df_memory = pd.DataFrame(memory_usage, columns=['Stage', 'Memory (MB)'])

            # Save results to CSV
            csv_path = self.results_dir / "memory_usage_results.csv"
            df_memory.to_csv(csv_path, index=False)

            # Create a bar chart of memory usage
            plt.figure(figsize=(10, 6))
            plt.bar(df_memory['Stage'], df_memory['Memory (MB)'])
            plt.xlabel('Processing Stage')
            plt.ylabel('Memory Usage (MB)')
            plt.title('Memory Usage During PDF Processing')
            plt.tight_layout()

            # Save the chart
            chart_path = self.results_dir / "memory_usage_chart.png"
            plt.savefig(chart_path)
            plt.close()

            # Log results
            print(f"Baseline memory: {baseline_memory:.2f} MB")
            print(f"Memory after processing: {after_processing_memory:.2f} MB")
            print(f"Memory after GC: {after_gc_memory:.2f} MB")
            print(f"Memory increase during processing: {processing_increase:.2f} MB")
            print(f"Memory retained after GC: {retained_after_gc:.2f} MB")

            # Check that memory increase is reasonable
            self.assertLessEqual(processing_increase, 500,
                                f"Memory increase during processing ({processing_increase:.2f} MB) exceeds 500 MB")

            # Check that most memory is released after GC
            self.assertLessEqual(retained_after_gc, processing_increase * 0.5,
                                f"Too much memory retained after GC ({retained_after_gc:.2f} MB)")

        except Exception as e:
            self.fail(f"Memory usage test failed: {e}")

    def test_memory_usage_with_multiple_pdfs(self):
        """Test memory usage when processing multiple PDFs sequentially."""
        # Get sample PDFs
        pdf_files = list(self.example_pdfs_dir.glob("*.pdf"))

        # Skip if not enough files found
        if len(pdf_files) < 2:
            self.skipTest("Not enough PDF files found for testing")

        # Limit to 3 PDFs for testing
        pdf_files = pdf_files[:3]

        # Get the current process
        process = psutil.Process(os.getpid())

        # Baseline memory usage
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Memory usage measurements
        memory_usage = [('Baseline', baseline_memory)]

        # Process each PDF and monitor memory usage
        try:
            for i, pdf_path in enumerate(pdf_files):
                # Process the PDF
                result = self.processor.process_pdf(pdf_path)

                # Memory usage after processing
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_usage.append((f'After PDF {i+1}', current_memory))

                # Force garbage collection
                gc.collect()

                # Memory usage after garbage collection
                after_gc_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_usage.append((f'After GC {i+1}', after_gc_memory))

            # Create a DataFrame with results
            df_memory = pd.DataFrame(memory_usage, columns=['Stage', 'Memory (MB)'])

            # Save results to CSV
            csv_path = self.results_dir / "memory_usage_multiple_pdfs.csv"
            df_memory.to_csv(csv_path, index=False)

            # Create a bar chart of memory usage
            plt.figure(figsize=(12, 6))
            plt.bar(df_memory['Stage'], df_memory['Memory (MB)'])
            plt.xlabel('Processing Stage')
            plt.ylabel('Memory Usage (MB)')
            plt.title('Memory Usage During Multiple PDF Processing')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()

            # Save the chart
            chart_path = self.results_dir / "memory_usage_multiple_pdfs_chart.png"
            plt.savefig(chart_path)
            plt.close()

            # Check for memory leaks
            final_memory = df_memory['Memory (MB)'].iloc[-1]
            memory_growth = final_memory - baseline_memory

            # Check that memory growth is reasonable
            self.assertLessEqual(memory_growth, 100,
                                f"Memory growth after multiple PDFs ({memory_growth:.2f} MB) indicates a possible leak")

        except Exception as e:
            self.fail(f"Memory usage test with multiple PDFs failed: {e}")

    def test_memory_usage_under_load(self):
        """Test memory usage under high load conditions."""
        # Get the current process
        process = psutil.Process(os.getpid())

        # Baseline memory usage
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Create a large test dataset in memory
        try:
            # Create a large array to simulate high memory usage
            large_array = np.ones((1000, 1000, 10), dtype=np.float32)  # ~40 MB

            # Memory usage after creating large array
            after_array_memory = process.memory_info().rss / 1024 / 1024  # MB

            # Create a processor instance under high memory conditions
            high_load_processor = JortPDFProcessor()

            # Memory usage after creating processor
            after_processor_memory = process.memory_info().rss / 1024 / 1024  # MB

            # Get a sample PDF
            pdf_files = list(self.example_pdfs_dir.glob("*.pdf"))

            # Skip if no files found
            if not pdf_files:
                self.skipTest("No PDF files found for testing")

            pdf_path = pdf_files[0]

            # Process the PDF under high memory conditions
            result = high_load_processor.process_pdf(pdf_path)

            # Memory usage after processing
            after_processing_memory = process.memory_info().rss / 1024 / 1024  # MB

            # Delete the large array to free memory
            del large_array
            gc.collect()

            # Memory usage after cleanup
            after_cleanup_memory = process.memory_info().rss / 1024 / 1024  # MB

            # Create memory usage data
            memory_usage = [
                ('Baseline', baseline_memory),
                ('After Large Array', after_array_memory),
                ('After Processor Creation', after_processor_memory),
                ('After Processing', after_processing_memory),
                ('After Cleanup', after_cleanup_memory)
            ]

            # Create a DataFrame with results
            df_memory = pd.DataFrame(memory_usage, columns=['Stage', 'Memory (MB)'])

            # Save results to CSV
            csv_path = self.results_dir / "memory_usage_under_load.csv"
            df_memory.to_csv(csv_path, index=False)

            # Create a bar chart of memory usage
            plt.figure(figsize=(12, 6))
            plt.bar(df_memory['Stage'], df_memory['Memory (MB)'])
            plt.xlabel('Processing Stage')
            plt.ylabel('Memory Usage (MB)')
            plt.title('Memory Usage Under High Load')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()

            # Save the chart
            chart_path = self.results_dir / "memory_usage_under_load_chart.png"
            plt.savefig(chart_path)
            plt.close()

            # Check that processing was successful even under high load
            self.assertEqual(result['status'], 'success')

            # Check that memory is properly released after cleanup
            memory_after_cleanup = after_cleanup_memory - baseline_memory
            self.assertLessEqual(memory_after_cleanup, 50,
                                f"Memory not properly released after cleanup ({memory_after_cleanup:.2f} MB)")

        except Exception as e:
            self.fail(f"Memory usage test under load failed: {e}")

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory
        cleanup_temp_directory(cls.temp_dir)

        # Keep results directory for analysis

if __name__ == "__main__":
    unittest.main()
