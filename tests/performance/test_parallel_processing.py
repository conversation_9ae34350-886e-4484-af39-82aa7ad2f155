"""Tests for parallel processing feature."""
import unittest
import os
import time
import multiprocessing
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor
from jortextractor.core.pipeline import JortPDFProcessor
from tests.test_utils import create_temp_directory, cleanup_temp_directory

class TestParallelProcessing(unittest.TestCase):
    """Test cases for parallel processing feature."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()

        # Create a directory for test results
        cls.results_dir = Path("tests/parallel_results")
        cls.results_dir.mkdir(parents=True, exist_ok=True)

        # Path to example PDFs
        cls.example_pdfs_dir = Path("ProjectContext/DataExamples")

    def test_parallel_vs_sequential_processing(self):
        """Test parallel processing of multiple PDFs compared to sequential processing."""
        # Get all example PDFs
        pdf_files = list(self.example_pdfs_dir.glob("*.pdf"))

        # Skip if not enough files found
        if len(pdf_files) < 2:
            self.skipTest("Not enough PDF files found for parallel processing test")

        # Function to process a single PDF
        def process_pdf(pdf_path):
            processor = JortPDFProcessor()
            start_time = time.time()
            result = processor.process_pdf(pdf_path)
            end_time = time.time()
            return {
                'filename': pdf_path.name,
                'processing_time': end_time - start_time,
                'status': result['status']
            }

        # Process PDFs in parallel
        num_cores = multiprocessing.cpu_count()
        max_workers = min(num_cores, len(pdf_files))

        start_time = time.time()

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            parallel_results = list(executor.map(process_pdf, pdf_files))

        end_time = time.time()
        parallel_total_time = end_time - start_time

        # Process PDFs sequentially for comparison
        sequential_results = []
        sequential_start_time = time.time()

        for pdf_path in pdf_files:
            sequential_results.append(process_pdf(pdf_path))

        sequential_end_time = time.time()
        sequential_total_time = sequential_end_time - sequential_start_time

        # Calculate speedup
        speedup = sequential_total_time / parallel_total_time

        # Create DataFrames with results
        df_parallel = pd.DataFrame(parallel_results)
        df_sequential = pd.DataFrame(sequential_results)

        # Save results to CSV
        parallel_csv_path = self.results_dir / "parallel_processing_results.csv"
        df_parallel.to_csv(parallel_csv_path, index=False)

        sequential_csv_path = self.results_dir / "sequential_processing_results.csv"
        df_sequential.to_csv(sequential_csv_path, index=False)

        # Create a bar chart comparing total processing times
        plt.figure(figsize=(10, 6))
        plt.bar(['Sequential', 'Parallel'], [sequential_total_time, parallel_total_time])
        plt.xlabel('Processing Method')
        plt.ylabel('Total Processing Time (seconds)')
        plt.title(f'Sequential vs. Parallel Processing (Speedup: {speedup:.2f}x)')
        plt.tight_layout()

        # Save the chart
        chart_path = self.results_dir / "parallel_vs_sequential_chart.png"
        plt.savefig(chart_path)
        plt.close()

        # Create a bar chart comparing individual file processing times
        plt.figure(figsize=(12, 6))

        # Get filenames and processing times
        filenames = df_sequential['filename']
        sequential_times = df_sequential['processing_time']

        # Create a dictionary mapping filenames to parallel processing times
        parallel_times_dict = {row['filename']: row['processing_time'] for row in parallel_results}
        parallel_times = [parallel_times_dict.get(filename, 0) for filename in filenames]

        # Set up the bar chart
        x = np.arange(len(filenames))
        width = 0.35

        fig, ax = plt.subplots(figsize=(12, 6))
        rects1 = ax.bar(x - width/2, sequential_times, width, label='Sequential')
        rects2 = ax.bar(x + width/2, parallel_times, width, label='Parallel')

        # Add labels and title
        ax.set_xlabel('PDF File')
        ax.set_ylabel('Processing Time (seconds)')
        ax.set_title('Sequential vs. Parallel Processing Time by File')
        ax.set_xticks(x)
        ax.set_xticklabels(filenames, rotation=45, ha='right')
        ax.legend()

        plt.tight_layout()

        # Save the chart
        file_chart_path = self.results_dir / "parallel_vs_sequential_by_file_chart.png"
        plt.savefig(file_chart_path)
        plt.close()

        # Check that parallel processing provides speedup
        self.assertGreater(speedup, 1.0,
                          f"Parallel processing speedup ({speedup:.2f}x) is less than 1.0x")

    def test_parallel_processing_with_varying_workers(self):
        """Test parallel processing with different numbers of worker processes."""
        # Get all example PDFs
        pdf_files = list(self.example_pdfs_dir.glob("*.pdf"))

        # Skip if not enough files found
        if len(pdf_files) < 4:
            self.skipTest("Not enough PDF files found for worker count test")

        # Limit to 4 PDFs for testing
        pdf_files = pdf_files[:4]

        # Function to process PDFs in parallel with a specific number of workers
        def process_pdfs_parallel(num_workers):
            start_time = time.time()

            with ProcessPoolExecutor(max_workers=num_workers) as executor:
                results = list(executor.map(process_single_pdf, pdf_files))

            end_time = time.time()
            total_time = end_time - start_time

            return {
                'num_workers': num_workers,
                'total_time': total_time,
                'results': results
            }

        # Function to process a single PDF
        def process_single_pdf(pdf_path):
            processor = JortPDFProcessor()
            start_time = time.time()
            result = processor.process_pdf(pdf_path)
            end_time = time.time()
            return {
                'filename': pdf_path.name,
                'processing_time': end_time - start_time,
                'status': result['status']
            }

        # Test with different numbers of workers
        max_workers = min(multiprocessing.cpu_count(), len(pdf_files))
        worker_counts = list(range(1, max_workers + 1))

        # Process with each worker count
        worker_results = []
        for num_workers in worker_counts:
            result = process_pdfs_parallel(num_workers)
            worker_results.append(result)

        # Extract total times
        worker_times = [(result['num_workers'], result['total_time']) for result in worker_results]

        # Create a DataFrame with results
        df_workers = pd.DataFrame(worker_times, columns=['num_workers', 'total_time'])

        # Save results to CSV
        csv_path = self.results_dir / "worker_count_results.csv"
        df_workers.to_csv(csv_path, index=False)

        # Create a line chart of processing times vs. worker count
        plt.figure(figsize=(10, 6))
        plt.plot(df_workers['num_workers'], df_workers['total_time'], marker='o')
        plt.xlabel('Number of Worker Processes')
        plt.ylabel('Total Processing Time (seconds)')
        plt.title('Processing Time vs. Worker Count')
        plt.grid(True)
        plt.tight_layout()

        # Save the chart
        chart_path = self.results_dir / "worker_count_chart.png"
        plt.savefig(chart_path)
        plt.close()

        # Calculate speedup for each worker count relative to single worker
        single_worker_time = df_workers.loc[df_workers['num_workers'] == 1, 'total_time'].values[0]
        df_workers['speedup'] = single_worker_time / df_workers['total_time']

        # Create a line chart of speedup vs. worker count
        plt.figure(figsize=(10, 6))
        plt.plot(df_workers['num_workers'], df_workers['speedup'], marker='o')
        plt.xlabel('Number of Worker Processes')
        plt.ylabel('Speedup (relative to single worker)')
        plt.title('Speedup vs. Worker Count')
        plt.grid(True)
        plt.tight_layout()

        # Save the chart
        speedup_chart_path = self.results_dir / "worker_speedup_chart.png"
        plt.savefig(speedup_chart_path)
        plt.close()

        # Check that adding workers provides speedup
        max_speedup = df_workers['speedup'].max()
        self.assertGreater(max_speedup, 1.0,
                          f"Maximum speedup with multiple workers ({max_speedup:.2f}x) is less than 1.0x")

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory
        cleanup_temp_directory(cls.temp_dir)

        # Keep results directory for analysis

if __name__ == "__main__":
    unittest.main()
