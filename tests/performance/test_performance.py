"""Performance tests for the JortExtractor system."""
import unittest
import os
import time
import psutil
import tempfile
import multiprocessing
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor
from jortextractor.core.pipeline import JortPDFProcessor

class TestPerformance(unittest.TestCase):
    """Performance tests for the JortExtractor system."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = Path(tempfile.mkdtemp())

        # Create a directory for test results
        cls.results_dir = Path("tests/performance_results")
        cls.results_dir.mkdir(parents=True, exist_ok=True)

        # Path to example PDFs
        cls.example_pdfs_dir = Path("ProjectContext/DataExamples")

        # Create a processor instance
        cls.processor = JortPDFProcessor(temp_dir=cls.temp_dir)

    def test_processing_time(self):
        """Test processing time for different PDF types."""
        # Get all example PDFs
        pdf_files = list(self.example_pdfs_dir.glob("*.pdf"))

        # Skip if no files found
        if not pdf_files:
            self.skipTest("No PDF files found for testing")

        # Process each PDF and measure time
        results = []
        for pdf_path in pdf_files:
            start_time = time.time()
            result = self.processor.process_pdf(pdf_path)
            end_time = time.time()

            processing_time = end_time - start_time
            num_pages = len(result.get('page_info', [1]))  # Default to 1 if not available

            results.append({
                'filename': pdf_path.name,
                'processing_time': processing_time,
                'num_pages': num_pages,
                'time_per_page': processing_time / num_pages
            })

        # Create a DataFrame with results
        df_results = pd.DataFrame(results)

        # Save results to CSV
        csv_path = self.results_dir / "processing_time_results.csv"
        df_results.to_csv(csv_path, index=False)

        # Create a bar chart of processing times
        plt.figure(figsize=(10, 6))
        plt.bar(df_results['filename'], df_results['processing_time'])
        plt.xlabel('PDF File')
        plt.ylabel('Processing Time (seconds)')
        plt.title('PDF Processing Time')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()

        # Save the chart
        chart_path = self.results_dir / "processing_time_chart.png"
        plt.savefig(chart_path)
        plt.close()

        # Check that processing time is reasonable
        avg_time_per_page = df_results['time_per_page'].mean()
        self.assertLessEqual(avg_time_per_page, 30,
                            f"Average processing time per page ({avg_time_per_page:.2f}s) exceeds 30 seconds")

    def test_memory_usage(self):
        """Test memory usage during processing."""
        # Get a sample PDF
        pdf_files = list(self.example_pdfs_dir.glob("*.pdf"))

        # Skip if no files found
        if not pdf_files:
            self.skipTest("No PDF files found for testing")

        pdf_path = pdf_files[0]

        # Monitor memory usage during processing
        process = psutil.Process(os.getpid())
        memory_usage = []

        # Baseline memory usage
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_usage.append(('Baseline', baseline_memory))

        # Memory usage during processing
        result = self.processor.process_pdf(pdf_path)
        current_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_usage.append(('After Processing', current_memory))

        # Memory increase
        memory_increase = current_memory - baseline_memory

        # Create a DataFrame with results
        df_memory = pd.DataFrame(memory_usage, columns=['Stage', 'Memory (MB)'])

        # Save results to CSV
        csv_path = self.results_dir / "memory_usage_results.csv"
        df_memory.to_csv(csv_path, index=False)

        # Create a bar chart of memory usage
        plt.figure(figsize=(8, 6))
        plt.bar(df_memory['Stage'], df_memory['Memory (MB)'])
        plt.xlabel('Processing Stage')
        plt.ylabel('Memory Usage (MB)')
        plt.title('Memory Usage During PDF Processing')
        plt.tight_layout()

        # Save the chart
        chart_path = self.results_dir / "memory_usage_chart.png"
        plt.savefig(chart_path)
        plt.close()

        # Check that memory increase is reasonable
        self.assertLessEqual(memory_increase, 500,
                            f"Memory increase ({memory_increase:.2f} MB) exceeds 500 MB")

    def test_parallel_processing(self):
        """Test parallel processing of multiple PDFs."""
        # Get all example PDFs
        pdf_files = list(self.example_pdfs_dir.glob("*.pdf"))

        # Skip if not enough files found
        if len(pdf_files) < 2:
            self.skipTest("Not enough PDF files found for parallel processing test")

        # Function to process a single PDF
        def process_pdf(pdf_path):
            processor = JortPDFProcessor()
            start_time = time.time()
            result = processor.process_pdf(pdf_path)
            end_time = time.time()
            return {
                'filename': pdf_path.name,
                'processing_time': end_time - start_time,
                'status': result['status']
            }

        # Process PDFs in parallel
        num_cores = multiprocessing.cpu_count()
        max_workers = min(num_cores, len(pdf_files))

        start_time = time.time()

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            parallel_results = list(executor.map(process_pdf, pdf_files))

        end_time = time.time()
        parallel_total_time = end_time - start_time

        # Process PDFs sequentially for comparison
        sequential_results = []
        sequential_start_time = time.time()

        for pdf_path in pdf_files:
            sequential_results.append(process_pdf(pdf_path))

        sequential_end_time = time.time()
        sequential_total_time = sequential_end_time - sequential_start_time

        # Calculate speedup
        speedup = sequential_total_time / parallel_total_time

        # Create DataFrames with results
        df_parallel = pd.DataFrame(parallel_results)
        df_sequential = pd.DataFrame(sequential_results)

        # Save results to CSV
        parallel_csv_path = self.results_dir / "parallel_processing_results.csv"
        df_parallel.to_csv(parallel_csv_path, index=False)

        sequential_csv_path = self.results_dir / "sequential_processing_results.csv"
        df_sequential.to_csv(sequential_csv_path, index=False)

        # Create a bar chart comparing total processing times
        plt.figure(figsize=(8, 6))
        plt.bar(['Sequential', 'Parallel'], [sequential_total_time, parallel_total_time])
        plt.xlabel('Processing Method')
        plt.ylabel('Total Processing Time (seconds)')
        plt.title(f'Sequential vs. Parallel Processing (Speedup: {speedup:.2f}x)')
        plt.tight_layout()

        # Save the chart
        chart_path = self.results_dir / "parallel_vs_sequential_chart.png"
        plt.savefig(chart_path)
        plt.close()

        # Check that parallel processing provides speedup
        self.assertGreater(speedup, 1.5,
                          f"Parallel processing speedup ({speedup:.2f}x) is less than 1.5x")

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory
        import shutil
        shutil.rmtree(cls.temp_dir)

        # Keep results directory for analysis

if __name__ == "__main__":
    unittest.main()
