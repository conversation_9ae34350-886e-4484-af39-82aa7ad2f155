# JortExtractor Testing Framework

This directory contains the testing framework for the JortExtractor system. The tests are organized into three categories:

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test the interaction between components
3. **Performance Tests**: Test the system's performance characteristics

## Running Tests

To run all tests:

```bash
python tests/run_tests.py
```

To run specific test types:

```bash
python tests/run_tests.py --type unit
python tests/run_tests.py --type integration
python tests/run_tests.py --type performance
```

For verbose output:

```bash
python tests/run_tests.py --verbose
```

## Test Structure

### Unit Tests

Unit tests focus on testing individual components in isolation:

- `test_document_classifier.py`: Tests for document type classification
- `test_image_processor.py`: Tests for image processing functions
- `test_name_validator.py`: Tests for name validation and correction

### Integration Tests

Integration tests focus on testing the interaction between components:

- `test_pipeline_integration.py`: Tests for the complete extraction pipeline

### Performance Tests

Performance tests focus on testing the system's performance characteristics:

- `test_performance.py`: Tests for processing time, memory usage, and parallel processing

## Test Data

Test data is stored in the `tests/test_data` directory. This includes:

- Sample images for testing image processing
- Sample PDFs for testing document classification
- Ground truth data for testing extraction accuracy

## Test Results

Test results are logged to `test_results.log` and also displayed in the console. Performance test results are saved to the `tests/performance_results` directory, including:

- CSV files with detailed results
- Charts visualizing the results

## Adding New Tests

When adding new tests:

1. Create a new test file in the appropriate directory
2. Subclass `unittest.TestCase`
3. Add test methods that start with `test_`
4. Run the tests to ensure they pass

## Test Coverage

To generate a test coverage report:

```bash
coverage run --source=jortextractor tests/run_tests.py
coverage report
coverage html  # Generates HTML report in htmlcov/
```

## Continuous Integration

These tests are designed to be run in a CI/CD pipeline. The test runner will exit with code 0 if all tests pass, and code 1 if any tests fail.
