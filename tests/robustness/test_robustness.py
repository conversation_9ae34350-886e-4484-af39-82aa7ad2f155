"""Robustness tests for the JortExtractor system."""
import unittest
import os
import shutil
import tempfile
import logging
import cv2
import numpy as np
import pandas as pd
from pathlib import Path
from unittest.mock import patch, MagicMock

from jortextractor.core.pipeline import JortPDFProcessor
from jortextractor.core.document_classifier import DocumentClassifier
from jortextractor.core.image_processor import optimize_for_name_ocr
from tests.test_utils import (
    create_temp_directory, cleanup_temp_directory, create_test_image,
    add_text_to_image, add_noise_to_image, save_image
)

class TestRobustness(unittest.TestCase):
    """Robustness tests for the JortExtractor system."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()
        cls.test_data_dir = cls.temp_dir / "test_data"
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

        # Create a directory for test results
        cls.results_dir = Path("tests/robustness_results")
        cls.results_dir.mkdir(parents=True, exist_ok=True)

        # Path to example PDFs
        cls.example_pdfs_dir = Path("ProjectContext/DataExamples")

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a logger for testing
        self.logger = logging.getLogger("test_robustness")
        self.logger.setLevel(logging.DEBUG)

        # Create a processor instance
        self.processor = JortPDFProcessor()

    def test_empty_page_handling(self):
        """Test handling of empty pages."""
        # Create an empty image
        empty_img = create_test_image(800, 1000)
        save_image(empty_img, self.test_data_dir / "empty_page.png")

        # Test document classification
        classifier = DocumentClassifier(logger=self.logger)
        doc_type = classifier.classify_document(empty_img)

        # Should return a valid document type even for empty page
        self.assertIn(doc_type, ["type1", "type2", "type3", "type4"])

        # Test image processing
        optimized_img = optimize_for_name_ocr(empty_img)
        self.assertIsNotNone(optimized_img)

        # The optimized image might be grayscale or color, so we just check that it's not empty
        self.assertTrue(optimized_img.size > 0, "Optimized image should not be empty")

        # Test table extraction (should not raise exceptions)
        try:
            tables = self.processor._extract_tables_from_image(empty_img, 1)
            # We don't assert anything about the tables, just that the function doesn't crash
            self.logger.info(f"Empty page extraction returned {len(tables)} tables")
        except Exception as e:
            self.fail(f"_extract_tables_from_image raised exception for empty page: {e}")

    def test_rotated_page_handling(self):
        """Test handling of rotated pages."""
        # Create a test image with text
        img = create_test_image(800, 1000)
        img = add_text_to_image(img, "MOHAMED BEN ALI", (300, 500), 0.8, (0, 0, 0), 2)
        save_image(img, self.test_data_dir / "original_page.png")

        # Create rotated versions
        rotated_90 = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
        rotated_180 = cv2.rotate(img, cv2.ROTATE_180)
        rotated_270 = cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE)

        save_image(rotated_90, self.test_data_dir / "rotated_90.png")
        save_image(rotated_180, self.test_data_dir / "rotated_180.png")
        save_image(rotated_270, self.test_data_dir / "rotated_270.png")

        # Test document classification for each rotation
        classifier = DocumentClassifier(logger=self.logger)

        # Original
        doc_type_original = classifier.classify_document(img)

        # 90 degrees
        doc_type_90 = classifier.classify_document(rotated_90)

        # 180 degrees
        doc_type_180 = classifier.classify_document(rotated_180)

        # 270 degrees
        doc_type_270 = classifier.classify_document(rotated_270)

        # Log results
        self.logger.info(f"Original: {doc_type_original}, 90°: {doc_type_90}, 180°: {doc_type_180}, 270°: {doc_type_270}")

        # Test image processing for each rotation
        try:
            optimized_90 = optimize_for_name_ocr(rotated_90)
            optimized_180 = optimize_for_name_ocr(rotated_180)
            optimized_270 = optimize_for_name_ocr(rotated_270)

            save_image(optimized_90, self.test_data_dir / "optimized_90.png")
            save_image(optimized_180, self.test_data_dir / "optimized_180.png")
            save_image(optimized_270, self.test_data_dir / "optimized_270.png")
        except Exception as e:
            self.fail(f"Image optimization failed for rotated images: {e}")

    def test_high_noise_handling(self):
        """Test handling of images with high noise levels."""
        # Create a test image with text
        img = create_test_image(800, 1000)
        img = add_text_to_image(img, "MOHAMED BEN ALI", (300, 500), 0.8, (0, 0, 0), 2)
        save_image(img, self.test_data_dir / "clean_page.png")

        # Create versions with increasing noise levels
        noise_levels = [10, 30, 50, 70, 90]
        noisy_images = []

        for noise in noise_levels:
            noisy_img = add_noise_to_image(img, noise)
            noisy_images.append(noisy_img)
            save_image(noisy_img, self.test_data_dir / f"noisy_page_{noise}.png")

        # Test document classification for each noise level
        classifier = DocumentClassifier(logger=self.logger)

        for i, (noise, noisy_img) in enumerate(zip(noise_levels, noisy_images)):
            # Test classification
            try:
                doc_type = classifier.classify_document(noisy_img)
                self.logger.info(f"Noise level {noise}: Classified as {doc_type}")
            except Exception as e:
                self.logger.error(f"Classification failed for noise level {noise}: {e}")

            # Test image optimization
            try:
                optimized_img = optimize_for_name_ocr(noisy_img)
                save_image(optimized_img, self.test_data_dir / f"optimized_noisy_{noise}.png")
            except Exception as e:
                self.logger.error(f"Optimization failed for noise level {noise}: {e}")

            # Test table extraction
            try:
                tables = self.processor._extract_tables_from_image(noisy_img, i+1)
                self.logger.info(f"Noise level {noise}: Extracted {len(tables)} tables")
            except Exception as e:
                self.logger.error(f"Table extraction failed for noise level {noise}: {e}")

    def test_low_resolution_handling(self):
        """Test handling of low resolution images."""
        # Create a high-resolution test image with text
        img = create_test_image(800, 1000)
        img = add_text_to_image(img, "MOHAMED BEN ALI", (300, 500), 0.8, (0, 0, 0), 2)
        save_image(img, self.test_data_dir / "high_res_page.png")

        # Create versions with decreasing resolution
        resolutions = [(400, 500), (200, 250), (100, 125), (50, 63)]
        low_res_images = []

        for width, height in resolutions:
            low_res_img = cv2.resize(img, (width, height))
            low_res_images.append(low_res_img)
            save_image(low_res_img, self.test_data_dir / f"low_res_page_{width}x{height}.png")

        # Test document classification for each resolution
        classifier = DocumentClassifier(logger=self.logger)

        for i, ((width, height), low_res_img) in enumerate(zip(resolutions, low_res_images)):
            # Test classification
            try:
                doc_type = classifier.classify_document(low_res_img)
                self.logger.info(f"Resolution {width}x{height}: Classified as {doc_type}")
            except Exception as e:
                self.logger.error(f"Classification failed for resolution {width}x{height}: {e}")

            # Test image optimization
            try:
                optimized_img = optimize_for_name_ocr(low_res_img)
                save_image(optimized_img, self.test_data_dir / f"optimized_low_res_{width}x{height}.png")
            except Exception as e:
                self.logger.error(f"Optimization failed for resolution {width}x{height}: {e}")

            # Test table extraction
            try:
                tables = self.processor._extract_tables_from_image(low_res_img, i+1)
                self.logger.info(f"Resolution {width}x{height}: Extracted {len(tables)} tables")
            except Exception as e:
                self.logger.error(f"Table extraction failed for resolution {width}x{height}: {e}")

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory
        cleanup_temp_directory(cls.temp_dir)

        # Keep results directory for analysis

if __name__ == "__main__":
    unittest.main()
