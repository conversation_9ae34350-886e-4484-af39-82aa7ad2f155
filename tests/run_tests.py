#!/usr/bin/env python3
"""Enhanced test runner for JortExtractor tests."""
import unittest
import argparse
import sys
import os
import logging
import time
import json
import datetime
import matplotlib.pyplot as plt
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import setup_path to add the current directory to the Python path
try:
    import setup_path
except ImportError:
    pass

# Configure logging
log_file = f"test_results_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("test_runner")

# Define test categories
TEST_CATEGORIES = {
    "unit": "tests/unit",
    "integration": "tests/integration",
    "performance": "tests/performance",
    "robustness": "tests/robustness",
    "security": "tests/security",
}

def discover_and_run_tests(test_type=None, verbose=False, pattern=None, parallel=False, max_workers=None):
    """Discover and run tests of the specified type.

    Args:
        test_type: Type of tests to run (unit, integration, etc.)
        verbose: Whether to show verbose output
        pattern: Pattern to match test files
        parallel: Whether to run tests in parallel
        max_workers: Maximum number of worker processes for parallel execution

    Returns:
        True if all tests passed, False otherwise
    """
    start_time = time.time()

    # Set up test discovery
    loader = unittest.TestLoader()
    if pattern:
        loader.testNamePatterns = [pattern]

    if test_type in TEST_CATEGORIES:
        logger.info(f"Running {test_type} tests...")
        start_dir = TEST_CATEGORIES[test_type]
    else:
        logger.info("Running all tests...")
        start_dir = 'tests'

    # Create test directory if it doesn't exist
    Path(start_dir).mkdir(parents=True, exist_ok=True)

    # Discover tests
    suite = loader.discover(start_dir)

    # Count tests
    test_count = suite.countTestCases()
    logger.info(f"Discovered {test_count} tests")

    # Run tests
    if parallel and test_count > 1:
        result = run_tests_in_parallel(suite, verbose, max_workers)
    else:
        runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
        result = runner.run(suite)

    # Log results
    end_time = time.time()
    duration = end_time - start_time

    logger.info(f"Tests completed in {duration:.2f} seconds")
    logger.info(f"Ran {result.testsRun} tests")

    if result.wasSuccessful():
        logger.info("All tests passed!")
    else:
        logger.error(f"Tests failed: {len(result.failures)} failures, {len(result.errors)} errors")

        # Log failures
        if result.failures:
            logger.error("Failures:")
            for i, (test, traceback) in enumerate(result.failures, 1):
                logger.error(f"  {i}. {test}")
                logger.error(f"     {traceback.split('Traceback')[0]}")

        # Log errors
        if result.errors:
            logger.error("Errors:")
            for i, (test, traceback) in enumerate(result.errors, 1):
                logger.error(f"  {i}. {test}")
                logger.error(f"     {traceback.split('Traceback')[0]}")

    # Generate test report
    generate_test_report(result, duration, test_type)

    return result.wasSuccessful()

def run_tests_in_parallel(suite, verbose=False, max_workers=None):
    """Run tests in parallel using ProcessPoolExecutor.

    Args:
        suite: Test suite to run
        verbose: Whether to show verbose output
        max_workers: Maximum number of worker processes

    Returns:
        TestResult object
    """
    # Create a custom result object to collect results
    result = unittest.TestResult()

    # Extract individual test cases
    test_cases = []
    for test in suite:
        if isinstance(test, unittest.TestSuite):
            for subtest in test:
                if hasattr(subtest, '_testMethodName'):
                    test_cases.append(subtest)
        elif hasattr(test, '_testMethodName'):
            test_cases.append(test)

    logger.info(f"Running {len(test_cases)} tests in parallel")

    # Determine number of workers
    if not max_workers:
        max_workers = min(os.cpu_count() or 1, len(test_cases))

    # Function to run a single test
    def run_test(test):
        # Create a new result for this test
        test_result = unittest.TestResult()
        test.run(test_result)
        return {
            'test': test.id(),
            'success': test_result.wasSuccessful(),
            'failures': [(test.id(), traceback) for test, traceback in test_result.failures],
            'errors': [(test.id(), traceback) for test, traceback in test_result.errors],
        }

    # Run tests in parallel
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        test_results = list(executor.map(run_test, test_cases))

    # Collect results
    for test_result in test_results:
        result.testsRun += 1

        if not test_result['success']:
            # Add failures and errors to the result
            for test_id, traceback in test_result['failures']:
                result.failures.append((test_id, traceback))

            for test_id, traceback in test_result['errors']:
                result.errors.append((test_id, traceback))

    return result

def generate_test_report(result, duration, test_type=None):
    """Generate a detailed test report.

    Args:
        result: TestResult object
        duration: Test duration in seconds
        test_type: Type of tests that were run
    """
    # Create report directory
    report_dir = Path("test_reports")
    report_dir.mkdir(exist_ok=True)

    # Create report data
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    report_data = {
        "timestamp": timestamp,
        "test_type": test_type or "all",
        "duration": duration,
        "tests_run": result.testsRun,
        "success": result.wasSuccessful(),
        "failures": len(result.failures),
        "errors": len(result.errors),
        "failure_details": [{"test": str(test), "traceback": traceback} for test, traceback in result.failures],
        "error_details": [{"test": str(test), "traceback": traceback} for test, traceback in result.errors],
    }

    # Save report as JSON
    report_file = report_dir / f"test_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(report_data, f, indent=2)

    logger.info(f"Test report saved to {report_file}")

    # Generate charts
    if result.testsRun > 0:
        # Create pie chart of test results
        plt.figure(figsize=(8, 6))
        labels = ['Passed', 'Failed', 'Error']
        sizes = [
            result.testsRun - len(result.failures) - len(result.errors),
            len(result.failures),
            len(result.errors)
        ]
        colors = ['#4CAF50', '#F44336', '#FFC107']
        explode = (0.1, 0, 0)  # explode the 1st slice (Passed)

        # Only include non-zero values
        non_zero_labels = [label for label, size in zip(labels, sizes) if size > 0]
        non_zero_sizes = [size for size in sizes if size > 0]
        non_zero_colors = [color for color, size in zip(colors, sizes) if size > 0]
        non_zero_explode = [explode[i] for i, size in enumerate(sizes) if size > 0]

        if non_zero_sizes:
            plt.pie(non_zero_sizes, explode=non_zero_explode, labels=non_zero_labels, colors=non_zero_colors,
                    autopct='%1.1f%%', shadow=True, startangle=140)
            plt.axis('equal')
            plt.title(f'Test Results: {test_type or "All"} Tests')

            # Save chart
            chart_file = report_dir / f"test_results_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_file)
            plt.close()

            logger.info(f"Test results chart saved to {chart_file}")

def main():
    """Main function to parse arguments and run tests."""
    parser = argparse.ArgumentParser(description="Run JortExtractor tests")
    parser.add_argument("--type", choices=list(TEST_CATEGORIES.keys()) + ["all"],
                        default="all", help="Type of tests to run")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--pattern", help="Pattern to match test names")
    parser.add_argument("--parallel", action="store_true", help="Run tests in parallel")
    parser.add_argument("--workers", type=int, help="Maximum number of worker processes for parallel execution")

    args = parser.parse_args()

    # Run tests
    success = discover_and_run_tests(
        test_type=None if args.type == "all" else args.type,
        verbose=args.verbose,
        pattern=args.pattern,
        parallel=args.parallel,
        max_workers=args.workers
    )

    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
