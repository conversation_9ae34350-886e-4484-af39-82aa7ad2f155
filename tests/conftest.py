"""Pytest configuration file."""

import os
import sys
import tempfile
import pytest
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import test utilities
from tests.test_utils import create_temp_directory, cleanup_temp_directory


@pytest.fixture(scope="session")
def project_root():
    """Return the project root directory."""
    return Path(__file__).parent.parent


@pytest.fixture(scope="session")
def data_dir(project_root):
    """Return the data directory."""
    return project_root / "data"


@pytest.fixture(scope="session")
def examples_dir(data_dir):
    """Return the examples directory."""
    return data_dir / "examples"


@pytest.fixture(scope="session")
def results_dir(project_root):
    """Return the results directory."""
    return project_root / "results"


@pytest.fixture(scope="function")
def temp_dir():
    """Create a temporary directory for test files."""
    temp_dir = create_temp_directory()
    yield temp_dir
    cleanup_temp_directory(temp_dir)
