"""Unit tests for type4 document support."""
import unittest
import numpy as np
import cv2
import os
import logging
from pathlib import Path
import tempfile
from unittest.mock import patch, MagicMock

from jortextractor.core.document_classifier import DocumentClassifier, DOCUMENT_TYPE4
from jortextractor.core.pipeline import JortPDFProcessor
from tests.test_utils import (
    create_type4_sample, create_temp_directory, cleanup_temp_directory,
    add_noise_to_image, save_image
)

class TestType4Support(unittest.TestCase):
    """Test cases for type4 document support."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()
        cls.test_data_dir = cls.temp_dir / "test_data"
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a logger for testing
        self.logger = logging.getLogger("test_type4")
        self.logger.setLevel(logging.DEBUG)

        # Create a classifier with the test logger
        self.classifier = DocumentClassifier(logger=self.logger)

        # Create a processor with the test logger
        self.processor = JortPDFProcessor()

        # Create sample images for testing
        self.type4_image = create_type4_sample()
        self.noisy_type4_image = add_noise_to_image(self.type4_image, 30)

        # Save the images for inspection
        save_image(self.type4_image, self.test_data_dir / "type4_sample.png")
        save_image(self.noisy_type4_image, self.test_data_dir / "noisy_type4_sample.png")

    def test_type4_classification(self):
        """Test classification of type4 document."""
        # Test with clean image
        doc_type = self.classifier.classify_document(self.type4_image)
        self.assertEqual(doc_type, DOCUMENT_TYPE4)

        # Test with noisy image
        doc_type = self.classifier.classify_document(self.noisy_type4_image)
        self.assertEqual(doc_type, DOCUMENT_TYPE4)

        # Test with filename
        doc_type = self.classifier.classify_document(self.type4_image, filename="document_type4.pdf")
        self.assertEqual(doc_type, DOCUMENT_TYPE4)

    def test_type4_column_detection(self):
        """Test column detection in type4 documents."""
        # Type4 should have 3 columns
        columns = self.classifier._count_columns(cv2.cvtColor(self.type4_image, cv2.COLOR_BGR2GRAY))
        self.assertEqual(columns, 3)

        # Test with noisy image
        columns = self.classifier._count_columns(cv2.cvtColor(self.noisy_type4_image, cv2.COLOR_BGR2GRAY))
        self.assertEqual(columns, 3)

    def test_type4_processing(self):
        """Test processing of type4 documents."""
        # Mock the process_pdf method to test type4 processing
        with patch.object(self.processor, '_process_type4_pdf') as mock_process:
            mock_process.return_value = {'status': 'success', 'extracted_data': MagicMock()}

            # Create a mock PDF path
            pdf_path = self.test_data_dir / "mock_type4.pdf"

            # Mock the document classifier to return type4
            with patch.object(self.processor.document_classifier, 'classify_document', return_value=DOCUMENT_TYPE4):
                # Process the mock PDF
                result = self.processor.process_pdf(pdf_path)

                # Check that _process_type4_pdf was called
                mock_process.assert_called_once_with(pdf_path, None)

                # Check that processing was successful
                self.assertEqual(result['status'], 'success')

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory and all its contents
        cleanup_temp_directory(cls.temp_dir)

if __name__ == "__main__":
    unittest.main()
