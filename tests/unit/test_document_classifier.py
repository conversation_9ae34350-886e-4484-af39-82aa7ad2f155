"""Enhanced unit tests for the document classifier module."""
import unittest
import numpy as np
import cv2
import os
import logging
from pathlib import Path
import tempfile
from unittest.mock import patch, MagicMock

from jortextractor.core.document_classifier import DocumentClassifier, DOCUMENT_TYPE1, DOCUMENT_TYPE2, DOCUMENT_TYPE3
from tests.test_utils import (
    create_type1_sample, create_type2_sample, create_type3_sample,
    create_sample_with_header_footer, create_sample_with_table_borders,
    add_noise_to_image, save_image, create_temp_directory, cleanup_temp_directory
)

class TestDocumentClassifier(unittest.TestCase):
    """Enhanced test cases for the DocumentClassifier class."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()
        cls.test_data_dir = cls.temp_dir / "test_data"
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a logger for testing
        self.logger = logging.getLogger("test_classifier")
        self.logger.setLevel(logging.DEBUG)

        # Create a classifier with the test logger
        self.classifier = DocumentClassifier(logger=self.logger)

        # Create sample images for testing
        self.type1_image = create_type1_sample()
        self.type2_image = create_type2_sample()
        self.type3_image = create_type3_sample()
        self.header_footer_image = create_sample_with_header_footer()
        self.table_borders_image = create_sample_with_table_borders()

        # Save the images for inspection
        save_image(self.type1_image, self.test_data_dir / "type1_sample.png")
        save_image(self.type2_image, self.test_data_dir / "type2_sample.png")
        save_image(self.type3_image, self.test_data_dir / "type3_sample.png")
        save_image(self.header_footer_image, self.test_data_dir / "header_footer_sample.png")
        save_image(self.table_borders_image, self.test_data_dir / "table_borders_sample.png")

        # Create noisy versions of the images
        self.noisy_type1_image = add_noise_to_image(self.type1_image, 30)
        self.noisy_type2_image = add_noise_to_image(self.type2_image, 30)
        self.noisy_type3_image = add_noise_to_image(self.type3_image, 30)

        # Save the noisy images for inspection
        save_image(self.noisy_type1_image, self.test_data_dir / "noisy_type1_sample.png")
        save_image(self.noisy_type2_image, self.test_data_dir / "noisy_type2_sample.png")
        save_image(self.noisy_type3_image, self.test_data_dir / "noisy_type3_sample.png")

    def test_classify_type1_document(self):
        """Test classification of type1 document."""
        # Test with clean image
        doc_type = self.classifier.classify_document(self.type1_image)
        self.assertEqual(doc_type, DOCUMENT_TYPE1)

        # Test with noisy image
        doc_type = self.classifier.classify_document(self.noisy_type1_image)
        self.assertEqual(doc_type, DOCUMENT_TYPE1)

        # Test with filename
        doc_type = self.classifier.classify_document(self.type1_image, filename="document_type1.pdf")
        self.assertEqual(doc_type, DOCUMENT_TYPE1)

    def test_classify_type2_document(self):
        """Test classification of type2 document."""
        # Test with clean image
        doc_type = self.classifier.classify_document(self.type2_image)
        self.assertEqual(doc_type, DOCUMENT_TYPE2)

        # Test with noisy image
        doc_type = self.classifier.classify_document(self.noisy_type2_image)
        self.assertEqual(doc_type, DOCUMENT_TYPE2)

        # Test with filename
        doc_type = self.classifier.classify_document(self.type1_image, filename="document_type2.pdf")
        self.assertEqual(doc_type, DOCUMENT_TYPE2)

    def test_classify_type3_document(self):
        """Test classification of type3 document."""
        # Test with clean image
        doc_type = self.classifier.classify_document(self.type3_image)
        self.assertEqual(doc_type, DOCUMENT_TYPE3)

        # Test with noisy image
        doc_type = self.classifier.classify_document(self.noisy_type3_image)
        self.assertEqual(doc_type, DOCUMENT_TYPE3)

        # Test with filename
        doc_type = self.classifier.classify_document(self.type1_image, filename="document_type3.pdf")
        self.assertEqual(doc_type, DOCUMENT_TYPE3)

    def test_count_columns(self):
        """Test column counting feature."""
        # Type1 should have 2 columns
        columns = self.classifier._count_columns(cv2.cvtColor(self.type1_image, cv2.COLOR_BGR2GRAY))
        self.assertEqual(columns, 2)

        # Type2 should have 3 columns
        columns = self.classifier._count_columns(cv2.cvtColor(self.type2_image, cv2.COLOR_BGR2GRAY))
        self.assertEqual(columns, 3)

        # Type3 should have 1 column
        columns = self.classifier._count_columns(cv2.cvtColor(self.type3_image, cv2.COLOR_BGR2GRAY))
        self.assertEqual(columns, 1)

        # Test with noisy images
        columns = self.classifier._count_columns(cv2.cvtColor(self.noisy_type1_image, cv2.COLOR_BGR2GRAY))
        self.assertEqual(columns, 2)

        columns = self.classifier._count_columns(cv2.cvtColor(self.noisy_type2_image, cv2.COLOR_BGR2GRAY))
        self.assertEqual(columns, 3)

        columns = self.classifier._count_columns(cv2.cvtColor(self.noisy_type3_image, cv2.COLOR_BGR2GRAY))
        self.assertEqual(columns, 1)

    def test_header_footer_detection(self):
        """Test detection and exclusion of headers and footers."""
        # Convert to grayscale
        gray = cv2.cvtColor(self.header_footer_image, cv2.COLOR_BGR2GRAY)

        # Test header detection if the method exists
        if hasattr(self.classifier, '_detect_headers_footers'):
            headers, footers = self.classifier._detect_headers_footers(gray)
            self.assertIsNotNone(headers)
            self.assertIsNotNone(footers)

            # Check that headers are in the top portion of the image
            for header in headers:
                x, y, w, h = header
                self.assertLess(y + h, gray.shape[0] // 3)

            # Check that footers are in the bottom portion of the image
            for footer in footers:
                x, y, w, h = footer
                self.assertGreater(y, gray.shape[0] * 2 // 3)

        # Test content extraction if the method exists
        if hasattr(self.classifier, '_extract_content_excluding_headers_footers'):
            content = self.classifier._extract_content_excluding_headers_footers(gray)
            self.assertIsNotNone(content)

            # Content should be smaller than the original image
            self.assertLess(content.shape[0], gray.shape[0])

    def test_table_border_detection(self):
        """Test detection of table borders."""
        # Convert to grayscale
        gray = cv2.cvtColor(self.table_borders_image, cv2.COLOR_BGR2GRAY)

        # Test table border detection if the method exists
        if hasattr(self.classifier, '_detect_table_borders'):
            borders = self.classifier._detect_table_borders(gray)
            self.assertIsNotNone(borders)
            self.assertGreater(len(borders), 0)

            # Save visualization of detected borders
            vis_img = self.table_borders_image.copy()
            for line in borders:
                x1, y1, x2, y2 = line
                cv2.line(vis_img, (x1, y1), (x2, y2), (0, 0, 255), 2)

            save_image(vis_img, self.test_data_dir / "detected_borders.png")

    def test_classification_confidence(self):
        """Test classification confidence scores."""
        # Test confidence scoring if the method exists
        if hasattr(self.classifier, 'classify_document_with_confidence'):
            # Type1 document
            doc_type, confidence = self.classifier.classify_document_with_confidence(self.type1_image)
            self.assertEqual(doc_type, DOCUMENT_TYPE1)
            self.assertGreaterEqual(confidence, 0.0)
            self.assertLessEqual(confidence, 1.0)

            # Type2 document
            doc_type, confidence = self.classifier.classify_document_with_confidence(self.type2_image)
            self.assertEqual(doc_type, DOCUMENT_TYPE2)
            self.assertGreaterEqual(confidence, 0.0)
            self.assertLessEqual(confidence, 1.0)

            # Type3 document
            doc_type, confidence = self.classifier.classify_document_with_confidence(self.type3_image)
            self.assertEqual(doc_type, DOCUMENT_TYPE3)
            self.assertGreaterEqual(confidence, 0.0)
            self.assertLessEqual(confidence, 1.0)

    def test_rotated_document(self):
        """Test classification of rotated documents."""
        # Rotate type2 document by 90 degrees
        rotated_90 = cv2.rotate(self.type2_image, cv2.ROTATE_90_CLOCKWISE)
        save_image(rotated_90, self.test_data_dir / "rotated_90.png")

        # Rotate type2 document by 180 degrees
        rotated_180 = cv2.rotate(self.type2_image, cv2.ROTATE_180)
        save_image(rotated_180, self.test_data_dir / "rotated_180.png")

        # Test classification of rotated documents
        # Note: This may fail if the classifier doesn't handle rotation
        # In that case, we should implement rotation detection and correction
        try:
            doc_type = self.classifier.classify_document(rotated_180)
            self.assertEqual(doc_type, DOCUMENT_TYPE2)
        except AssertionError:
            self.skipTest("Classifier doesn't handle 180-degree rotation")

    def test_mixed_format_document(self):
        """Test classification of documents with mixed formats."""
        # Create a mixed format document (type1 on top, type2 on bottom)
        h1, w1 = self.type1_image.shape[:2]
        h2, w2 = self.type2_image.shape[:2]

        # Resize to same width if needed
        if w1 != w2:
            self.type1_image = cv2.resize(self.type1_image, (w2, h1))
            h1, w1 = self.type1_image.shape[:2]

        # Combine images
        mixed_image = np.vstack([self.type1_image[:h1//2], self.type2_image[h2//2:]])
        save_image(mixed_image, self.test_data_dir / "mixed_format.png")

        # Test classification
        # This is a challenging case - we're testing how the classifier handles ambiguity
        doc_type = self.classifier.classify_document(mixed_image)
        # We don't assert a specific type, just log the result
        self.logger.info(f"Mixed format document classified as: {doc_type}")

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory and all its contents
        cleanup_temp_directory(cls.temp_dir)

if __name__ == "__main__":
    unittest.main()
