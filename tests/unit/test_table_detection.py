"""Unit tests for table detection and cropping."""
import unittest
import numpy as np
import cv2
import os
import logging
from pathlib import Path
import tempfile
from unittest.mock import patch, MagicMock

from jortextractor.core.pipeline import JortPDFProcessor
from jortextractor.core.image_processor import detect_text_regions
from tests.test_utils import (
    create_temp_directory, cleanup_temp_directory, create_test_image,
    add_text_to_image, add_rectangle_to_image, add_line_to_image,
    create_sample_with_table_borders, save_image
)

class TestTableDetection(unittest.TestCase):
    """Test cases for table detection and cropping."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()
        cls.test_data_dir = cls.temp_dir / "test_data"
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a logger for testing
        self.logger = logging.getLogger("test_table_detection")
        self.logger.setLevel(logging.DEBUG)

        # Create a processor with the test logger
        self.processor = JortPDFProcessor()

        # Create sample images for testing
        self.table_borders_image = create_sample_with_table_borders()
        save_image(self.table_borders_image, self.test_data_dir / "table_borders_sample.png")

        # Create a simple table image
        self.simple_table = create_test_image(800, 600)
        # Add table borders
        for y in range(100, 500, 100):
            self.simple_table = add_line_to_image(self.simple_table, (100, y), (700, y), (0, 0, 0), 2)
        for x in range(100, 800, 200):
            self.simple_table = add_line_to_image(self.simple_table, (x, 100), (x, 500), (0, 0, 0), 2)
        # Add some text
        for row in range(4):
            for col in range(3):
                y = 100 + row * 100 + 50
                x = 100 + col * 200 + 50
                self.simple_table = add_text_to_image(self.simple_table, f"Cell {row},{col}", (x, y), 0.6, (0, 0, 0), 1)
        save_image(self.simple_table, self.test_data_dir / "simple_table.png")

        # Create a multi-column layout
        self.multi_column = create_test_image(800, 600)
        # Add columns
        for col in range(3):
            x = 100 + col * 250
            for y in range(100, 500, 30):
                self.multi_column = add_rectangle_to_image(self.multi_column, (x, y), (x + 200, y + 20), (200, 200, 200))
        save_image(self.multi_column, self.test_data_dir / "multi_column.png")

        # Create a complex layout with multiple tables
        self.complex_layout = create_test_image(800, 1000)
        # Add header
        self.complex_layout = add_rectangle_to_image(self.complex_layout, (0, 0), (800, 50), (220, 220, 220))
        self.complex_layout = add_text_to_image(self.complex_layout, "DOCUMENT TITLE", (300, 30), 0.8, (0, 0, 0), 2)
        # Add first table
        for y in range(100, 300, 50):
            self.complex_layout = add_line_to_image(self.complex_layout, (100, y), (700, y), (0, 0, 0), 2)
        for x in range(100, 800, 200):
            self.complex_layout = add_line_to_image(self.complex_layout, (x, 100), (x, 300), (0, 0, 0), 2)
        # Add second table
        for y in range(400, 600, 50):
            self.complex_layout = add_line_to_image(self.complex_layout, (100, y), (700, y), (0, 0, 0), 2)
        for x in range(100, 800, 200):
            self.complex_layout = add_line_to_image(self.complex_layout, (x, 400), (x, 600), (0, 0, 0), 2)
        # Add text section
        for y in range(650, 950, 30):
            self.complex_layout = add_rectangle_to_image(self.complex_layout, (200, y), (600, y + 20), (200, 200, 200))
        save_image(self.complex_layout, self.test_data_dir / "complex_layout.png")

    def test_table_border_detection(self):
        """Test detection of table borders."""
        # Convert to grayscale
        gray = cv2.cvtColor(self.table_borders_image, cv2.COLOR_BGR2GRAY)

        # Apply thresholding
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Detect horizontal lines
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
        detected_horizontal = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)

        # Detect vertical lines
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
        detected_vertical = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, vertical_kernel, iterations=2)

        # Combine horizontal and vertical lines
        table_mask = cv2.add(detected_horizontal, detected_vertical)

        # Find contours
        contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Check that contours were found
        self.assertGreater(len(contours), 0, "No table contours found")

        # Visualize the detected table
        result_img = self.table_borders_image.copy()
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            cv2.rectangle(result_img, (x, y), (x + w, y + h), (0, 255, 0), 2)

        save_image(result_img, self.test_data_dir / "detected_table_borders.png")

    def test_text_region_detection(self):
        """Test detection of text regions."""
        # Detect text regions in the multi-column layout
        regions = detect_text_regions(self.multi_column)

        # Check that regions were found
        self.assertGreater(len(regions), 0, "No text regions found")

        # Visualize the detected regions
        result_img = self.multi_column.copy()
        for x, y, w, h in regions:
            cv2.rectangle(result_img, (x, y), (x + w, y + h), (0, 255, 0), 2)

        save_image(result_img, self.test_data_dir / "detected_text_regions.png")

        # Check that the number of regions is reasonable
        # For a 3-column layout, we expect at least 3 regions
        self.assertGreaterEqual(len(regions), 3, "Too few text regions detected")

    def test_table_extraction_from_image(self):
        """Test extraction of tables from an image."""
        # Extract tables from the simple table image
        tables = self.processor._extract_tables_from_image(self.simple_table, 1)

        # Check that tables were found
        self.assertGreater(len(tables), 0, "No tables extracted")

        # Check the structure of the extracted tables
        for table in tables:
            self.assertIn('page', table, "Table missing 'page' field")
            self.assertIn('table_num_id', table, "Table missing 'table_num_id' field")
            self.assertIn('data', table, "Table missing 'data' field")
            self.assertIn('bbox', table, "Table missing 'bbox' field")

            # Check that the table has data
            self.assertGreater(len(table['data']), 0, "Table has no data")

            # Check the structure of the data
            for row in table['data']:
                self.assertIn('text', row, "Row missing 'text' field")
                self.assertIn('bbox', row, "Row missing 'bbox' field")

    def test_multi_column_detection(self):
        """Test detection of multi-column layouts."""
        # Check if the multi-column layout is detected as multi-column
        is_multi_column = self.processor._check_if_multi_column(self.multi_column)

        # It should be detected as multi-column
        self.assertTrue(is_multi_column, "Multi-column layout not detected")

        # Check if the simple table is not detected as multi-column
        is_multi_column = self.processor._check_if_multi_column(self.simple_table)

        # It might be detected as multi-column due to the table structure
        # This is acceptable behavior

        # Check if a blank image is not detected as multi-column
        blank_img = create_test_image(800, 600)
        is_multi_column = self.processor._check_if_multi_column(blank_img)

        # It should not be detected as multi-column
        self.assertFalse(is_multi_column, "Blank image incorrectly detected as multi-column")

    def test_complex_layout_processing(self):
        """Test processing of complex layouts with multiple tables."""
        # Extract tables from the complex layout
        tables = self.processor._extract_tables_from_image(self.complex_layout, 1)

        # Check that tables were found
        self.assertGreater(len(tables), 0, "No tables extracted from complex layout")

        # Visualize the extracted tables
        result_img = self.complex_layout.copy()
        for table in tables:
            x, y, w, h = table['bbox']
            cv2.rectangle(result_img, (x, y), (x + w, y + h), (0, 255, 0), 2)

            # Add table ID
            cv2.putText(result_img, table['table_num_id'], (x, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

        save_image(result_img, self.test_data_dir / "extracted_tables_complex.png")

        # Check that multiple tables were found
        # The complex layout has at least 2 tables
        self.assertGreaterEqual(len(tables), 1, "Too few tables extracted from complex layout")

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory and all its contents
        cleanup_temp_directory(cls.temp_dir)

if __name__ == "__main__":
    unittest.main()
