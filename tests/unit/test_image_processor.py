"""Enhanced unit tests for the image processor module."""
import unittest
import numpy as np
import cv2
import os
import logging
from pathlib import Path
import tempfile
from unittest.mock import patch, MagicMock

from jortextractor.core.image_processor import optimize_for_name_ocr, detect_text_regions
from tests.test_utils import (
    create_test_image, add_text_to_image, add_rectangle_to_image, add_line_to_image,
    add_noise_to_image, save_image, create_temp_directory, cleanup_temp_directory,
    create_sample_with_table_borders
)

class TestImageProcessor(unittest.TestCase):
    """Enhanced test cases for the image processing functions."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()
        cls.test_data_dir = cls.temp_dir / "test_data"
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a logger for testing
        self.logger = logging.getLogger("test_image_processor")
        self.logger.setLevel(logging.DEBUG)

        # Create sample images for testing
        self.clean_image = self._create_clean_image()
        self.noisy_image = self._create_noisy_image()
        self.multi_column_image = self._create_multi_column_image()
        self.low_contrast_image = self._create_low_contrast_image()
        self.skewed_image = self._create_skewed_image()
        self.blurry_image = self._create_blurry_image()
        self.table_borders_image = create_sample_with_table_borders()

        # Save the images for inspection
        save_image(self.clean_image, self.test_data_dir / "clean_image.png")
        save_image(self.noisy_image, self.test_data_dir / "noisy_image.png")
        save_image(self.multi_column_image, self.test_data_dir / "multi_column_image.png")
        save_image(self.low_contrast_image, self.test_data_dir / "low_contrast_image.png")
        save_image(self.skewed_image, self.test_data_dir / "skewed_image.png")
        save_image(self.blurry_image, self.test_data_dir / "blurry_image.png")
        save_image(self.table_borders_image, self.test_data_dir / "table_borders_image.png")

    def _create_clean_image(self):
        """Create a clean sample image with text."""
        # Create a blank image (800x600)
        img = create_test_image(800, 600)

        # Add some text
        img = add_text_to_image(img, "John Smith", (50, 50), 1.0, (0, 0, 0), 2)
        img = add_text_to_image(img, "Jane Doe", (50, 100), 1.0, (0, 0, 0), 2)
        img = add_text_to_image(img, "Robert Johnson", (50, 150), 1.0, (0, 0, 0), 2)

        return img

    def _create_noisy_image(self):
        """Create a noisy sample image with text."""
        # Create a blank image with slightly gray background
        img = create_test_image(800, 600, (240, 240, 240))

        # Add some text
        img = add_text_to_image(img, "John Smith", (50, 50), 1.0, (30, 30, 30), 2)
        img = add_text_to_image(img, "Jane Doe", (50, 100), 1.0, (30, 30, 30), 2)
        img = add_text_to_image(img, "Robert Johnson", (50, 150), 1.0, (30, 30, 30), 2)

        # Add noise
        img = add_noise_to_image(img, 30)

        # Add some artifacts
        for _ in range(20):
            x = np.random.randint(0, img.shape[1])
            y = np.random.randint(0, img.shape[0])
            cv2.circle(img, (x, y), 2, (200, 200, 200), -1)

        return img

    def _create_multi_column_image(self):
        """Create a multi-column sample image."""
        # Create a blank image (800x600)
        img = create_test_image(800, 600)

        # Add column 1
        img = add_text_to_image(img, "John Smith", (50, 50), 0.8, (0, 0, 0), 2)
        img = add_text_to_image(img, "Jane Doe", (50, 100), 0.8, (0, 0, 0), 2)
        img = add_text_to_image(img, "Robert Johnson", (50, 150), 0.8, (0, 0, 0), 2)

        # Add column 2
        img = add_text_to_image(img, "Alice Brown", (400, 50), 0.8, (0, 0, 0), 2)
        img = add_text_to_image(img, "Bob Williams", (400, 100), 0.8, (0, 0, 0), 2)
        img = add_text_to_image(img, "Carol Davis", (400, 150), 0.8, (0, 0, 0), 2)

        return img

    def _create_low_contrast_image(self):
        """Create a low contrast sample image with text."""
        # Create a blank image with light gray background
        img = create_test_image(800, 600, (220, 220, 220))

        # Add some text with dark gray color (low contrast)
        img = add_text_to_image(img, "John Smith", (50, 50), 1.0, (180, 180, 180), 2)
        img = add_text_to_image(img, "Jane Doe", (50, 100), 1.0, (180, 180, 180), 2)
        img = add_text_to_image(img, "Robert Johnson", (50, 150), 1.0, (180, 180, 180), 2)

        return img

    def _create_skewed_image(self):
        """Create a skewed sample image with text."""
        # Create a clean image first
        img = self._create_clean_image()

        # Define the skew transformation
        rows, cols = img.shape[:2]
        skew_factor = 0.2
        M = np.float32([[1, skew_factor, 0], [0, 1, 0]])

        # Apply the skew transformation
        skewed = cv2.warpAffine(img, M, (cols, rows))

        return skewed

    def _create_blurry_image(self):
        """Create a blurry sample image with text."""
        # Create a clean image first
        img = self._create_clean_image()

        # Apply Gaussian blur
        blurry = cv2.GaussianBlur(img, (7, 7), 3)

        return blurry

    def test_optimize_for_name_ocr_clean(self):
        """Test image optimization on clean image."""
        # Convert to grayscale for processing
        gray = cv2.cvtColor(self.clean_image, cv2.COLOR_BGR2GRAY)

        # Optimize the image
        optimized = optimize_for_name_ocr(gray)

        # Check that the output is a valid image
        self.assertIsInstance(optimized, np.ndarray)
        self.assertEqual(len(optimized.shape), 2)  # Should be grayscale
        self.assertEqual(optimized.shape, gray.shape)

        # Save the optimized image for visual inspection
        save_image(optimized, self.test_data_dir / "optimized_clean.png")

    def test_optimize_for_name_ocr_noisy(self):
        """Test image optimization on noisy image."""
        # Convert to grayscale for processing
        gray = cv2.cvtColor(self.noisy_image, cv2.COLOR_BGR2GRAY)

        # Optimize the image
        optimized = optimize_for_name_ocr(gray)

        # Check that the output is a valid image
        self.assertIsInstance(optimized, np.ndarray)
        self.assertEqual(len(optimized.shape), 2)  # Should be grayscale
        self.assertEqual(optimized.shape, gray.shape)

        # Save the optimized image for visual inspection
        save_image(optimized, self.test_data_dir / "optimized_noisy.png")

        # Check that optimization improved contrast
        # Calculate standard deviation as a measure of contrast
        std_before = np.std(gray)
        std_after = np.std(optimized)

        # Optimized image should have better contrast
        self.assertGreaterEqual(std_after, std_before * 0.8)

    def test_optimize_for_name_ocr_low_contrast(self):
        """Test image optimization on low contrast image."""
        # Convert to grayscale for processing
        gray = cv2.cvtColor(self.low_contrast_image, cv2.COLOR_BGR2GRAY)

        # Optimize the image
        optimized = optimize_for_name_ocr(gray)

        # Save the optimized image for visual inspection
        save_image(optimized, self.test_data_dir / "optimized_low_contrast.png")

        # Check that optimization improved contrast
        # Calculate standard deviation as a measure of contrast
        std_before = np.std(gray)
        std_after = np.std(optimized)

        # Optimized image should have significantly better contrast
        self.assertGreater(std_after, std_before * 1.2)

    def test_optimize_for_name_ocr_skewed(self):
        """Test image optimization on skewed image."""
        # Convert to grayscale for processing
        gray = cv2.cvtColor(self.skewed_image, cv2.COLOR_BGR2GRAY)

        # Optimize the image
        optimized = optimize_for_name_ocr(gray)

        # Save the optimized image for visual inspection
        save_image(optimized, self.test_data_dir / "optimized_skewed.png")

        # Check that the output is a valid image
        self.assertIsInstance(optimized, np.ndarray)
        self.assertEqual(len(optimized.shape), 2)  # Should be grayscale
        self.assertEqual(optimized.shape, gray.shape)

    def test_optimize_for_name_ocr_blurry(self):
        """Test image optimization on blurry image."""
        # Convert to grayscale for processing
        gray = cv2.cvtColor(self.blurry_image, cv2.COLOR_BGR2GRAY)

        # Optimize the image
        optimized = optimize_for_name_ocr(gray)

        # Save the optimized image for visual inspection
        save_image(optimized, self.test_data_dir / "optimized_blurry.png")

        # Check that the output is a valid image
        self.assertIsInstance(optimized, np.ndarray)
        self.assertEqual(len(optimized.shape), 2)  # Should be grayscale
        self.assertEqual(optimized.shape, gray.shape)

        # Check if sharpening was applied (higher standard deviation of Laplacian)
        laplacian_before = cv2.Laplacian(gray, cv2.CV_64F).var()
        laplacian_after = cv2.Laplacian(optimized, cv2.CV_64F).var()

        # Log the values for inspection
        self.logger.info(f"Laplacian variance before: {laplacian_before}, after: {laplacian_after}")

    def test_detect_text_regions(self):
        """Test text region detection."""
        # Detect regions in multi-column image
        regions = detect_text_regions(self.multi_column_image)

        # Should detect at least 2 regions (columns)
        self.assertGreaterEqual(len(regions), 2)

        # Each region should be a tuple of (x, y, w, h)
        for region in regions:
            self.assertEqual(len(region), 4)
            x, y, w, h = region
            self.assertGreaterEqual(w, 10)  # Should have reasonable width
            self.assertGreaterEqual(h, 10)  # Should have reasonable height

        # Create a visualization of detected regions
        vis_img = self.multi_column_image.copy()
        for x, y, w, h in regions:
            cv2.rectangle(vis_img, (x, y), (x+w, y+h), (0, 255, 0), 2)

        # Save the visualization
        save_image(vis_img, self.test_data_dir / "detected_regions.png")

    def test_detect_text_regions_noisy(self):
        """Test text region detection on noisy image."""
        # Detect regions in noisy image
        regions = detect_text_regions(self.noisy_image)

        # Should detect at least 1 region
        self.assertGreaterEqual(len(regions), 1)

        # Create a visualization of detected regions
        vis_img = self.noisy_image.copy()
        for x, y, w, h in regions:
            cv2.rectangle(vis_img, (x, y), (x+w, y+h), (0, 255, 0), 2)

        # Save the visualization
        save_image(vis_img, self.test_data_dir / "detected_regions_noisy.png")

    def test_detect_table_borders(self):
        """Test detection of table borders."""
        # Check if the function exists
        if hasattr(cv2, 'detect_table_borders'):
            # Detect table borders
            borders = cv2.detect_table_borders(self.table_borders_image)

            # Should detect some borders
            self.assertGreaterEqual(len(borders), 1)

            # Create a visualization of detected borders
            vis_img = self.table_borders_image.copy()
            for line in borders:
                x1, y1, x2, y2 = line
                cv2.line(vis_img, (x1, y1), (x2, y2), (0, 0, 255), 2)

            # Save the visualization
            save_image(vis_img, self.test_data_dir / "detected_table_borders.png")
        else:
            self.skipTest("detect_table_borders function not available")

    def test_preprocessing_pipeline(self):
        """Test the complete preprocessing pipeline."""
        # Check if the function exists
        if hasattr(cv2, 'preprocess_for_ocr'):
            # Process the noisy image
            processed = cv2.preprocess_for_ocr(self.noisy_image)

            # Should return a valid image
            self.assertIsInstance(processed, np.ndarray)

            # Save the processed image
            save_image(processed, self.test_data_dir / "preprocessed_noisy.png")
        else:
            # If the function doesn't exist, test our own pipeline
            # Convert to grayscale
            gray = cv2.cvtColor(self.noisy_image, cv2.COLOR_BGR2GRAY)

            # Apply preprocessing steps
            # 1. Denoise
            denoised = cv2.fastNlMeansDenoising(gray, None, 10, 7, 21)

            # 2. Optimize contrast
            optimized = optimize_for_name_ocr(denoised)

            # Save intermediate results
            save_image(denoised, self.test_data_dir / "denoised.png")
            save_image(optimized, self.test_data_dir / "optimized.png")

    def test_different_resolutions(self):
        """Test image processing with different resolutions."""
        # Create images with different resolutions
        resolutions = [(400, 300), (800, 600), (1200, 900)]

        for width, height in resolutions:
            # Create a clean image with the specified resolution
            img = create_test_image(width, height)
            img = add_text_to_image(img, "John Smith", (width//16, height//12), width/800, (0, 0, 0), 2)

            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # Optimize the image
            optimized = optimize_for_name_ocr(gray)

            # Check that the output has the correct resolution
            self.assertEqual(optimized.shape, (height, width))

            # Save the optimized image
            save_image(optimized, self.test_data_dir / f"optimized_{width}x{height}.png")

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory and all its contents
        cleanup_temp_directory(cls.temp_dir)

if __name__ == "__main__":
    unittest.main()
