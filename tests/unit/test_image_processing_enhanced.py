"""Enhanced unit tests for image processing."""
import unittest
import numpy as np
import cv2
import os
import logging
from pathlib import Path
import tempfile
from unittest.mock import patch, MagicMock

from jortextractor.core.image_processor import (
    optimize_for_name_ocr, detect_text_regions
)
from tests.test_utils import (
    create_temp_directory, cleanup_temp_directory, create_test_image,
    add_text_to_image, add_noise_to_image, save_image
)

class TestImageProcessingEnhanced(unittest.TestCase):
    """Enhanced test cases for image processing."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()
        cls.test_data_dir = cls.temp_dir / "test_data"
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a logger for testing
        self.logger = logging.getLogger("test_image_processing")
        self.logger.setLevel(logging.DEBUG)

        # Create sample images for testing
        self.clean_image = create_test_image(800, 600)
        self.clean_image = add_text_to_image(self.clean_image, "MOHAMED BEN ALI", (300, 300), 0.8, (0, 0, 0), 2)
        save_image(self.clean_image, self.test_data_dir / "clean_image.png")

        # Create noisy image
        self.noisy_image = add_noise_to_image(self.clean_image, 30)
        save_image(self.noisy_image, self.test_data_dir / "noisy_image.png")

        # Create low contrast image
        self.low_contrast_image = self.clean_image.copy()
        self.low_contrast_image = cv2.convertScaleAbs(self.low_contrast_image, alpha=0.5, beta=50)
        save_image(self.low_contrast_image, self.test_data_dir / "low_contrast_image.png")

        # Create blurry image
        self.blurry_image = cv2.GaussianBlur(self.clean_image, (15, 15), 0)
        save_image(self.blurry_image, self.test_data_dir / "blurry_image.png")

        # Create skewed image
        rows, cols = self.clean_image.shape[:2]
        M = cv2.getRotationMatrix2D((cols/2, rows/2), 10, 1)
        self.skewed_image = cv2.warpAffine(self.clean_image, M, (cols, rows))
        save_image(self.skewed_image, self.test_data_dir / "skewed_image.png")

        # Create multi-text image
        self.multi_text_image = create_test_image(800, 600)
        self.multi_text_image = add_text_to_image(self.multi_text_image, "MOHAMED BEN ALI", (100, 100), 0.8, (0, 0, 0), 2)
        self.multi_text_image = add_text_to_image(self.multi_text_image, "FATIMA BENT AHMED", (100, 200), 0.8, (0, 0, 0), 2)
        self.multi_text_image = add_text_to_image(self.multi_text_image, "ABDEL KARIM EL MANSOURI", (100, 300), 0.8, (0, 0, 0), 2)
        self.multi_text_image = add_text_to_image(self.multi_text_image, "LEILA TRABELSI", (100, 400), 0.8, (0, 0, 0), 2)
        self.multi_text_image = add_text_to_image(self.multi_text_image, "YOUSSEF OULD MOHAMED", (100, 500), 0.8, (0, 0, 0), 2)
        save_image(self.multi_text_image, self.test_data_dir / "multi_text_image.png")

    def test_optimize_for_name_ocr_clean_image(self):
        """Test image optimization for OCR with clean image."""
        # Optimize the clean image
        optimized_img = optimize_for_name_ocr(self.clean_image)

        # Save the optimized image
        save_image(optimized_img, self.test_data_dir / "optimized_clean_image.png")

        # Check that the optimized image has the same dimensions
        self.assertEqual(optimized_img.shape, self.clean_image.shape)

        # Check that the optimized image is not identical to the original
        # (optimization should have changed the image)
        self.assertFalse(np.array_equal(optimized_img, self.clean_image))

    def test_optimize_for_name_ocr_noisy_image(self):
        """Test image optimization for OCR with noisy image."""
        # Optimize the noisy image
        optimized_img = optimize_for_name_ocr(self.noisy_image)

        # Save the optimized image
        save_image(optimized_img, self.test_data_dir / "optimized_noisy_image.png")

        # Check that the optimized image has the same dimensions
        self.assertEqual(optimized_img.shape, self.noisy_image.shape)

        # Check that the optimized image is not identical to the original
        # (optimization should have changed the image)
        self.assertFalse(np.array_equal(optimized_img, self.noisy_image))

        # Check that the optimized image has less noise
        # (standard deviation of pixel values should be lower)
        orig_std = np.std(cv2.cvtColor(self.noisy_image, cv2.COLOR_BGR2GRAY))
        opt_std = np.std(cv2.cvtColor(optimized_img, cv2.COLOR_BGR2GRAY))
        self.assertNotEqual(orig_std, opt_std)

    def test_optimize_for_name_ocr_low_contrast_image(self):
        """Test image optimization for OCR with low contrast image."""
        # Optimize the low contrast image
        optimized_img = optimize_for_name_ocr(self.low_contrast_image)

        # Save the optimized image
        save_image(optimized_img, self.test_data_dir / "optimized_low_contrast_image.png")

        # Check that the optimized image has the same dimensions
        self.assertEqual(optimized_img.shape, self.low_contrast_image.shape)

        # Check that the optimized image is not identical to the original
        # (optimization should have changed the image)
        self.assertFalse(np.array_equal(optimized_img, self.low_contrast_image))

        # Check that the optimized image has higher contrast
        # (standard deviation of pixel values should be higher)
        orig_std = np.std(cv2.cvtColor(self.low_contrast_image, cv2.COLOR_BGR2GRAY))
        opt_std = np.std(cv2.cvtColor(optimized_img, cv2.COLOR_BGR2GRAY))
        self.assertNotEqual(orig_std, opt_std)

    def test_optimize_for_name_ocr_blurry_image(self):
        """Test image optimization for OCR with blurry image."""
        # Optimize the blurry image
        optimized_img = optimize_for_name_ocr(self.blurry_image)

        # Save the optimized image
        save_image(optimized_img, self.test_data_dir / "optimized_blurry_image.png")

        # Check that the optimized image has the same dimensions
        self.assertEqual(optimized_img.shape, self.blurry_image.shape)

        # Check that the optimized image is not identical to the original
        # (optimization should have changed the image)
        self.assertFalse(np.array_equal(optimized_img, self.blurry_image))

    def test_optimize_for_name_ocr_skewed_image(self):
        """Test image optimization for OCR with skewed image."""
        # Optimize the skewed image
        optimized_img = optimize_for_name_ocr(self.skewed_image)

        # Save the optimized image
        save_image(optimized_img, self.test_data_dir / "optimized_skewed_image.png")

        # Check that the optimized image has the same dimensions
        self.assertEqual(optimized_img.shape, self.skewed_image.shape)

        # Check that the optimized image is not identical to the original
        # (optimization should have changed the image)
        self.assertFalse(np.array_equal(optimized_img, self.skewed_image))

    def test_detect_text_regions(self):
        """Test detection of text regions."""
        # Detect text regions in the multi-text image
        regions = detect_text_regions(self.multi_text_image)

        # Check that regions were found
        self.assertGreater(len(regions), 0)

        # Visualize the detected regions
        result_img = self.multi_text_image.copy()
        for i, (x, y, w, h) in enumerate(regions):
            cv2.rectangle(result_img, (x, y), (x + w, y + h), (0, 255, 0), 2)
            cv2.putText(result_img, f"Region {i+1}", (x, y - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

        save_image(result_img, self.test_data_dir / "detected_text_regions.png")

        # Check that each region has a reasonable size
        for x, y, w, h in regions:
            self.assertGreater(w, 10, "Region width is too small")
            self.assertGreater(h, 10, "Region height is too small")
            self.assertLess(w, self.multi_text_image.shape[1], "Region width is too large")
            self.assertLess(h, self.multi_text_image.shape[0], "Region height is too large")

    def test_detect_text_regions_empty_image(self):
        """Test detection of text regions in an empty image."""
        # Create an empty image
        empty_img = create_test_image(800, 600)

        # Detect text regions
        regions = detect_text_regions(empty_img)

        # Check that no regions were found
        self.assertEqual(len(regions), 0)

    def test_detect_text_regions_with_noise(self):
        """Test detection of text regions in a noisy image."""
        # Add noise to the multi-text image
        noisy_multi_text = add_noise_to_image(self.multi_text_image, 30)
        save_image(noisy_multi_text, self.test_data_dir / "noisy_multi_text_image.png")

        # Detect text regions
        regions = detect_text_regions(noisy_multi_text)

        # Check that regions were found
        self.assertGreater(len(regions), 0)

        # Visualize the detected regions
        result_img = noisy_multi_text.copy()
        for i, (x, y, w, h) in enumerate(regions):
            cv2.rectangle(result_img, (x, y), (x + w, y + h), (0, 255, 0), 2)
            cv2.putText(result_img, f"Region {i+1}", (x, y - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

        save_image(result_img, self.test_data_dir / "detected_text_regions_noisy.png")

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory and all its contents
        cleanup_temp_directory(cls.temp_dir)

if __name__ == "__main__":
    unittest.main()
