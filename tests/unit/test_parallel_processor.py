"""Unit tests for the parallel processor module."""
import unittest
import tempfile
import shutil
import time
import os
import psutil
from pathlib import Path
from unittest.mock import patch, MagicMock
from jortextractor.parallel.parallel_processor import ParallelProcessor
from jortextractor.core.pipeline import JortPDFProcessor

class TestParallelProcessor(unittest.TestCase):
    """Test cases for the ParallelProcessor class."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = Path(tempfile.mkdtemp())

        # Create a directory for test data
        cls.test_data_dir = Path("tests/test_data")
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

        # Path to example PDFs
        cls.example_pdfs_dir = Path("ProjectContext/DataExamples")

    def setUp(self):
        """Set up test fixtures."""
        self.processor = ParallelProcessor(temp_dir=self.temp_dir)

    def test_init(self):
        """Test initialization of ParallelProcessor."""
        # Test with default parameters
        processor = ParallelProcessor()
        self.assertEqual(processor.max_workers, os.cpu_count())
        self.assertEqual(processor.memory_limit_percent, 80.0)
        self.assertIsNone(processor.temp_dir)

        # Test with custom parameters
        processor = ParallelProcessor(
            max_workers=2,
            memory_limit_percent=70.0,
            temp_dir=self.temp_dir
        )
        self.assertEqual(processor.max_workers, 2)
        self.assertEqual(processor.memory_limit_percent, 70.0)
        self.assertEqual(processor.temp_dir, self.temp_dir)

    @patch('concurrent.futures.ProcessPoolExecutor')
    def test_process_pdfs(self, mock_executor):
        """Test processing multiple PDFs."""
        # Mock the executor and future
        mock_future = MagicMock()
        mock_future.result.return_value = {'status': 'success', 'extracted_data': []}

        # Mock the executor's submit method to return the mock future
        mock_executor_instance = MagicMock()
        mock_executor_instance.submit.return_value = mock_future
        mock_executor.return_value.__enter__.return_value = mock_executor_instance

        # Create test PDF paths
        pdf_paths = [
            Path("test1.pdf"),
            Path("test2.pdf"),
            Path("test3.pdf")
        ]

        # Process PDFs
        results = self.processor.process_pdfs(pdf_paths)

        # Check that submit was called for each PDF
        self.assertEqual(mock_executor_instance.submit.call_count, 3)

        # Check that results were collected
        self.assertEqual(len(results), 3)

    @patch('psutil.virtual_memory')
    @patch('concurrent.futures.ProcessPoolExecutor')
    def test_resource_monitoring(self, mock_executor, mock_virtual_memory):
        """Test resource monitoring during processing."""
        # Mock memory usage
        mock_memory = MagicMock()
        mock_memory.percent = 85.0  # High memory usage
        mock_virtual_memory.return_value = mock_memory

        # Mock the executor and future
        mock_future = MagicMock()
        mock_future.result.return_value = {'status': 'success', 'extracted_data': []}

        # Mock the executor's submit method to return the mock future
        mock_executor_instance = MagicMock()
        mock_executor_instance.submit.return_value = mock_future
        mock_executor.return_value.__enter__.return_value = mock_executor_instance

        # Create test PDF paths
        pdf_paths = [
            Path("test1.pdf"),
            Path("test2.pdf"),
            Path("test3.pdf")
        ]

        # Create processor with high max_workers
        processor = ParallelProcessor(max_workers=4, memory_limit_percent=80.0)

        # Process PDFs
        results = processor.process_pdfs(pdf_paths)

        # Check that max_active_workers was reduced due to high memory usage
        # This is hard to test directly, but we can check that submit was still called
        self.assertGreaterEqual(mock_executor_instance.submit.call_count, 1)

    def test_callback(self):
        """Test callback feature."""
        # Create a callback function
        callback_results = []
        def callback(result):
            callback_results.append(result)

        # Mock process_single_pdf to return a result without actually processing
        def mock_process(processor, pdf_path, gt_path, output_dir):
            return {'pdf_path': pdf_path, 'status': 'success', 'extracted_data': []}

        # Patch the _process_single_pdf method
        with patch.object(ParallelProcessor, '_process_single_pdf', side_effect=mock_process):
            # Create test PDF paths
            pdf_paths = [
                Path("test1.pdf"),
                Path("test2.pdf")
            ]

            # Process PDFs with callback
            results = self.processor.process_pdfs(pdf_paths, callback=callback)

            # Check that callback was called for each PDF
            self.assertEqual(len(callback_results), 2)

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory
        shutil.rmtree(cls.temp_dir)

        # Try to remove test data directory (will only succeed if empty)
        try:
            cls.test_data_dir.rmdir()
        except:
            pass

if __name__ == "__main__":
    unittest.main()
