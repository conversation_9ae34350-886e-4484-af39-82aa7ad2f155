"""Unit tests for OCR accuracy."""
import unittest
import numpy as np
import cv2
import os
import logging
import pytesseract
from pathlib import Path
import tempfile
from unittest.mock import patch, MagicMock

from jortextractor.core.image_processor import optimize_for_name_ocr
from tests.test_utils import (
    create_temp_directory, cleanup_temp_directory, add_text_to_image,
    create_test_image, add_noise_to_image, save_image
)

class TestOCRAccuracy(unittest.TestCase):
    """Test cases for OCR accuracy."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()
        cls.test_data_dir = cls.temp_dir / "test_data"
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a logger for testing
        self.logger = logging.getLogger("test_ocr")
        self.logger.setLevel(logging.DEBUG)

        # Create sample images with known text
        self.sample_names = [
            "MOHAMED BEN ALI",
            "FATIMA BENT AHMED",
            "ABDEL KARIM EL MANSOURI",
            "LEILA TRABELSI",
            "YOUSSEF OULD MOHAMED",
            "AMINA BINT IBRAHIM",
            "SAMIR AIT AHMED",
            "NADIA EPOUSE KARIM",
            "JAMAL ABDUL NASIR",
            "SAMIRA BENT MOHAMED"
        ]

        # Create clean and noisy test images
        self.clean_images = []
        self.noisy_images = []

        for i, name in enumerate(self.sample_names):
            # Create clean image with name
            img = create_test_image(400, 100)
            img = add_text_to_image(img, name, (50, 50), 0.8, (0, 0, 0), 2)
            self.clean_images.append(img)
            save_image(img, self.test_data_dir / f"clean_name_{i}.png")

            # Create noisy version
            noisy_img = add_noise_to_image(img, 20)
            self.noisy_images.append(noisy_img)
            save_image(noisy_img, self.test_data_dir / f"noisy_name_{i}.png")

    def test_ocr_accuracy_clean_images(self):
        """Test OCR accuracy on clean images."""
        correct_count = 0

        for i, (img, expected_name) in enumerate(zip(self.clean_images, self.sample_names)):
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # Apply OCR
            text = pytesseract.image_to_string(gray, config="--psm 6").strip()

            # Log results
            self.logger.info(f"Clean image {i}: Expected: '{expected_name}', Got: '{text}'")

            # Check if the expected name is in the OCR result
            if expected_name in text:
                correct_count += 1

        # Calculate accuracy
        accuracy = correct_count / len(self.sample_names)
        self.logger.info(f"OCR accuracy on clean images: {accuracy:.2f}")

        # Assert minimum accuracy
        self.assertGreaterEqual(accuracy, 0.7, f"OCR accuracy on clean images ({accuracy:.2f}) is below 70%")

    def test_ocr_accuracy_noisy_images(self):
        """Test OCR accuracy on noisy images."""
        correct_count = 0

        for i, (img, expected_name) in enumerate(zip(self.noisy_images, self.sample_names)):
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # Apply OCR
            text = pytesseract.image_to_string(gray, config="--psm 6").strip()

            # Log results
            self.logger.info(f"Noisy image {i}: Expected: '{expected_name}', Got: '{text}'")

            # Check if the expected name is in the OCR result
            if expected_name in text:
                correct_count += 1

        # Calculate accuracy
        accuracy = correct_count / len(self.sample_names)
        self.logger.info(f"OCR accuracy on noisy images: {accuracy:.2f}")

        # Assert minimum accuracy
        self.assertGreaterEqual(accuracy, 0.5, f"OCR accuracy on noisy images ({accuracy:.2f}) is below 50%")

    def test_ocr_with_optimization(self):
        """Test OCR accuracy with image optimization."""
        correct_count = 0

        for i, (img, expected_name) in enumerate(zip(self.noisy_images, self.sample_names)):
            # Apply image optimization
            optimized_img = optimize_for_name_ocr(img)
            save_image(optimized_img, self.test_data_dir / f"optimized_name_{i}.png")

            # Apply OCR
            text = pytesseract.image_to_string(optimized_img, config="--psm 6").strip()

            # Log results
            self.logger.info(f"Optimized image {i}: Expected: '{expected_name}', Got: '{text}'")

            # Check if the expected name is in the OCR result
            if expected_name in text:
                correct_count += 1

        # Calculate accuracy
        accuracy = correct_count / len(self.sample_names)
        self.logger.info(f"OCR accuracy with optimization: {accuracy:.2f}")

        # Assert minimum accuracy
        self.assertGreaterEqual(accuracy, 0.6, f"OCR accuracy with optimization ({accuracy:.2f}) is below 60%")

    def test_ocr_with_different_psm_modes(self):
        """Test OCR accuracy with different PSM modes."""
        # Test with a subset of images
        test_images = self.clean_images[:3]
        expected_names = self.sample_names[:3]

        psm_modes = [3, 4, 6, 7, 8, 11, 12]
        results = {}

        for psm in psm_modes:
            correct_count = 0

            for img, expected_name in zip(test_images, expected_names):
                # Apply OCR with specific PSM mode
                text = pytesseract.image_to_string(img, config=f"--psm {psm}").strip()

                # Check if the expected name is in the OCR result
                if expected_name in text:
                    correct_count += 1

            # Calculate accuracy for this PSM mode
            accuracy = correct_count / len(test_images)
            results[psm] = accuracy
            self.logger.info(f"OCR accuracy with PSM {psm}: {accuracy:.2f}")

        # Find the best PSM mode
        best_psm = max(results, key=results.get)
        self.logger.info(f"Best PSM mode: {best_psm} with accuracy {results[best_psm]:.2f}")

        # Assert that at least one PSM mode has good accuracy
        self.assertGreaterEqual(max(results.values()), 0.6,
                               f"No PSM mode achieved accuracy above 60%")

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory and all its contents
        cleanup_temp_directory(cls.temp_dir)

if __name__ == "__main__":
    unittest.main()
