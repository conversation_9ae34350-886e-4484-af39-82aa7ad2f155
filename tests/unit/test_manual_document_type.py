"""Unit tests for required document type specification."""
import unittest
import os
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock
import numpy as np
import cv2

from jortextractor.core import JortPDFProcessor
from jortextractor.core.document_classifier import DOCUMENT_TYPE1, DOCUMENT_TYPE2, DOCUMENT_TYPE3, DOCUMENT_TYPE4

class TestRequiredDocumentType(unittest.TestCase):
    """Test cases for required document type specification."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary config file
        self.temp_dir = tempfile.TemporaryDirectory()
        self.config_path = Path(self.temp_dir.name) / "test_config.yaml"
        with open(self.config_path, "w") as f:
            f.write("output_dir: test_output\n")
            f.write("dpi: 300\n")
            f.write("ocr_languages: eng+fra\n")

        # Initialize processor with test config
        self.processor = JortPDFProcessor(self.config_path)

        # Mock the document classifier to ensure it's not called when manual type is provided
        self.processor.document_classifier = MagicMock()

    def tearDown(self):
        """Clean up test fixtures."""
        self.temp_dir.cleanup()

    @patch('jortextractor.core.pipeline.convert_from_path')
    @patch('jortextractor.core.pipeline.JortPDFProcessor._process_type2_pdf')
    def test_manual_type2_specification(self, mock_process_type2, mock_convert):
        """Test that manually specifying type2 bypasses classification and uses the correct processor."""
        # Setup mocks
        mock_convert.return_value = []  # Should not be called
        mock_process_type2.return_value = {'extracted_data': MagicMock(), 'status': 'success'}

        # Create a dummy PDF path
        pdf_path = Path(self.temp_dir.name) / "test.pdf"
        with open(pdf_path, "wb") as f:
            f.write(b"%PDF-1.4\n")  # Minimal PDF header

        # Process with document type
        self.processor.process_pdf(pdf_path, DOCUMENT_TYPE2)

        # Verify that the document classifier was not called
        self.processor.document_classifier.classify_document.assert_not_called()

        # Verify that the correct processor was called
        mock_process_type2.assert_called_once()

    @patch('jortextractor.core.pipeline.convert_from_path')
    @patch('jortextractor.core.pipeline.JortPDFProcessor._process_type4_pdf')
    def test_manual_type4_specification(self, mock_process_type4, mock_convert):
        """Test that manually specifying type4 bypasses classification and uses the correct processor."""
        # Setup mocks
        mock_convert.return_value = []  # Should not be called
        mock_process_type4.return_value = {'extracted_data': MagicMock(), 'status': 'success'}

        # Create a dummy PDF path
        pdf_path = Path(self.temp_dir.name) / "test.pdf"
        with open(pdf_path, "wb") as f:
            f.write(b"%PDF-1.4\n")  # Minimal PDF header

        # Process with document type
        self.processor.process_pdf(pdf_path, DOCUMENT_TYPE4)

        # Verify that the document classifier was not called
        self.processor.document_classifier.classify_document.assert_not_called()

        # Verify that the correct processor was called
        mock_process_type4.assert_called_once()

    @patch('jortextractor.core.pipeline.convert_from_path')
    @patch('jortextractor.core.pipeline.JortPDFProcessor._extract_tables_from_image')
    def test_manual_type1_and_type3_specification(self, mock_extract_tables, mock_convert):
        """Test that manually specifying type1 or type3 bypasses classification and uses the general processor."""
        # Setup mocks for type1
        mock_convert.return_value = [MagicMock()]  # Need to return an image for processing
        mock_extract_tables.return_value = []

        # Create a dummy PDF path
        pdf_path = Path(self.temp_dir.name) / "test.pdf"
        with open(pdf_path, "wb") as f:
            f.write(b"%PDF-1.4\n")  # Minimal PDF header

        # Process with document type (type1)
        self.processor.process_pdf(pdf_path, DOCUMENT_TYPE1)

        # Verify that the document classifier was not called
        self.processor.document_classifier.classify_document.assert_not_called()

        # Reset mocks for type3
        mock_convert.reset_mock()
        mock_extract_tables.reset_mock()
        self.processor.document_classifier.reset_mock()

        # Process with document type (type3)
        self.processor.process_pdf(pdf_path, DOCUMENT_TYPE3)

        # Verify that the document classifier was not called
        self.processor.document_classifier.classify_document.assert_not_called()

    def test_document_type_is_required(self):
        """Test that document_type is a required parameter."""
        # Create a dummy PDF path
        pdf_path = Path(self.temp_dir.name) / "test.pdf"
        with open(pdf_path, "wb") as f:
            f.write(b"%PDF-1.4\n")  # Minimal PDF header

        # Verify that calling process_pdf without document_type raises an error
        with self.assertRaises(TypeError):
            self.processor.process_pdf(pdf_path)

if __name__ == "__main__":
    unittest.main()
