"""Enhanced unit tests for name validation and correction."""
import unittest
import logging
from pathlib import Path
import tempfile
from unittest.mock import patch, MagicMock

from jortextractor.core.name_validator import (
    normalize_text, is_valid_name, correct_name,
    validate_and_correct_name, extract_name_components
)
from tests.test_utils import create_temp_directory, cleanup_temp_directory

class TestNameValidationEnhanced(unittest.TestCase):
    """Enhanced test cases for name validation and correction."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()
        cls.test_data_dir = cls.temp_dir / "test_data"
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a logger for testing
        self.logger = logging.getLogger("test_name_validation")
        self.logger.setLevel(logging.DEBUG)

    def test_normalize_text(self):
        """Test text normalization."""
        test_cases = [
            # Input, Expected output
            ("<PERSON>", "MOHAMED BEN ALI"),
            ("Fá<PERSON><PERSON>t Ahmed", "FATIMA BENT AHMED"),
            ("ABDEL-KARIM EL MANSOURI", "ABDEL-KARIM EL MANSOURI"),
            ("Leïla Trabelsi", "LEILA TRABELSI"),
            ("Youssef Ould Mohamed!", "YOUSSEF OULD MOHAMED"),
            ("Amina Bint Ibrahim123", "AMINA BINT IBRAHIM123"),
            ("Samir Aït Ahmed", "SAMIR AIT AHMED"),
            ("Nadia épouse Karim", "NADIA EPOUSE KARIM"),
            ("Jamal Abdul-Nasir", "JAMAL ABDUL-NASIR"),
            ("Samira  Bent  Mohamed", "SAMIRA BENT MOHAMED"),
            ("MOHAMEDBENALI", "MOHAMEDBENALI"),
            ("M0HAMED BEN AL1", "MOHAMED BEN ALI"),
            ("5AMIR AIT AHMED", "SAMIR AIT AHMED"),
            ("LEILA TRABEL5I", "LEILA TRABELSI"),
            ("ABDEL KAR|M", "ABDEL KARIM"),
            ("FATIMA BENT AHMED!", "FATIMA BENT AHMED"),
        ]

        for input_text, expected_output in test_cases:
            result = normalize_text(input_text)
            self.assertEqual(result, expected_output,
                            f"normalize_text('{input_text}') returned '{result}' instead of '{expected_output}'")

    def test_is_valid_name(self):
        """Test name validation."""
        valid_names = [
            "MOHAMED BEN ALI",
            "FATIMA BENT AHMED",
            "ABDEL-KARIM EL MANSOURI",
            "LEILA TRABELSI",
            "YOUSSEF OULD MOHAMED",
            "AMINA BINT IBRAHIM",
            "SAMIR AIT AHMED",
            "NADIA EPOUSE KARIM",
            "JAMAL ABDUL-NASIR",
            "SAMIRA BENT MOHAMED",
            "MOHAMED",
            "BEN ALI",
            "AHMED MOHAMED IBRAHIM",
            "FATIMA ZAHRA BENT AHMED",
            "ABDEL KARIM BEN MOHAMED EL MANSOURI",
        ]

        invalid_names = [
            "",  # Empty string
            "A",  # Too short
            "123456",  # Only digits
            "LISTE DES TITULAIRES",  # Common keyword
            "JOURNAL OFFICIEL",  # Common keyword
            "PAGE 1",  # Contains page number
            "MOHAMED 123456789",  # Too many digits
            "!@#$%^&*()",  # Only special characters
            "A B C D E F G H I J",  # Too many short words
            "ABCDEFGHIJKLMNOPQRSTUVWXYZ",  # No spaces, too long
            "MOHAMED AHMED IBRAHIM KARIM MANSOURI TRABELSI NASIR",  # Too many words
        ]

        # Test valid names
        for name in valid_names:
            self.assertTrue(is_valid_name(name), f"'{name}' should be a valid name")

        # Test invalid names
        for name in invalid_names:
            self.assertFalse(is_valid_name(name), f"'{name}' should be an invalid name")

        # Test with different word count limits
        self.assertTrue(is_valid_name("MOHAMED BEN ALI", min_words=3, max_words=3))
        self.assertFalse(is_valid_name("MOHAMED BEN ALI", min_words=4, max_words=6))
        self.assertFalse(is_valid_name("MOHAMED BEN ALI", min_words=1, max_words=2))

    def test_correct_name(self):
        """Test name correction."""
        test_cases = [
            # Input, Expected output
            ("M0HAMED BEN AL1", "MOHAMED BEN ALI"),
            ("5AMIR AIT AHMED", "SAMIR AIT AHMED"),
            ("LEILA TRABEL5I", "LEILA TRABELSI"),
            ("ABDEL KAR|M", "ABDEL KARIM"),
            ("FATIMA BENT AHMED!", "FATIMA BENT AHMED"),
            ("MOHAMEDBENALI", "MOHAMED BEN ALI"),
            ("BENTAHMED", "BENT AHMED"),
            ("ABDEL KARIIVI", "ABDEL KARIM"),
            ("IVIOHAIVIED", "MOHAMED"),
            ("SAIFEDDINNE", "SAIFEDDINE"),
            ("BOUGHANMI", "BOUGHHANMI"),
            ("HABIBI", "HABIB"),
            ("FREDJ", "FREJ"),
            ("MRABTI", "MRABET"),
        ]

        for input_text, expected_output in test_cases:
            result = correct_name(input_text)
            self.assertEqual(result, expected_output,
                            f"correct_name('{input_text}') returned '{result}' instead of '{expected_output}'")

    def test_validate_and_correct_name(self):
        """Test name validation and correction."""
        valid_cases = [
            # Input, Expected output
            ("Mohamed Ben Ali", "MOHAMED BEN ALI"),
            ("Fátima Bent Ahmed", "FATIMA BENT AHMED"),
            ("ABDEL-KARIM EL MANSOURI", "ABDEL-KARIM EL MANSOURI"),
            ("M0HAMED BEN AL1", "MOHAMED BEN ALI"),
            ("5AMIR AIT AHMED", "SAMIR AIT AHMED"),
            ("MOHAMEDBENALI", "MOHAMED BEN ALI"),
        ]

        invalid_cases = [
            "",  # Empty string
            "A",  # Too short
            "123456",  # Only digits
            "LISTE DES TITULAIRES",  # Common keyword
            "JOURNAL OFFICIEL",  # Common keyword
            "PAGE 1",  # Contains page number
            "MOHAMED 123456789",  # Too many digits
        ]

        # Test valid cases
        for input_text, expected_output in valid_cases:
            result = validate_and_correct_name(input_text)
            self.assertEqual(result, expected_output,
                            f"validate_and_correct_name('{input_text}') returned '{result}' instead of '{expected_output}'")

        # Test invalid cases
        for input_text in invalid_cases:
            result = validate_and_correct_name(input_text)
            self.assertIsNone(result, f"validate_and_correct_name('{input_text}') should return None")

        # Test with different word count limits
        self.assertEqual(validate_and_correct_name("Mohamed Ben Ali", min_words=3, max_words=3), "MOHAMED BEN ALI")
        self.assertIsNone(validate_and_correct_name("Mohamed Ben Ali", min_words=4, max_words=6))
        self.assertIsNone(validate_and_correct_name("Mohamed Ben Ali", min_words=1, max_words=2))

    def test_extract_name_components(self):
        """Test extraction of name components."""
        test_cases = [
            # Input, Expected last name, Expected first name
            ("MOHAMED BEN ALI", "MOHAMED", "BEN ALI"),
            ("FATIMA BENT AHMED", "FATIMA", "BENT AHMED"),
            ("ABDEL-KARIM EL MANSOURI", "ABDEL-KARIM", "EL MANSOURI"),
            ("LEILA TRABELSI", "LEILA", "TRABELSI"),
            ("YOUSSEF OULD MOHAMED", "YOUSSEF", "OULD MOHAMED"),
            ("AMINA BINT IBRAHIM", "AMINA", "BINT IBRAHIM"),
            ("SAMIR AIT AHMED", "SAMIR", "AIT AHMED"),
            ("NADIA EPOUSE KARIM", "NADIA", "EPOUSE KARIM"),
            ("JAMAL ABDUL-NASIR", "JAMAL", "ABDUL-NASIR"),
            ("SAMIRA BENT MOHAMED", "SAMIRA", "BENT MOHAMED"),
            ("MOHAMED", "MOHAMED", ""),
        ]

        for input_text, expected_last_name, expected_first_name in test_cases:
            components = extract_name_components(input_text)
            self.assertEqual(components["full_name"], input_text,
                            f"extract_name_components('{input_text}')['full_name'] should be '{input_text}'")
            self.assertEqual(components["last_name"], expected_last_name,
                            f"extract_name_components('{input_text}')['last_name'] should be '{expected_last_name}'")
            self.assertEqual(components["first_name"], expected_first_name,
                            f"extract_name_components('{input_text}')['first_name'] should be '{expected_first_name}'")

    def test_multilingual_names(self):
        """Test validation of multilingual names."""
        multilingual_names = [
            # Arabic transliterated names
            "MOHAMMED AL-FAYED",
            "ABDUL RAHMAN IBN KHALDUN",
            "FATIMA AL-ZAHRA",
            "SALAH AD-DIN AL-AYYUBI",

            # French names
            "JEAN-PIERRE DUPONT",
            "MARIE-CLAIRE LEFEBVRE",
            "FRANÇOIS HOLLANDE",
            "AMÉLIE POULAIN",

            # Mixed names
            "MOHAMED BEN JEAN",
            "PIERRE BEN ALI",
            "FATIMA EPOUSE DUPONT",
            "JEAN OULD MOHAMED",
        ]

        for name in multilingual_names:
            normalized = normalize_text(name)
            self.assertTrue(is_valid_name(normalized), f"'{normalized}' should be a valid name")
            self.assertIsNotNone(validate_and_correct_name(name), f"validate_and_correct_name('{name}') should not return None")

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory and all its contents
        cleanup_temp_directory(cls.temp_dir)

if __name__ == "__main__":
    unittest.main()
