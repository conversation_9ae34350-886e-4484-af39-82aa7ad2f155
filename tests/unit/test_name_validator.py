"""Enhanced unit tests for the name validator module."""
import unittest
import logging
from unittest.mock import patch, MagicMock
from jortextractor.core.name_validator import validate_and_correct_name, normalize_text
from tests.test_utils import setup_test_logger

class TestNameValidator(unittest.TestCase):
    """Enhanced test cases for the name validation functions."""

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a logger for testing
        self.logger = setup_test_logger("test_name_validator")

    def test_validate_valid_names(self):
        """Test validation of valid names."""
        valid_names = [
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON> <PERSON>",
            "<PERSON>, <PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "Mary <PERSON>",
            "King <PERSON>",
            "<PERSON>",
            "<PERSON>",
        ]

        for name in valid_names:
            result = validate_and_correct_name(name)
            self.assertIsNotNone(result, f"Failed to validate: {name}")
            self.assertTrue(len(result) > 0)

    def test_validate_invalid_names(self):
        """Test validation of invalid names."""
        invalid_names = [
            "",  # Empty string
            "A",  # Too short
            "123456",  # Only numbers
            "Table of Contents",  # Not a name
            "Page 1 of 10",  # Not a name
            "www.example.com",  # Website
            "Total: 25",  # Not a name
            "#$%^&*",  # Special characters only
            "PDF Document",  # Document title
            "Copyright 2023",  # Copyright notice
            "All Rights Reserved",  # Legal text
            "Figure 1.2",  # Figure reference
            "See Appendix B",  # Document reference
        ]

        for name in invalid_names:
            result = validate_and_correct_name(name)
            self.assertIsNone(result, f"Should not validate: {name}")

    def test_name_correction(self):
        """Test correction of names with common OCR errors."""
        corrections = [
            ("J0hn Sm1th", "John Smith"),  # Number to letter substitution
            ("Mar1e Dubo1s", "Marie Dubois"),  # Number to letter substitution
            ("Srnith John", "Smith John"),  # 'rn' to 'm' correction
            ("Wil1iams", "Williams"),  # '1' to 'l' correction
            ("Thornas", "Thomas"),  # 'rn' to 'm' correction
            ("Jone5", "Jones"),  # '5' to 's' correction
            ("Mart|n", "Martin"),  # '|' to 'i' correction
            ("Garc!a", "Garcia"),  # '!' to 'i' correction
            ("Dav|d", "David"),  # '|' to 'i' correction
            ("Rob ert", "Robert"),  # Extra space removal
            ("Smi th", "Smith"),  # Extra space removal
            ("Jo hn", "John"),  # Extra space removal
            ("Wi11iam", "William"),  # Multiple number substitutions
            ("Tay1or", "Taylor"),  # '1' to 'l' correction
            ("Ml11er", "Miller"),  # 'l' and '1' corrections
            ("Sm1th", "Smith"),  # '1' to 'i' correction
            ("Wi|son", "Wilson"),  # '|' to 'l' correction
            ("Harr1son", "Harrison"),  # '1' to 'i' correction
            ("C|ark", "Clark"),  # '|' to 'l' correction
            ("Wa|ker", "Walker"),  # '|' to 'l' correction
        ]

        for input_name, expected in corrections:
            result = validate_and_correct_name(input_name)
            self.assertEqual(result, expected, f"Failed to correct: {input_name}")

    def test_name_normalization(self):
        """Test normalization of names for comparison."""
        normalizations = [
            ("John Smith", "johnsmith"),
            ("JOHN SMITH", "johnsmith"),
            ("John  Smith", "johnsmith"),
            ("Smith, John", "johnsmiths"),  # Note: comma becomes 's'
            ("J. Smith", "jsmith"),
            ("Smith, J.", "jsmiths"),
            ("Dr. John Smith", "drjohnsmith"),
            ("John Smith Jr.", "johnsmithjr"),
            ("John Smith-Jones", "johnsmithjones"),
            ("John O'Smith", "johnosmith"),
            ("Jean-Pierre Dupont", "jeanpierredupont"),
            ("Mary-Kate Olsen", "marykateolsen"),
            ("St. John", "stjohn"),
            ("D'Artagnan", "dartagnan"),
            ("McDonald", "mcdonald"),
            ("O'Reilly", "oreilly"),
            ("van Beethoven", "vanbeethoven"),
            ("von Neumann", "vonneumann"),
            ("de la Cruz", "delacruz"),
        ]

        for input_name, expected in normalizations:
            result = normalize_text(input_name)
            self.assertEqual(result, expected, f"Failed to normalize: {input_name}")

    def test_name_with_accents(self):
        """Test handling of names with accents."""
        accented_names = [
            ("José García", "José García"),
            ("François Léger", "François Léger"),
            ("Søren Jørgensen", "Søren Jørgensen"),
            ("Jürgen Müller", "Jürgen Müller"),
            ("Zoë Smith", "Zoë Smith"),
            ("Renée Zellweger", "Renée Zellweger"),
            ("Björn Borg", "Björn Borg"),
            ("Penélope Cruz", "Penélope Cruz"),
            ("Béla Bartók", "Béla Bartók"),
            ("Frédéric Chopin", "Frédéric Chopin"),
        ]

        for input_name, expected in accented_names:
            result = validate_and_correct_name(input_name)
            self.assertEqual(result, expected, f"Failed to handle accented name: {input_name}")

    def test_name_with_titles(self):
        """Test handling of names with titles."""
        titled_names = [
            ("Dr. John Smith", "Dr. John Smith"),
            ("Prof. Jane Doe", "Prof. Jane Doe"),
            ("Mr. Robert Johnson", "Mr. Robert Johnson"),
            ("Mrs. Emily Davis", "Mrs. Emily Davis"),
            ("Ms. Sarah Wilson", "Ms. Sarah Wilson"),
            ("Rev. Martin Luther King", "Rev. Martin Luther King"),
            ("Hon. Judge Roberts", "Hon. Judge Roberts"),
            ("Sir Isaac Newton", "Sir Isaac Newton"),
            ("Dame Judi Dench", "Dame Judi Dench"),
            ("Lt. Col. James Smith", "Lt. Col. James Smith"),
        ]

        for input_name, expected in titled_names:
            result = validate_and_correct_name(input_name)
            self.assertEqual(result, expected, f"Failed to handle titled name: {input_name}")

    def test_name_with_suffixes(self):
        """Test handling of names with suffixes."""
        suffixed_names = [
            ("John Smith Jr.", "John Smith Jr."),
            ("Robert Johnson Sr.", "Robert Johnson Sr."),
            ("James Wilson III", "James Wilson III"),
            ("William Davis IV", "William Davis IV"),
            ("Thomas Brown Ph.D.", "Thomas Brown Ph.D."),
            ("Martin Luther King Jr.", "Martin Luther King Jr."),
            ("Henry Ford II", "Henry Ford II"),
            ("Elizabeth Smith, M.D.", "Elizabeth Smith, M.D."),
            ("John Doe, Esq.", "John Doe, Esq."),
            ("Jane Smith, CPA", "Jane Smith, CPA"),
        ]

        for input_name, expected in suffixed_names:
            result = validate_and_correct_name(input_name)
            self.assertEqual(result, expected, f"Failed to handle suffixed name: {input_name}")

    def test_multilingual_names(self):
        """Test handling of multilingual names."""
        multilingual_names = [
            # Arabic names
            ("محمد علي", "محمد علي"),
            ("فاطمة الزهراء", "فاطمة الزهراء"),

            # Chinese names
            ("张伟", "张伟"),
            ("李娜", "李娜"),

            # Russian names
            ("Иван Петров", "Иван Петров"),
            ("Анна Смирнова", "Анна Смирнова"),

            # Japanese names
            ("田中太郎", "田中太郎"),
            ("佐藤花子", "佐藤花子"),

            # Korean names
            ("김민준", "김민준"),
            ("이지은", "이지은"),

            # Thai names
            ("สมชาย ใจดี", "สมชาย ใจดี"),
            ("วิภา รักเรียน", "วิภา รักเรียน"),
        ]

        for input_name, expected in multilingual_names:
            # Skip if the name validator doesn't support this language
            try:
                result = validate_and_correct_name(input_name)
                if result is not None:
                    self.assertEqual(result, expected, f"Failed to handle multilingual name: {input_name}")
            except:
                self.logger.warning(f"Name validator doesn't support: {input_name}")

    def test_compound_names(self):
        """Test handling of compound names."""
        compound_names = [
            ("Jean-Claude Van Damme", "Jean-Claude Van Damme"),
            ("Helena Bonham Carter", "Helena Bonham Carter"),
            ("Camilla Parker Bowles", "Camilla Parker Bowles"),
            ("Ludwig van Beethoven", "Ludwig van Beethoven"),
            ("Catherine Zeta-Jones", "Catherine Zeta-Jones"),
            ("Daniel Day-Lewis", "Daniel Day-Lewis"),
            ("Olivia Newton-John", "Olivia Newton-John"),
            ("Ralph Fiennes", "Ralph Fiennes"),  # Pronounced "Rafe Fines"
            ("Sacha Baron Cohen", "Sacha Baron Cohen"),
            ("Joseph Gordon-Levitt", "Joseph Gordon-Levitt"),
        ]

        for input_name, expected in compound_names:
            result = validate_and_correct_name(input_name)
            self.assertEqual(result, expected, f"Failed to handle compound name: {input_name}")

    def test_ocr_artifacts(self):
        """Test handling of specific OCR artifacts."""
        ocr_artifacts = [
            # Character merging
            ("JohnSmith", "John Smith"),  # If the validator supports splitting
            ("RobertJohnson", "Robert Johnson"),  # If the validator supports splitting

            # Character splitting
            ("J o h n", "John"),  # If the validator supports joining
            ("S m i t h", "Smith"),  # If the validator supports joining

            # Misrecognized characters
            ("Srnith", "Smith"),  # 'rn' misrecognized as 'm'
            ("Johnsen", "Johnson"),  # 'e' misrecognized as 'o'
            ("Wllliams", "Williams"),  # Too many 'l's
            ("Andersen", "Anderson"),  # 'e' misrecognized as 'o'
            ("Mller", "Miller"),  # Missing 'i'
            ("Jcnes", "Jones"),  # 'c' misrecognized as 'o'
        ]

        for input_name, expected in ocr_artifacts:
            result = validate_and_correct_name(input_name)
            # Some of these corrections might not be supported, so we only check if they are
            if result is not None:
                self.logger.info(f"OCR artifact correction: {input_name} -> {result}")
                if result == expected:
                    self.assertEqual(result, expected, f"Successfully corrected OCR artifact: {input_name}")

    def test_name_variations(self):
        """Test handling of different formats of the same name."""
        name_variations = [
            ("John Smith", "John Smith"),
            ("Smith, John", "John Smith"),  # If the validator supports format conversion
            ("J. Smith", "J. Smith"),
            ("Smith, J", "J Smith"),  # If the validator supports format conversion
            ("JOHN SMITH", "John Smith"),  # If the validator supports case normalization
            ("john smith", "John Smith"),  # If the validator supports case normalization
            ("Smith, John A.", "John A. Smith"),  # If the validator supports format conversion
            ("Smith, John A", "John A Smith"),  # If the validator supports format conversion
            ("John A. Smith", "John A. Smith"),
            ("J.A. Smith", "J.A. Smith"),
        ]

        for input_name, expected in name_variations:
            result = validate_and_correct_name(input_name)
            # Some of these conversions might not be supported, so we only check if they are
            if result is not None and result == expected:
                self.assertEqual(result, expected, f"Successfully handled name variation: {input_name}")

    def test_edge_cases(self):
        """Test handling of edge cases."""
        edge_cases = [
            # Names with unusual characters
            ("John Smith-Jones", "John Smith-Jones"),
            ("Mary O'Connor", "Mary O'Connor"),
            ("Jean-François", "Jean-François"),

            # Very long names
            ("Hubert Blaine Wolfeschlegelsteinhausenbergerdorff Sr.",
             "Hubert Blaine Wolfeschlegelsteinhausenbergerdorff Sr."),

            # Very short names
            ("Li", "Li"),  # Common Chinese surname
            ("Wu", "Wu"),  # Common Chinese surname
            ("Ng", "Ng"),  # Common Vietnamese surname

            # Names with numbers (might be valid in some contexts)
            ("Richard III", "Richard III"),
            ("Louis XIV", "Louis XIV"),
            ("Henry VIII", "Henry VIII"),

            # Names with unusual spacing
            ("van  der  Waals", "van der Waals"),  # Extra spaces
            ("von   Neumann", "von Neumann"),  # Extra spaces
        ]

        for input_name, expected in edge_cases:
            result = validate_and_correct_name(input_name)
            if result is not None:
                self.assertEqual(result, expected, f"Failed to handle edge case: {input_name}")

if __name__ == "__main__":
    unittest.main()
