"""Tests for logging and monitoring feature."""
import unittest
import logging
import io
import re
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

from jortextractor.core.pipeline import JortPDFProcessor
from jortextractor.core.document_classifier import DocumentClassifier
from tests.test_utils import create_temp_directory, cleanup_temp_directory, setup_test_logger

class TestLogging(unittest.TestCase):
    """Test cases for logging and monitoring feature."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test outputs
        cls.temp_dir = create_temp_directory()
        cls.test_data_dir = cls.temp_dir / "test_data"
        cls.test_data_dir.mkdir(parents=True, exist_ok=True)

        # Path to example PDFs
        cls.example_pdfs_dir = Path("ProjectContext/DataExamples")

    def setUp(self):
        """Set up test fixtures for each test."""
        # Create a string IO for capturing log output
        self.log_stream = io.StringIO()

        # Configure a handler that writes to the string IO
        self.handler = logging.StreamHandler(self.log_stream)
        self.handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self.handler.setFormatter(formatter)

        # Get the root logger and add the handler
        self.root_logger = logging.getLogger()
        self.root_logger.addHandler(self.handler)

        # Store the original level to restore it later
        self.original_level = self.root_logger.level
        self.root_logger.setLevel(logging.DEBUG)

    def tearDown(self):
        """Clean up test fixtures for each test."""
        # Remove the handler and restore the original level
        self.root_logger.removeHandler(self.handler)
        self.root_logger.setLevel(self.original_level)

    def test_processor_initialization_logging(self):
        """Test logging during processor initialization."""
        # Clear the log stream
        self.log_stream.seek(0)
        self.log_stream.truncate()

        # Create a processor with debug output enabled
        processor = JortPDFProcessor()

        # Get the log output
        log_output = self.log_stream.getvalue()

        # Check that initialization is logged
        self.assertIn("jortextractor.pipeline", log_output)

    def test_document_classifier_logging(self):
        """Test logging in the document classifier."""
        # Create a test logger
        test_logger = setup_test_logger("test_classifier")

        # Create a classifier with the test logger
        classifier = DocumentClassifier(logger=test_logger)

        # Create a mock image
        mock_image = MagicMock()
        mock_image.shape = (1000, 800, 3)

        # Mock the necessary methods to avoid actual image processing
        with patch.object(classifier, '_is_type2', return_value=True):
            # Classify the mock image
            doc_type = classifier.classify_document(mock_image)

            # Get the log output
            log_output = test_logger.buffer.getvalue()

            # Check that classification is logged
            self.assertIn("Document classified as Type2", log_output)

    def test_processor_logging_levels(self):
        """Test different logging levels in the processor."""
        # Create loggers with different levels
        debug_logger = setup_test_logger("debug_logger")
        debug_logger.setLevel(logging.DEBUG)

        info_logger = setup_test_logger("info_logger")
        info_logger.setLevel(logging.INFO)

        warning_logger = setup_test_logger("warning_logger")
        warning_logger.setLevel(logging.WARNING)

        # Create processors with different loggers
        with patch('jortextractor.pipeline.logging.getLogger') as mock_get_logger:
            # Test DEBUG level
            mock_get_logger.return_value = debug_logger
            debug_processor = JortPDFProcessor()

            # Test INFO level
            mock_get_logger.return_value = info_logger
            info_processor = JortPDFProcessor()

            # Test WARNING level
            mock_get_logger.return_value = warning_logger
            warning_processor = JortPDFProcessor()

        # Create a mock PDF path
        mock_pdf_path = MagicMock()
        mock_pdf_path.exists.return_value = True

        # Mock the necessary methods to avoid actual PDF processing
        with patch.object(JortPDFProcessor, 'process_pdf', return_value={'status': 'success'}):
            # Process with DEBUG level
            debug_processor.process_pdf(mock_pdf_path)
            debug_log = debug_logger.buffer.getvalue()

            # Process with INFO level
            info_processor.process_pdf(mock_pdf_path)
            info_log = info_logger.buffer.getvalue()

            # Process with WARNING level
            warning_processor.process_pdf(mock_pdf_path)
            warning_log = warning_logger.buffer.getvalue()

            # Check log outputs
            self.assertGreater(len(debug_log), len(info_log))
            self.assertGreater(len(info_log), len(warning_log))

    def test_error_logging(self):
        """Test logging of errors."""
        # Create a test logger
        test_logger = setup_test_logger("error_logger")

        # Create a processor with the test logger
        with patch('jortextractor.pipeline.logging.getLogger') as mock_get_logger:
            mock_get_logger.return_value = test_logger
            processor = JortPDFProcessor()

        # Create a non-existent PDF path
        non_existent_path = Path("non_existent_file.pdf")

        # Process the non-existent PDF (should log an error)
        result = processor.process_pdf(non_existent_path)

        # Get the log output
        log_output = test_logger.buffer.getvalue()

        # Check that the error is logged
        self.assertIn("ERROR", log_output)
        self.assertIn("File not found", log_output)

        # Check that the result indicates an error
        self.assertIn("error", result["status"])

    def test_performance_logging(self):
        """Test logging of performance metrics."""
        # Create a test logger
        test_logger = setup_test_logger("performance_logger")

        # Create a processor with the test logger
        with patch('jortextractor.pipeline.logging.getLogger') as mock_get_logger:
            mock_get_logger.return_value = test_logger
            processor = JortPDFProcessor()

        # Get a sample PDF
        pdf_files = list(self.example_pdfs_dir.glob("*.pdf"))

        # Skip if no files found
        if not pdf_files:
            self.skipTest("No PDF files found for testing")

        pdf_path = pdf_files[0]

        # Skip if file doesn't exist
        if not pdf_path.exists():
            self.skipTest(f"Test file not found: {pdf_path}")

        # Process the PDF
        try:
            result = processor.process_pdf(pdf_path)

            # Get the log output
            log_output = test_logger.buffer.getvalue()

            # Check that processing start is logged
            self.assertIn(f"Processing {pdf_path}", log_output)

            # Check that document type is logged
            self.assertIn("Document classified as", log_output)

            # Check that processing was successful
            self.assertEqual(result["status"], "success")

        except Exception as e:
            self.fail(f"Processing failed: {e}")

    def test_log_file_creation(self):
        """Test creation of log files."""
        # Create a temporary log file
        with tempfile.NamedTemporaryFile(suffix='.log', delete=False) as temp_log_file:
            log_path = temp_log_file.name

        try:
            # Configure file logging
            file_handler = logging.FileHandler(log_path)
            file_handler.setLevel(logging.DEBUG)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)

            # Get the root logger and add the handler
            root_logger = logging.getLogger()
            root_logger.addHandler(file_handler)

            # Create a processor
            processor = JortPDFProcessor()

            # Get a sample PDF
            pdf_files = list(self.example_pdfs_dir.glob("*.pdf"))

            # Skip if no files found
            if not pdf_files:
                self.skipTest("No PDF files found for testing")

            pdf_path = pdf_files[0]

            # Skip if file doesn't exist
            if not pdf_path.exists():
                self.skipTest(f"Test file not found: {pdf_path}")

            # Process the PDF
            try:
                result = processor.process_pdf(pdf_path)

                # Remove the file handler
                root_logger.removeHandler(file_handler)
                file_handler.close()

                # Check that the log file was created and contains log entries
                with open(log_path, 'r') as f:
                    log_content = f.read()

                    # Check that processing start is logged
                    self.assertIn(f"Processing {pdf_path}", log_content)

                    # Check that document type is logged
                    self.assertIn("Document classified as", log_content)

            except Exception as e:
                self.fail(f"Processing failed: {e}")

        finally:
            # Clean up the temporary log file
            if os.path.exists(log_path):
                os.unlink(log_path)

    @classmethod
    def tearDownClass(cls):
        """Clean up test fixtures."""
        # Remove temporary directory and all its contents
        cleanup_temp_directory(cls.temp_dir)

if __name__ == "__main__":
    unittest.main()
