"""Utility functions for JortExtractor tests."""
import os
import sys
import cv2
import numpy as np
import tempfile
import shutil
import logging
import psutil
import time
import random
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union, Any

def create_test_image(width: int = 800, height: int = 1000,
                     bg_color: Tuple[int, int, int] = (255, 255, 255)) -> np.ndarray:
    """Create a blank test image.

    Args:
        width: Image width in pixels
        height: Image height in pixels
        bg_color: Background color as BGR tuple

    Returns:
        NumPy array containing the image
    """
    return np.ones((height, width, 3), dtype=np.uint8) * np.array(bg_color, dtype=np.uint8)

def add_text_to_image(image: np.ndarray, text: str, position: Tuple[int, int],
                     font_scale: float = 0.8, color: Tuple[int, int, int] = (0, 0, 0),
                     thickness: int = 2) -> np.ndarray:
    """Add text to an image.

    Args:
        image: Image as NumPy array
        text: Text to add
        position: (x, y) position
        font_scale: Font scale
        color: Text color as BGR tuple
        thickness: Line thickness

    Returns:
        Image with text added
    """
    result = image.copy()
    cv2.putText(result, text, position, cv2.FONT_HERSHEY_SIMPLEX,
                font_scale, color, thickness)
    return result

def add_rectangle_to_image(image: np.ndarray, top_left: Tuple[int, int],
                          bottom_right: Tuple[int, int],
                          color: Tuple[int, int, int] = (200, 200, 200),
                          thickness: int = -1) -> np.ndarray:
    """Add a rectangle to an image.

    Args:
        image: Image as NumPy array
        top_left: (x, y) top-left corner
        bottom_right: (x, y) bottom-right corner
        color: Rectangle color as BGR tuple
        thickness: Line thickness (-1 for filled)

    Returns:
        Image with rectangle added
    """
    result = image.copy()
    cv2.rectangle(result, top_left, bottom_right, color, thickness)
    return result

def add_line_to_image(image: np.ndarray, start_point: Tuple[int, int],
                     end_point: Tuple[int, int],
                     color: Tuple[int, int, int] = (0, 0, 0),
                     thickness: int = 2) -> np.ndarray:
    """Add a line to an image.

    Args:
        image: Image as NumPy array
        start_point: (x, y) start point
        end_point: (x, y) end point
        color: Line color as BGR tuple
        thickness: Line thickness

    Returns:
        Image with line added
    """
    result = image.copy()
    cv2.line(result, start_point, end_point, color, thickness)
    return result

def add_noise_to_image(image: np.ndarray, noise_level: int = 20) -> np.ndarray:
    """Add random noise to an image.

    Args:
        image: Image as NumPy array
        noise_level: Maximum noise intensity

    Returns:
        Noisy image
    """
    result = image.copy()
    noise = np.random.randint(-noise_level, noise_level, image.shape, dtype=np.int16)
    result = cv2.add(result, noise.astype(np.uint8))
    return result

def create_type1_sample() -> np.ndarray:
    """Create a sample type1 document image (two columns with hidden separator).

    Returns:
        Image as NumPy array
    """
    # Create a blank image
    img = create_test_image(800, 1000)

    # Add two columns of text-like content
    for y in range(100, 900, 30):
        # Left column
        img = add_rectangle_to_image(img, (50, y), (350, y+20), (200, 200, 200))
        # Right column
        img = add_rectangle_to_image(img, (400, y), (700, y+20), (200, 200, 200))

    return img

def create_type2_sample() -> np.ndarray:
    """Create a sample type2 document image (multiple columns with headers).

    Returns:
        Image as NumPy array
    """
    # Create a blank image
    img = create_test_image(800, 1000)

    # Add header with type2 indicators
    img = add_text_to_image(img, "NOMS & PRENOMS", (50, 50), 0.8, (0, 0, 0), 2)
    img = add_text_to_image(img, "CIN", (350, 50), 0.8, (0, 0, 0), 2)
    img = add_text_to_image(img, "ADRESSE", (550, 50), 0.8, (0, 0, 0), 2)

    # Add separator line
    img = add_line_to_image(img, (50, 70), (750, 70), (0, 0, 0), 2)

    # Add content rows
    for y in range(100, 900, 30):
        # Name column
        img = add_rectangle_to_image(img, (50, y), (300, y+20), (200, 200, 200))
        # CIN column
        img = add_rectangle_to_image(img, (350, y), (500, y+20), (200, 200, 200))
        # Address column
        img = add_rectangle_to_image(img, (550, y), (750, y+20), (200, 200, 200))

    return img

def create_type3_sample() -> np.ndarray:
    """Create a sample type3 document image (single column format).

    Returns:
        Image as NumPy array
    """
    # Create a blank image
    img = create_test_image(800, 1000)

    # Add title
    img = add_text_to_image(img, "LISTE DES TITULAIRES", (250, 50), 0.8, (0, 0, 0), 2)

    # Add single column of names
    for y in range(100, 900, 30):
        img = add_rectangle_to_image(img, (200, y), (600, y+20), (200, 200, 200))

    return img

def create_type4_sample() -> np.ndarray:
    """Create a sample type4 document image (three columns of names without headers).

    Returns:
        Image as NumPy array
    """
    # Create a blank image
    img = create_test_image(800, 1000)

    # Type4 has no headers, just three columns of names

    # Add three columns of names
    for y in range(100, 900, 30):
        # Left column
        img = add_rectangle_to_image(img, (50, y), (230, y+20), (200, 200, 200))
        # Middle column
        img = add_rectangle_to_image(img, (280, y), (460, y+20), (200, 200, 200))
        # Right column
        img = add_rectangle_to_image(img, (510, y), (690, y+20), (200, 200, 200))

    return img

def create_sample_with_header_footer() -> np.ndarray:
    """Create a sample document with header and footer.

    Returns:
        Image as NumPy array
    """
    # Create a blank image
    img = create_test_image(800, 1000)

    # Add header
    header_bg = add_rectangle_to_image(img, (0, 0), (800, 80), (240, 240, 240))
    img = add_text_to_image(header_bg, "DOCUMENT HEADER", (300, 40), 0.8, (0, 0, 0), 2)

    # Add footer
    footer_bg = add_rectangle_to_image(img, (0, 920), (800, 1000), (240, 240, 240))
    img = add_text_to_image(footer_bg, "Page 1", (700, 960), 0.8, (0, 0, 0), 2)

    # Add content
    for y in range(100, 900, 30):
        img = add_rectangle_to_image(img, (100, y), (700, y+20), (200, 200, 200))

    return img

def create_sample_with_table_borders() -> np.ndarray:
    """Create a sample document with table borders.

    Returns:
        Image as NumPy array
    """
    # Create a blank image
    img = create_test_image(800, 1000)

    # Add table borders
    # Horizontal lines
    for y in range(100, 900, 100):
        img = add_line_to_image(img, (100, y), (700, y), (0, 0, 0), 2)

    # Vertical lines
    for x in range(100, 800, 200):
        img = add_line_to_image(img, (x, 100), (x, 900), (0, 0, 0), 2)

    # Add content
    for row in range(8):
        for col in range(3):
            y = 100 + row * 100 + 40
            x = 100 + col * 200 + 20
            img = add_text_to_image(img, f"Cell {row},{col}", (x, y), 0.6, (0, 0, 0), 1)

    return img

def create_temp_directory() -> Path:
    """Create a temporary directory for test files.

    Returns:
        Path to the temporary directory
    """
    return Path(tempfile.mkdtemp())

def cleanup_temp_directory(temp_dir: Path) -> None:
    """Clean up a temporary directory.

    Args:
        temp_dir: Path to the temporary directory
    """
    if temp_dir.exists():
        shutil.rmtree(temp_dir)

def save_image(image: np.ndarray, path: Path) -> None:
    """Save an image to a file.

    Args:
        image: Image as NumPy array
        path: Path to save the image
    """
    path.parent.mkdir(parents=True, exist_ok=True)
    cv2.imwrite(str(path), image)

def monitor_memory_usage(interval: float = 0.1, duration: float = 1.0) -> List[Dict[str, float]]:
    """Monitor memory usage over time.

    Args:
        interval: Sampling interval in seconds
        duration: Monitoring duration in seconds

    Returns:
        List of dictionaries with memory usage information
    """
    start_time = time.time()
    measurements = []

    while time.time() - start_time < duration:
        # Get memory usage
        memory = psutil.virtual_memory()

        # Record measurement
        timestamp = time.time() - start_time
        measurements.append({
            'timestamp': timestamp,
            'percent': memory.percent,
            'used_mb': memory.used / (1024 * 1024),
            'available_mb': memory.available / (1024 * 1024)
        })

        # Sleep for the specified interval
        time.sleep(interval)

    return measurements

def setup_test_logger(name: str = "test_logger") -> logging.Logger:
    """Set up a logger for testing.

    Args:
        name: Logger name

    Returns:
        Logger instance
    """
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)

    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Add a string IO handler for testing
    import io
    log_buffer = io.StringIO()
    handler = logging.StreamHandler(log_buffer)
    handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

    # Store the buffer in the logger for access
    logger.buffer = log_buffer

    return logger
