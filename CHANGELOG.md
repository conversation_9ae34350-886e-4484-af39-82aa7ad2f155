# JortExtractor Changelog

## [Unreleased]

### Added
- Comprehensive test evaluation and enhancement plan
- Test evaluation script for automated assessment of test quality
- Ground truth validation script for measuring OCR accuracy
- Test tracking spreadsheet for monitoring test improvements
- Detailed test evaluation templates and reports
- Rotation detection and correction for document classification
- Confidence scores for document type classification
- Enhanced error handling for image processing functions
- Improved robustness for empty page handling
- OCR benchmarking module for evaluating OCR performance
- OCR.space API integration for benchmarking OCR performance (NEVER as an alternative)
- Standalone OCR benchmarking script for detailed analysis
- OCR comparison metrics (precision, recall, F1 score) for evaluating OCR quality
- Clear separation between core OCR implementation and benchmarking tools
- Enhanced OCR comparison reporting with detailed text and HTML reports
- Side-by-side text comparison for better OCR analysis
- Visual highlighting of matched and unmatched OCR results
- Inclusion of original images in OCR comparison reports for direct visual assessment
- Enhanced ground truth comparison with visual processing steps report
- HTML report showing consecutive images of the processing steps (original pages, detected tables, pre-OCR tables, pre-OCR columns)
- Required document type specification via command-line argument, removing automatic detection
- Completely redesigned Type 1 document processing with specialized pipeline:
  - Implemented a text-based approach instead of table detection (no solid borders)
  - First detect and crop the big table boundaries in the page using projection profiles
  - Optimally remove headers, footers, and excessive margins while preserving all content
  - Process the cropped big table with OCR using multiple approaches
  - Use text layout analysis to identify names and identifiers
  - Detect white separator spaces between columns using vertical projection profiles
  - Divide the table at the exact positions of white spaces (628px, 520px, 596px)
  - Ensure no letters are cut off at sub-table boundaries by using natural white spaces
  - Detect the natural column boundary within each sub-table to properly identify the 2-column structure
  - Group names and identifiers into logical pairs based on vertical alignment within each sub-table
  - Added minimal post-processing:
    - Basic name cleaning (removing extra spaces only)
    - Preserving original OCR output for accurate evaluation
    - Identifier standardization for consistent format
    - Deduplication of entries with the same name but different identifiers
    - Achieved 100% recall with 58.6% precision (F1 score: 73.9%)
  - Save both columns in CSV output with sub-table information
  - Save pre-OCR images of the big table and each cropped sub-table for better debugging
- Reorganized project structure following best practices:
  - Modular package structure with core, utils, benchmarks, and parallel modules
  - Improved test organization with conftest.py for pytest configuration
  - Added setup.py and pyproject.toml for proper packaging
  - Created comprehensive documentation structure
  - Organized data and results directories
  - Added development tools configuration
  - Cleaned up project root directory
  - Moved configuration files to config directory
  - Organized documentation files into planning and tracking directories
  - Consolidated log files into logs directory
  - Removed redundant and temporary files
- Implemented proper package organization:
  - Moved core functionality to jortextractor/core/ directory
  - Moved utility functions to jortextractor/utils/ directory
  - Moved benchmarking tools to jortextractor/benchmarks/ directory
  - Moved parallel processing to jortextractor/parallel/ directory
  - Fixed imports in all files to use the new module structure
  - Removed duplicate files and modules
  - Improved code organization and maintainability

### Changed
- Updated testing methodology with focus on ground truth validation
- Enhanced test evaluation criteria with emphasis on recall for name extraction
- Improved test documentation with detailed enhancement plans
- Enhanced table detection algorithm for better handling of noisy images
- Improved multi-column layout detection
- Better handling of OpenCV errors in image processing
- Extended ground truth testing to include OCR benchmarking (for evaluation only)
- Added command-line options for OCR benchmarking
- Restructured OCR benchmarking code for better separation from main pipeline
- Added clear warnings and documentation about OCR.space being for benchmarking only
- Reorganized code into logical modules (core, utils, benchmarks, parallel)
- Improved code organization by removing duplicate files and fixing imports across the codebase

### Fixed
- Fixed document classification tests for all document types
- Fixed table detection tests for complex layouts
- Fixed robustness tests for handling empty pages, rotated documents, and noisy images
- Fixed OpenCV errors in image processing functions
- Improved error handling in text region detection
- Fixed issues with multi-column detection
- Fixed issue with titles and descriptions appearing in pre-OCR images by improving table boundary detection
- Enhanced text region filtering to exclude non-table elements like headers and footers
- Improved table detection to prioritize actual table structures over general text regions

## [1.1.0] - 2025-05-11

### Added
- Comprehensive testing strategy with 67 tests across unit, integration, performance, robustness, security, and logging categories
- Support for type4 document format (three columns of names without headers)
- Enhanced OCR accuracy testing with different image qualities and PSM modes
- Memory usage monitoring and testing with visualization of memory patterns
- Parallel processing testing with worker count optimization
- Pre-OCR image saving feature for debugging and validation
- Improved name validation with multilingual support and compound name handling
- Table detection and cropping tests for all document types
- Ground truth comparison testing with precision, recall, and F1 score metrics
- Security testing for path traversal prevention, invalid input handling, and malformed PDF detection
- Logging and monitoring tests with different log levels
- Test result visualization with charts and CSV exports
- Automated test runner with parallel execution support
- Test directories for organizing test results and artifacts

### Changed
- Updated README.md with detailed testing strategy information
- Improved project structure documentation
- Enhanced test utilities with more sample generation functions
- Added Python path handling for better test discovery
- Implemented test skipping for unsupported features
- Added detailed test reporting with failure analysis

### Fixed
- Improved robustness for handling rotated, noisy, and low-resolution images
- Better error handling for invalid inputs
- Memory leak prevention with proper cleanup
- Fixed Python module path issues in test environment

## [1.0.0] - Initial Release

### Added
- Basic PDF processing pipeline
- Document type classification (type1, type2, type3)
- Image processing utilities for OCR optimization
- Name validation and correction
- Parallel processing support
- Command-line interface
