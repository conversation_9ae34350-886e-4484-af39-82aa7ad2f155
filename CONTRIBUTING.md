# Contributing to <PERSON><PERSON><PERSON><PERSON><PERSON>or

Thank you for considering contributing to JortExtractor! This document provides guidelines and instructions for contributing to the project.

## Code of Conduct

Please be respectful and considerate of others when contributing to this project.

## How to Contribute

1. Fork the repository
2. Create a new branch for your feature or bugfix
3. Make your changes
4. Run tests to ensure your changes don't break existing feature
5. Submit a pull request

## Development Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/jortextractor.git
   cd jortextractor
   ```

2. Create a virtual environment and install development dependencies:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements-dev.txt
   ```

3. Install the package in development mode:
   ```bash
   pip install -e .
   ```

## Running Tests

Run the tests using pytest:
```bash
pytest
```

For more comprehensive testing:
```bash
python -m tests.run_tests
```

## Code Style

This project follows the [Black](https://black.readthedocs.io/en/stable/) code style. Please ensure your code is formatted with <PERSON> before submitting a pull request.

```bash
black .
isort .
```

## Documentation

Please update the documentation when adding or modifying features. Documentation is written in Markdown and is located in the `docs/` directory.

## Submitting a Pull Request

1. Update the README.md and documentation with details of changes if appropriate
2. Update the CHANGELOG.md with details of changes
3. The version number will be updated by the maintainers
4. Your pull request will be reviewed by the maintainers

## License

By contributing to JortExtractor, you agree that your contributions will be licensed under the project's license.
