#!/usr/bin/env python3
"""
JortDataExtractor - Main Program

This program extracts data from Tunisian Official Gazette (JORT) PDFs.
It uses the following subprojects:
- JORTPROC-GenerateSubtables: Transforms a PDF page into subtables
- JORTPROC-OCRLoc: Processes OCR on images using local OCR code
- JORTPROC-OCRSpace: Processes OCR on images using OCR.space API
"""
import argparse
import logging
import os
import sys
from pathlib import Path
import pandas as pd

# Import subprojects
from Subprojects.JORTPROC_GenerateSubtacles.generate_subtables import generate_subtables
from Subprojects.JORTPROC_OCRLoc.ocr_local import process_ocr_local
from Subprojects.JORTPROC_OCRSpace.ocr_space import process_ocr_space

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def process_pdf(pdf_path: str, page_num: int, document_type: str, output_dir: str,
                headers: str, use_ocr_space: bool = False, api_key: str = 'K888957') -> str:
    """
    Process a PDF page to extract data.

    Args:
        pdf_path: Path to the PDF file
        page_num: Page number to process (1-based)
        document_type: Type of document (e.g., 'type1', 'type2')
        output_dir: Directory to save the output files
        headers: Comma-separated list of headers for the CSV file
        use_ocr_space: Whether to use OCR.space API instead of local OCR
        api_key: OCR.space API key (only used if use_ocr_space is True)

    Returns:
        Path to the output CSV file
    """
    try:
        # Convert paths to Path objects
        pdf_path = Path(pdf_path)
        output_dir = Path(output_dir)

        # Create output directory if it doesn't exist
        output_dir.mkdir(parents=True, exist_ok=True)

        # Step 1: Generate subtables from the PDF page
        logger.info(f"Step 1: Generating subtables from {pdf_path}, page {page_num}")
        subtables_dir = generate_subtables(pdf_path, page_num, document_type, output_dir / "subtables")
        logger.info(f"Generated subtables in {subtables_dir}")

        # Step 2: Process OCR on the subtables
        if use_ocr_space:
            logger.info(f"Step 2: Processing OCR using OCR.space API")
            output_file = output_dir / f"page{page_num}_ocr_space.csv"
            process_ocr_space(subtables_dir, headers, output_file, api_key)
        else:
            logger.info(f"Step 2: Processing OCR using local OCR")
            output_file = output_dir / f"page{page_num}_ocr_local.csv"
            process_ocr_local(subtables_dir, headers, output_file)

        logger.info(f"Successfully processed PDF page and saved results to {output_file}")
        return str(output_file)
    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        raise

def main():
    """Main entry point for the program."""
    parser = argparse.ArgumentParser(description="Extract data from Tunisian Official Gazette (JORT) PDFs")
    parser.add_argument("pdf_path", help="Path to the PDF file")
    parser.add_argument("page_num", type=int, help="Page number to process (1-based)")
    parser.add_argument("document_type", help="Type of document (e.g., 'type1', 'type2')")
    parser.add_argument("--output_dir", default="output", help="Directory to save the output files")
    parser.add_argument("--headers", default="Nom et prénom,Identifiant", help="Comma-separated list of headers for the CSV file")
    parser.add_argument("--use_ocr_space", action="store_true", help="Use OCR.space API instead of local OCR")
    parser.add_argument("--api_key", default="K888957", help="OCR.space API key (only used if use_ocr_space is True)")

    args = parser.parse_args()

    try:
        output_file = process_pdf(
            args.pdf_path,
            args.page_num,
            args.document_type,
            args.output_dir,
            args.headers,
            args.use_ocr_space,
            args.api_key
        )
        logger.info(f"Output saved to {output_file}")
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
