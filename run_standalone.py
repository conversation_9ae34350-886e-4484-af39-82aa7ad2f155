#!/usr/bin/env python3
"""
Standalone script for JORTPROC-GenerateSubtables subproject.

This script can be run independently to generate subtables from a PDF page.
"""
import argparse
import logging
import sys
import os
from pathlib import Path

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import from the subproject
from generate_subtables import generate_subtables

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def main():
    """Main entry point for the standalone script."""
    parser = argparse.ArgumentParser(description="Generate subtables from a PDF page")
    parser.add_argument("pdf_path", help="Path to the PDF file")
    parser.add_argument("page_num", type=int, help="Page number to process (1-based)")
    parser.add_argument("document_type", help="Type of document (e.g., 'type1', 'type2')")
    parser.add_argument("--output_dir", default="output/subtables", help="Directory to save the output files")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    # Set up logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

    try:
        # Generate subtables
        output_dir = generate_subtables(
            Path(args.pdf_path),
            args.page_num,
            args.document_type,
            Path(args.output_dir)
        )
        logger.info(f"Generated subtables in {output_dir}")
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
