#!/usr/bin/env python3
"""
Test script for testing column row count mismatch validation in OCR.space.
"""
import os
import sys
import logging
import shutil
from pathlib import Path

# Add the subproject to the Python path
sys.path.append(str(Path("Subprojects/JORTPROC_OCRSpace")))

# Import from the subproject
from ocr_space import main as ocr_space_main

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def create_test_data():
    """Create test data with mismatched column counts."""
    # Create test directory
    test_dir = Path("test_output/type2_test/column_mismatch")
    test_dir.mkdir(parents=True, exist_ok=True)

    # Copy the original column files
    src_dir = Path("test_output/type2_test/subtables_columns/page1")

    # Copy column 1 (names) - keep all rows
    shutil.copy(
        src_dir / "page1_table1_column1_inverted_lined.png",
        test_dir / "page1_table1_column1_inverted_lined.png"
    )

    # Copy column 2 (CIN) - keep all rows
    shutil.copy(
        src_dir / "page1_table1_column2_inverted_lined.png",
        test_dir / "page1_table1_column2_inverted_lined.png"
    )

    # Copy column 3 (addresses) - but we'll truncate it to simulate a mismatch
    # Instead of actually truncating the image, we'll just rename it to something
    # that won't be picked up by the OCR.space code
    shutil.copy(
        src_dir / "page1_table1_column3_inverted_lined.png",
        test_dir / "page1_table1_column3_inverted_lined.png.bak"
    )

    # For column 3, we'll create a text file with just a few entries
    # This will simulate a significant mismatch in row counts
    with open(test_dir / "page1_table1_column3_inverted_lined.txt", "w") as f:
        f.write("ADRESSE\nAddress 1\nAddress 2\nAddress 3\nAddress 4\nAddress 5")

    # Now we'll create a small image with just a few rows for column 3
    # We'll use a dummy image that will be processed by OCR.space
    # but will result in much fewer rows than columns 1 and 2
    import numpy as np
    import cv2

    # Create a blank white image
    img = np.ones((300, 200), np.uint8) * 255

    # Add some text to the image
    cv2.putText(img, "ADRESSE", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Address 1", (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Address 2", (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Address 3", (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Address 4", (10, 190), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Address 5", (10, 230), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

    # Invert the image (black text on white background -> white text on black background)
    img = cv2.bitwise_not(img)

    # Save the image
    cv2.imwrite(str(test_dir / "page1_table1_column3_inverted_lined.png"), img)

    logger.info(f"Created test data in {test_dir}")
    return test_dir

def main():
    """Main entry point for the test script."""
    # Create test data
    test_dir = create_test_data()

    # Define output file
    output_file = Path("test_output/type2_test/column_mismatch_results.csv")

    # Run OCR.space on the test data
    logger.info(f"Running OCR.space on {test_dir}")
    ocr_space_main(
        input_dir=str(test_dir),
        headers="NOMS & PRENOMS,CIN,ADRESSE",
        output_file=str(output_file)
    )

    # Check if error file was created
    error_file = output_file.with_suffix('.error')
    if error_file.exists():
        logger.info(f"Error file created: {error_file}")
        with open(error_file, 'r') as f:
            logger.info(f"Error message: {f.read().strip()}")
    else:
        logger.warning(f"No error file created at {error_file}")

    # Check if empty output file was created
    if output_file.exists():
        logger.info(f"Output file created: {output_file}")
        with open(output_file, 'r') as f:
            logger.info(f"Output file content: {f.read().strip()}")
    else:
        logger.warning(f"No output file created at {output_file}")

if __name__ == "__main__":
    main()
