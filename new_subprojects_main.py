#!/usr/bin/env python3
"""
JortNamesDataExtractor - Subprojects Main Program

This program extracts data from Tunisian Official Gazette (JORT) PDFs.
It uses the following subprojects:
- JORTPROC-GenerateSubtables: Transforms a PDF page into subtables
- JORTPROC-OCRLoc: Processes OCR on images using local OCR code
- JORTPROC-OCRSpace: Processes OCR on images using OCR.space API

It supports both type1 and type2 documents, and can process single pages or full PDFs.
"""
import argparse
import logging
import os
import sys
import subprocess
from pathlib import Path
import pandas as pd
import PyPDF2

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def get_pdf_page_count(pdf_path):
    """Get the number of pages in a PDF file."""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            return len(pdf_reader.pages)
    except Exception as e:
        logger.error(f"Error getting page count: {e}")
        return 0

def process_single_page(pdf_path, document_type, output_dir,
                headers, use_ocr_space=False, api_key='K888957',
                page_num=1):
    """
    Process a single page of a PDF file.

    Args:
        pdf_path: Path to the PDF file
        document_type: Type of document (e.g., 'type1', 'type2')
        output_dir: Directory to save the output files
        headers: Comma-separated list of headers for the CSV file
        use_ocr_space: Whether to use OCR.space API instead of local OCR
        api_key: OCR.space API key (only used if use_ocr_space is True)
        page_num: Page number to process (1-based)

    Returns:
        DataFrame with the OCR results, or None if processing failed
    """
    try:
        # Convert paths to Path objects
        pdf_path = Path(pdf_path)
        pdf_name = pdf_path.stem

        # Create output directory for this page
        output_dir = Path(output_dir)
        page_output_dir = output_dir / f"page{page_num}"
        page_output_dir.mkdir(parents=True, exist_ok=True)

        # Step 1: Generate subtables from the PDF page
        logger.info(f"Step 1: Generating subtables from {pdf_path}, page {page_num}")

        # Run the generate_subtables script
        generate_cmd = [
            "python3", "Subprojects/JORTPROC_GenerateSubtacles/generate_subtables.py",
            str(pdf_path), str(page_num), document_type, "--output_dir", str(page_output_dir)
        ]

        logger.info(f"Running command: {' '.join(generate_cmd)}")
        subprocess.run(generate_cmd, check=True)

        # Check if the output files exist
        # Try to find column files with the correct page number pattern
        column_files = list(page_output_dir.glob(f"page{page_num}/page{page_num}_table1_column*_inverted_lined.png"))

        # If not found, check if they're directly in the page directory without the subfolder
        if not column_files:
            column_files = list(page_output_dir.glob(f"page{page_num}_table1_column*_inverted_lined.png"))

        if column_files:
            logger.info(f"Found {len(column_files)} column files for page {page_num}")

            # Determine the correct input directory based on where the column files were found
            if "page" in str(column_files[0]):
                # If files are in a subfolder like page1/page1_table1_column*
                input_dir = page_output_dir / f"page{page_num}"
            else:
                # If files are directly in the page directory
                input_dir = page_output_dir

            # Step 2: Process OCR on the subtables
            if use_ocr_space:
                logger.info(f"Step 2: Processing OCR using OCR.space API")
                output_file = page_output_dir / f"{pdf_name}_page{page_num}_ocr_results.csv"

                ocr_cmd = [
                    "python3", "Subprojects/JORTPROC_OCRSpace/run_standalone.py",
                    str(input_dir), "--headers", headers,
                    "--output_file", str(output_file)
                ]

                logger.info(f"Running command: {' '.join(ocr_cmd)}")
                subprocess.run(ocr_cmd, check=True)
            else:
                logger.info(f"Step 2: Processing OCR using local OCR")
                output_file = page_output_dir / f"{pdf_name}_page{page_num}_ocr_local.csv"

                ocr_cmd = [
                    "python3", "Subprojects/JORTPROC_OCRLoc/run_standalone.py",
                    str(input_dir), "--headers", headers,
                    "--output_file", str(output_file)
                ]

                logger.info(f"Running command: {' '.join(ocr_cmd)}")
                subprocess.run(ocr_cmd, check=True)

            # Check if the OCR results file exists
            if output_file.exists():
                logger.info(f"OCR results file exists for page {page_num}: {output_file}")

                # Read the OCR results
                df = pd.read_csv(output_file)
                logger.info(f"OCR results for page {page_num} have {len(df)} rows")

                return df
            else:
                logger.error(f"OCR results file does not exist for page {page_num}: {output_file}")
        else:
            logger.error(f"No column files found for page {page_num}")

    except subprocess.CalledProcessError as e:
        logger.error(f"Error processing page {page_num}: {e}")
    except Exception as e:
        logger.error(f"Error processing page {page_num}: {e}")

    return None

def process_full_pdf(pdf_path, document_type, output_dir, headers,
                    use_ocr_space=False, api_key='K888957',
                    start_page=1, end_page=None):
    """
    Process all pages of a PDF file.

    Args:
        pdf_path: Path to the PDF file
        document_type: Type of document (e.g., 'type1', 'type2')
        output_dir: Directory to save the output files
        headers: Comma-separated list of headers for the CSV file
        use_ocr_space: Whether to use OCR.space API instead of local OCR
        api_key: OCR.space API key (only used if use_ocr_space is True)
        start_page: First page to process (1-based)
        end_page: Last page to process (1-based), or None to process all pages

    Returns:
        Path to the combined OCR results file
    """
    # Get the PDF file name without extension to use in output paths
    pdf_path = Path(pdf_path)
    pdf_name = pdf_path.stem

    # Create output directory with the PDF name
    output_dir = Path(output_dir)
    if not output_dir.name.startswith(pdf_name):
        output_dir = output_dir / pdf_name.replace(" ", "_")
    output_dir.mkdir(parents=True, exist_ok=True)

    # Get the number of pages in the PDF
    num_pages = get_pdf_page_count(pdf_path)
    logger.info(f"PDF has {num_pages} pages")

    if end_page is None:
        end_page = num_pages

    # Validate page range
    start_page = max(1, start_page)
    end_page = min(num_pages, end_page)

    # Process each page
    all_results = []
    for page_num in range(start_page, end_page + 1):
        df = process_single_page(
            pdf_path,
            document_type,
            output_dir,
            headers,
            use_ocr_space,
            api_key,
            page_num
        )
        if df is not None:
            all_results.append(df)

    # Combine all results
    if all_results:
        combined_df = pd.concat(all_results, ignore_index=True)

        # Save the combined results with the PDF name
        ocr_type = "ocr_space" if use_ocr_space else "ocr_local"
        combined_output_file = output_dir / f"{pdf_name}_combined_{ocr_type}_results.csv"
        combined_df.to_csv(combined_output_file, index=False)
        logger.info(f"Saved combined OCR results with {len(combined_df)} rows to {combined_output_file}")
        return str(combined_output_file)
    else:
        logger.error("No OCR results to combine")
        return None

def main():
    """Main entry point for the program."""
    parser = argparse.ArgumentParser(description="Extract data from Tunisian Official Gazette (JORT) PDFs")
    parser.add_argument("input", help="Path to PDF file or directory containing PDF files")
    parser.add_argument("-o", "--output", help="Output directory for extracted data", default="output")
    parser.add_argument("-d", "--doc-type", help="Document type (type1, type2)", required=True)
    parser.add_argument("--ocr-space", action="store_true", help="Use OCR.space API instead of local OCR")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--page", type=int, default=None, help="Page number to process (1-based). If not specified, process all pages.")
    parser.add_argument("--start-page", type=int, default=1, help="Start page number for full PDF processing (1-based)")
    parser.add_argument("--end-page", type=int, default=None, help="End page number for full PDF processing (1-based)")
    parser.add_argument("--headers", default="Nom et prénom,Identifiant", help="Comma-separated list of headers for the CSV file")
    parser.add_argument("--api-key", default="K888957", help="OCR.space API key (only used if ocr-space is True)")

    args = parser.parse_args()

    # Set up logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

    try:
        # Process single file or directory
        input_path = Path(args.input)
        if input_path.is_file():
            # Process the PDF file
            logger.info(f"Processing {input_path}...")

            if args.page is not None:
                # Process a single page
                logger.info(f"Processing page {args.page} of {input_path}...")
                output_file = process_single_page(
                    str(input_path),
                    args.doc_type,
                    args.output,
                    args.headers,
                    args.ocr_space,
                    args.api_key,
                    args.page
                )
                if output_file is not None:
                    logger.info(f"Output saved to {output_file}")
            else:
                # Process all pages
                logger.info(f"Processing all pages of {input_path}...")
                output_file = process_full_pdf(
                    str(input_path),
                    args.doc_type,
                    args.output,
                    args.headers,
                    args.ocr_space,
                    args.api_key,
                    args.start_page,
                    args.end_page
                )
                if output_file is not None:
                    logger.info(f"Combined output saved to {output_file}")
        elif input_path.is_dir():
            # Process all PDF files in the directory
            pdf_files = list(input_path.glob('*.pdf'))
            logger.info(f"Found {len(pdf_files)} PDF files in {input_path}")

            for pdf_file in pdf_files:
                logger.info(f"Processing {pdf_file}...")
                if args.page is not None:
                    # Process a single page
                    logger.info(f"Processing page {args.page} of {pdf_file}...")
                    output_file = process_single_page(
                        str(pdf_file),
                        args.doc_type,
                        args.output,
                        args.headers,
                        args.ocr_space,
                        args.api_key,
                        args.page
                    )
                    if output_file is not None:
                        logger.info(f"Output saved to {output_file}")
                else:
                    # Process all pages
                    logger.info(f"Processing all pages of {pdf_file}...")
                    output_file = process_full_pdf(
                        str(pdf_file),
                        args.doc_type,
                        args.output,
                        args.headers,
                        args.ocr_space,
                        args.api_key,
                        args.start_page,
                        args.end_page
                    )
                    if output_file is not None:
                        logger.info(f"Combined output saved to {output_file}")
        else:
            logger.error(f"Input path {input_path} does not exist or is not a file or directory")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
