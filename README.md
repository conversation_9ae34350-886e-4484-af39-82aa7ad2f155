# JortExtractor

A Python pipeline for extracting tables from Tunisian Official Gazette (Jort) PDFs.

## Features

- Detects and crops tables from PDF pages using OpenCV
- Performs OCR on table cells with Tesseract (supports French and English)
- Normalizes extracted data into consistent structure
- Outputs CSV files matching input PDF names
- Handles multiple table formats (1-3 columns)
- Configurable through YAML config file

## Installation

### System Requirements

```bash
# Ubuntu/Debian
sudo apt-get install poppler-utils tesseract-ocr tesseract-ocr-fra

# MacOS (using Homebrew)
brew install poppler tesseract tesseract-lang
```

### Python Package

```bash
pip install -r requirements.txt
```

## Usage

### Basic Command (Document Type Required)

```bash
python main.py input.pdf -d type1  # Options: type1, type2, type3, type4
```

### With Custom Config

```bash
python main.py input.pdf -d type2 -c config.yaml
```

### Batch Processing

```bash
python main.py pdf_directory/ -d type3 -o output_dir/
```

## Configuration

Edit `config.yaml` to customize:

- Input/output directories
- PDF processing settings (DPI, image format)
- Table detection parameters
- OCR settings (languages, confidence thresholds)
- Field extraction patterns

## Development

### Setup Development Environment

```bash
# Clone the repository
git clone https://github.com/yourusername/jortextractor.git
cd jortextractor

# Create a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -r requirements-dev.txt

# Install the package in development mode
pip install -e .
```

### Running Tests

```bash
# Run all tests
python -m tests.run_tests

# Run specific test types
python -m tests.run_tests --type unit
python -m tests.run_tests --type integration
python -m tests.run_tests --type performance
python -m tests.run_tests --type robustness
python -m tests.run_tests --type security
python -m tests.run_tests --type logging

# Run tests in parallel
python -m tests.run_tests --parallel --workers 4

# Run specific test modules
python -m unittest tests.unit.test_document_classifier
python -m unittest tests.integration.test_ground_truth_comparison
```

### Code Quality

```bash
# Format code with Black
black .

# Sort imports with isort
isort .

# Check linting with flake8
flake8 jortextractor/

# Run type checking with mypy
mypy jortextractor/
```

### Building Documentation

```bash
# Generate documentation
cd docs
sphinx-build -b html . _build/html
```

## Testing Strategy

The JortExtractor system includes a comprehensive testing strategy with the following components:

### Unit Testing
- Document classification testing
- Image processing testing
- Name validation testing
- OCR processing testing
- Table detection testing

### Integration Testing
- Pipeline integration testing
- Ground truth comparison testing
- Pre-OCR image saving testing

### Performance Testing
- Processing time testing
- Memory usage testing
- Parallel processing testing

### Robustness Testing
- Empty page handling
- Rotated page handling
- High noise handling
- Low resolution handling

### Security Testing
- Path traversal prevention
- Invalid input handling
- Malformed PDF handling
- Very large input handling

### Logging and Monitoring Testing
- Log output testing
- Log levels testing
- Error reporting testing

Test results are saved to the `test_reports` directory, including CSV files with detailed metrics and charts visualizing the results.

## Project Structure

```
JortDataExtractor/
├── jortextractor/              # Main package
│   ├── core/                   # Core feature
│   │   ├── __init__.py
│   │   ├── document_classifier.py
│   │   ├── image_processor.py
│   │   ├── name_validator.py
│   │   └── pipeline.py
│   ├── utils/                  # Utility modules
│   │   ├── __init__.py
│   │   ├── config.py
│   │   └── post_processor.py
│   ├── benchmarks/             # Benchmarking tools
│   │   ├── __init__.py
│   │   ├── ocr_benchmark.py
│   │   └── ocr_space_client.py
│   ├── parallel/               # Parallel processing
│   │   ├── __init__.py
│   │   └── parallel_processor.py
│   └── __init__.py
├── scripts/                    # Utility scripts
│   ├── benchmark_ocr.py
│   ├── compare_ocr.py
│   ├── evaluate_tests.py
│   ├── run_all_tests.py
│   ├── test_all_ground_truth.py
│   └── validate_against_ground_truth.py
├── tests/                      # Test suite
│   ├── integration/            # Integration tests
│   │   ├── test_ground_truth_comparison.py
│   │   ├── test_pipeline_integration.py
│   │   └── test_pre_ocr_image_saving.py
│   ├── logging/                # Logging tests
│   │   └── test_logging.py
│   ├── performance/            # Performance tests
│   │   ├── test_memory_usage.py
│   │   ├── test_parallel_processing.py
│   │   └── test_performance.py
│   ├── robustness/             # Robustness tests
│   │   └── test_robustness.py
│   ├── security/               # Security tests
│   │   └── test_security.py
│   ├── unit/                   # Unit tests
│   │   ├── test_document_classifier.py
│   │   ├── test_document_classifier_enhanced.py
│   │   ├── test_image_processing_enhanced.py
│   │   ├── test_name_validation_enhanced.py
│   │   ├── test_name_validator.py
│   │   ├── test_ocr_accuracy.py
│   │   ├── test_parallel_processor.py
│   │   ├── test_table_detection.py
│   │   └── test_type4_support.py
│   ├── conftest.py             # Test configuration
│   └── test_utils.py           # Test utilities
├── config/                     # Configuration files
│   └── config.yaml             # Default configuration
├── data/                       # Data directory
│   ├── examples/               # Example data
│   ├── input/                  # Input data
│   └── output/                 # Output data
├── results/                    # Results directory
│   ├── ocr_benchmark/          # OCR benchmark results
│   ├── ocr_comparison/         # OCR comparison results
│   ├── ground_truth/           # Ground truth results
│   └── test_reports/           # Test reports
├── docs/                       # Documentation
│   ├── images/                 # Documentation images
│   ├── user_guide/             # User guides and tutorials
│   ├── planning/               # Planning documents
│   │   ├── critical_issues_plan.md
│   │   ├── test_enhancement_plan.md
│   │   ├── test_evaluation_plan.md
│   │   ├── test_evaluation_template.md
│   │   └── TEST_EVALUATION_README.md
│   └── tracking/               # Project tracking
│       ├── Features.txt
│       ├── test_tracking_spreadsheet.csv
│       └── ToDo.txt
├── logs/                       # Log files
├── .github/                    # GitHub workflows and templates
├── setup.py                    # Package setup file
├── pyproject.toml              # Python project configuration
├── requirements.txt            # Project dependencies
├── requirements-dev.txt        # Development dependencies
├── CHANGELOG.md                # Project changelog
├── CONTRIBUTING.md             # Contributing guidelines
├── .gitignore                  # Git ignore file
├── README.md                   # Project README
└── main.py                     # Main entry point
```

## License

MIT
