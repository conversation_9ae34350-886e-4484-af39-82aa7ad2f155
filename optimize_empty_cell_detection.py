#!/usr/bin/env python3
"""
Script to optimize empty cell detection parameters for type2 documents.

This script runs multiple tests with different empty cell detection parameters
and finds the optimal values that result in the best column alignment and OCR accuracy.
"""
import os
import sys
import logging
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import re
import subprocess
import shutil

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

# Define constants
OUTPUT_DIR = Path("test_output/empty_cell_optimization")
RESULTS_FILE = OUTPUT_DIR / "optimization_results.json"
GENERATE_SUBTABLES_PATH = Path("Subprojects/JORTPROC_GenerateSubtacles/generate_subtables.py")
BACKUP_PATH = Path("Subprojects/JORTPROC_GenerateSubtacles/generate_subtables.py.bak")

def setup():
    """Set up the optimization environment."""
    # Create output directory
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    
    # Create a backup of the original generate_subtables.py file
    if not BACKUP_PATH.exists():
        shutil.copy(GENERATE_SUBTABLES_PATH, BACKUP_PATH)
        logger.info(f"Created backup of {GENERATE_SUBTABLES_PATH} at {BACKUP_PATH}")
    
    logger.info("Setup complete")

def restore_original():
    """Restore the original generate_subtables.py file."""
    if BACKUP_PATH.exists():
        shutil.copy(BACKUP_PATH, GENERATE_SUBTABLES_PATH)
        logger.info(f"Restored original {GENERATE_SUBTABLES_PATH} from {BACKUP_PATH}")
    else:
        logger.warning(f"Backup file {BACKUP_PATH} not found, cannot restore original")

def modify_parameters(white_pixel_threshold, contour_area_threshold, minimal_content_threshold):
    """
    Modify the empty cell detection parameters in generate_subtables.py.
    
    Args:
        white_pixel_threshold: Threshold for white pixel count (e.g., 0.022)
        contour_area_threshold: Threshold for contour area (e.g., 0.02)
        minimal_content_threshold: Threshold for minimal content detection (e.g., 0.035)
    """
    # Read the original file
    with open(GENERATE_SUBTABLES_PATH, 'r') as f:
        content = f.read()
    
    # Replace the white pixel threshold
    content = re.sub(
        r'if white_pixel_count < cell_area \* ([\d\.]+):  # Slightly more aggressive than 2%',
        f'if white_pixel_count < cell_area * {white_pixel_threshold}:  # Slightly more aggressive than 2%',
        content
    )
    
    # Replace the contour area threshold
    content = re.sub(
        r'if len\(contours\) <= 2 and total_contour_area < cell_area \* ([\d\.]+):',
        f'if len(contours) <= 2 and total_contour_area < cell_area * {contour_area_threshold}:',
        content
    )
    
    # Replace the minimal content threshold
    content = re.sub(
        r'is_minimal_content = \(len\(contours\) <= 3 and total_contour_area < cell_area \* ([\d\.]+)\)',
        f'is_minimal_content = (len(contours) <= 3 and total_contour_area < cell_area * {minimal_content_threshold})',
        content
    )
    
    # Write the modified file
    with open(GENERATE_SUBTABLES_PATH, 'w') as f:
        f.write(content)
    
    logger.info(f"Modified parameters: white_pixel={white_pixel_threshold}, contour_area={contour_area_threshold}, minimal_content={minimal_content_threshold}")

def run_test():
    """
    Run the column alignment test with the current parameters.
    
    Returns:
        Dictionary with test results
    """
    # Run the test script
    cmd = ["python3", "test_type2_column_alignment.py"]
    logger.info(f"Running command: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
        
        # Load the test results
        results_file = Path("test_output/type2_column_alignment_test/test_results.json")
        if results_file.exists():
            with open(results_file, 'r') as f:
                results = json.load(f)
            return results
        else:
            logger.error(f"Test results file {results_file} not found")
            return None
    except subprocess.CalledProcessError as e:
        logger.error(f"Error running test: {e}")
        return None

def evaluate_parameters(white_pixel_thresholds, contour_area_thresholds, minimal_content_thresholds):
    """
    Evaluate different combinations of parameters.
    
    Args:
        white_pixel_thresholds: List of white pixel thresholds to test
        contour_area_thresholds: List of contour area thresholds to test
        minimal_content_thresholds: List of minimal content thresholds to test
        
    Returns:
        Dictionary with evaluation results
    """
    results = {}
    
    # Test each combination of parameters
    for wp_threshold in white_pixel_thresholds:
        for ca_threshold in contour_area_thresholds:
            for mc_threshold in minimal_content_thresholds:
                # Create a key for this combination
                key = f"wp{wp_threshold}_ca{ca_threshold}_mc{mc_threshold}"
                
                # Modify the parameters
                modify_parameters(wp_threshold, ca_threshold, mc_threshold)
                
                # Run the test
                test_results = run_test()
                
                if test_results:
                    # Extract the best threshold and its results
                    best_threshold = None
                    best_score = -1
                    
                    for threshold, threshold_results in test_results.items():
                        # Calculate a combined score based on alignment and F1 score
                        alignment_score = 1.0 if threshold_results['alignment']['is_aligned'] else 0.0
                        f1_score = threshold_results['comparison']['overall_f1']
                        
                        # Weighted score (alignment is more important)
                        combined_score = (0.7 * alignment_score) + (0.3 * f1_score)
                        
                        if combined_score > best_score:
                            best_score = combined_score
                            best_threshold = threshold
                    
                    if best_threshold:
                        # Store the results for this combination
                        results[key] = {
                            'parameters': {
                                'white_pixel_threshold': wp_threshold,
                                'contour_area_threshold': ca_threshold,
                                'minimal_content_threshold': mc_threshold
                            },
                            'best_threshold': best_threshold,
                            'best_score': best_score,
                            'alignment': test_results[best_threshold]['alignment'],
                            'comparison': test_results[best_threshold]['comparison']
                        }
                        
                        logger.info(f"Combination {key}: Best threshold={best_threshold}, Score={best_score:.4f}")
    
    # Save all results
    with open(RESULTS_FILE, 'w') as f:
        json.dump(results, f, indent=2)
    
    return results

def find_optimal_parameters(results):
    """
    Find the optimal parameters based on the evaluation results.
    
    Args:
        results: Dictionary with evaluation results
        
    Returns:
        Dictionary with optimal parameters
    """
    # Find the combination with the highest score
    best_key = None
    best_score = -1
    
    for key, result in results.items():
        if result['best_score'] > best_score:
            best_score = result['best_score']
            best_key = key
    
    if best_key:
        optimal_params = results[best_key]['parameters']
        logger.info(f"Optimal parameters: {optimal_params}")
        logger.info(f"Best score: {best_score:.4f}")
        
        # Apply the optimal parameters
        modify_parameters(
            optimal_params['white_pixel_threshold'],
            optimal_params['contour_area_threshold'],
            optimal_params['minimal_content_threshold']
        )
        
        return optimal_params
    else:
        logger.error("No optimal parameters found")
        return None

def plot_results(results):
    """
    Plot the evaluation results.
    
    Args:
        results: Dictionary with evaluation results
    """
    # Extract data for plotting
    keys = list(results.keys())
    scores = [result['best_score'] for result in results.values()]
    alignment_scores = [1.0 if result['alignment']['is_aligned'] else 0.0 for result in results.values()]
    f1_scores = [result['comparison']['overall_f1'] for result in results.values()]
    
    # Create a figure with multiple subplots
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 18))
    
    # Plot combined scores
    ax1.bar(keys, scores)
    ax1.set_title('Combined Scores')
    ax1.set_xlabel('Parameter Combinations')
    ax1.set_ylabel('Score')
    ax1.set_xticklabels(keys, rotation=90)
    
    # Plot alignment scores
    ax2.bar(keys, alignment_scores)
    ax2.set_title('Alignment Scores (1.0 = Aligned)')
    ax2.set_xlabel('Parameter Combinations')
    ax2.set_ylabel('Score')
    ax2.set_xticklabels(keys, rotation=90)
    
    # Plot F1 scores
    ax3.bar(keys, f1_scores)
    ax3.set_title('F1 Scores')
    ax3.set_xlabel('Parameter Combinations')
    ax3.set_ylabel('Score')
    ax3.set_xticklabels(keys, rotation=90)
    
    # Adjust layout and save the figure
    plt.tight_layout()
    plt.savefig(OUTPUT_DIR / 'results_plot.png')
    logger.info(f"Saved results plot to {OUTPUT_DIR / 'results_plot.png'}")

def main():
    """Main entry point for the script."""
    # Set up the optimization environment
    setup()
    
    try:
        # Define parameter ranges to test
        white_pixel_thresholds = [0.018, 0.020, 0.022, 0.025]
        contour_area_thresholds = [0.015, 0.020, 0.025]
        minimal_content_thresholds = [0.030, 0.035, 0.040]
        
        # Evaluate different combinations of parameters
        results = evaluate_parameters(white_pixel_thresholds, contour_area_thresholds, minimal_content_thresholds)
        
        # Find the optimal parameters
        optimal_params = find_optimal_parameters(results)
        
        # Plot the results
        plot_results(results)
        
        # Print a summary of the optimization
        logger.info("=== Optimization Summary ===")
        logger.info(f"Tested {len(results)} parameter combinations")
        if optimal_params:
            logger.info(f"Optimal parameters: {optimal_params}")
        
        return 0
    finally:
        # Restore the original generate_subtables.py file
        restore_original()

if __name__ == "__main__":
    sys.exit(main())
