#!/usr/bin/env python3
"""
Standalone script for JORTPROC-OCRLoc subproject.

This script can be run independently to process OCR on images using local OCR code.
"""
import argparse
import logging
import sys
import os
from pathlib import Path

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import from the subproject
from ocr_local import process_ocr_local

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def main():
    """Main entry point for the standalone script."""
    parser = argparse.ArgumentParser(description="Process OCR on images using local OCR code")
    parser.add_argument("input_dir", help="Directory containing the images to process")
    parser.add_argument("--headers", default="Nom et prénom,Identifiant", help="Comma-separated list of headers for the CSV file")
    parser.add_argument("--output_file", default="output/ocr_local_results.csv", help="Path to the output CSV file")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    # Set up logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

    try:
        # Process OCR
        output_file = process_ocr_local(
            Path(args.input_dir),
            args.headers,
            Path(args.output_file)
        )
        logger.info(f"Saved OCR results to {output_file}")
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
