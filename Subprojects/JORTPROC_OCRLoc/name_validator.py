"""
Name validation and normalization functions for the JORTPROC-OCRLoc subproject.

This module contains functions copied from the main project to make the subproject standalone.
"""
import re
import unicodedata
import logging

logger = logging.getLogger(__name__)

def normalize_text(text: str) -> str:
    """Normalize text by removing diacritics, converting to uppercase, and removing extra spaces.

    Args:
        text: The input text to normalize

    Returns:
        Normalized text
    """
    if not text:
        return ""

    # Convert to uppercase
    text = text.upper()

    # Remove diacritics (accents)
    text = ''.join(c for c in unicodedata.normalize('NFD', text)
                  if unicodedata.category(c) != 'Mn')

    # Replace multiple spaces with a single space
    text = re.sub(r'\s+', ' ', text)

    # Remove leading and trailing spaces
    text = text.strip()

    return text

def validate_and_correct_name(name: str) -> str:
    """Validate and correct a name.

    Args:
        name: The name to validate and correct

    Returns:
        Corrected name
    """
    if not name:
        return ""

    # Remove any non-alphanumeric characters except spaces, hyphens, and apostrophes
    name = re.sub(r'[^\w\s\'-]', '', name)

    # Replace multiple spaces with a single space
    name = re.sub(r'\s+', ' ', name)

    # Remove leading and trailing spaces
    name = name.strip()

    return name

def is_valid_name(name: str) -> bool:
    """Check if a name is valid.

    Args:
        name: The name to check

    Returns:
        True if the name is valid, False otherwise
    """
    if not name:
        return False

    # Check if the name contains at least one letter
    if not re.search(r'[a-zA-Z]', name):
        return False

    # Check if the name is too short
    if len(name) < 2:
        return False

    # Check if the name contains too many digits
    digit_count = sum(c.isdigit() for c in name)
    if digit_count > len(name) / 3:
        return False

    return True

def is_valid_identifier(identifier: str) -> bool:
    """Check if an identifier is valid.

    Args:
        identifier: The identifier to check

    Returns:
        True if the identifier is valid, False otherwise
    """
    if not identifier:
        return False

    # Check if the identifier matches the expected format
    if re.match(r'\*{5}\d+', identifier):
        return True

    # Check if the identifier contains at least one digit
    if not re.search(r'\d', identifier):
        return False

    return True

def format_identifier(identifier: str) -> str:
    """Format an identifier to the standard format.

    Args:
        identifier: The identifier to format

    Returns:
        Formatted identifier
    """
    if not identifier:
        return ""

    # Extract only the digits
    digits = ''.join(c for c in identifier if c.isdigit())

    # If no digits, return empty string
    if not digits:
        return ""

    # Format with 5 asterisks
    return f"*****{digits}"
