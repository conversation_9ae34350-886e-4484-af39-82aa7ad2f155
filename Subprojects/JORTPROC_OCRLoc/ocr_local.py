"""
Process OCR on images using local OCR code.

This module takes all images in a folder and processes OCR on them with our local OCR code.
"""
import logging
import os
import re
import csv
from pathlib import Path
from typing import List, Dict, Optional, Union, Tuple
import numpy as np
import pandas as pd
import cv2
import pytesseract

# Import local helpers - use absolute imports for standalone execution
try:
    # When imported as a module
    from .name_validator import (
        validate_and_correct_name,
        normalize_text
    )
except ImportError:
    # When run as a standalone script
    from name_validator import (
        validate_and_correct_name,
        normalize_text
    )

logger = logging.getLogger(__name__)

def process_ocr_local(input_dir: Union[str, Path], headers: str, output_file: Union[str, Path]) -> Path:
    """
    Process OCR on all images in a folder using local OCR code.

    Args:
        input_dir: Directory containing the subtable images
        headers: Comma-separated list of headers for the CSV file
        output_file: Path to the output CSV file

    Returns:
        Path to the output CSV file
    """
    # Convert paths to Path objects
    input_dir = Path(input_dir)
    output_file = Path(output_file)

    # Create output directory if it doesn't exist
    output_file.parent.mkdir(parents=True, exist_ok=True)

    # Parse headers
    header_list = [h.strip() for h in headers.split(',')]
    logger.info(f"Using headers: {header_list}")

    # Find all PNG images in the input directory
    image_files = sorted(list(input_dir.glob('*_inverted_lined.png')))
    logger.info(f"Found {len(image_files)} images in {input_dir}")

    # Process each image
    all_data = []
    for img_path in image_files:
        logger.info(f"Processing {img_path}")

        # Extract data from the image
        data = _extract_data_from_image(img_path)

        # Add the data to the result
        all_data.extend(data)

    # Create a DataFrame from the extracted data
    if all_data:
        # If we have exactly two headers, assume they are 'text' and 'identifier'
        if len(header_list) == 2:
            df = pd.DataFrame(all_data, columns=['text', 'identifier'])
            # Rename columns to match the provided headers
            df.columns = header_list
        else:
            # Otherwise, use the default column names
            df = pd.DataFrame(all_data)
            # If we have more headers than columns, add empty columns
            for i in range(len(df.columns), len(header_list)):
                df[f'column_{i}'] = ''
            # Rename columns to match the provided headers
            df.columns = header_list[:len(df.columns)]

        # Save the DataFrame to CSV
        df.to_csv(output_file, index=False)
        logger.info(f"Saved {len(df)} rows to {output_file}")
    else:
        # Create an empty DataFrame with the provided headers
        df = pd.DataFrame(columns=header_list)
        df.to_csv(output_file, index=False)
        logger.info(f"No data extracted. Saved empty CSV to {output_file}")

    return output_file

def _extract_data_from_image(image_path: Path) -> List[Dict]:
    """
    Extract data from an image using local OCR.

    Args:
        image_path: Path to the image file

    Returns:
        List of dictionaries with 'text' and 'identifier' keys
    """
    try:
        # Read the image
        img = cv2.imread(str(image_path))
        if img is None:
            logger.error(f"Failed to read image: {image_path}")
            return []

        # Convert to grayscale if needed
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()

        # Apply adaptive thresholding to enhance text
        # This helps with varying lighting conditions
        binary = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        # Invert the image if it's not already inverted
        # Check if the image is already inverted by counting white pixels
        white_pixel_count = np.sum(binary == 255)
        black_pixel_count = np.sum(binary == 0)

        if white_pixel_count > black_pixel_count:
            # Image is not inverted, so invert it
            binary = cv2.bitwise_not(binary)

        # Apply OCR with optimized configuration
        # PSM 6: Assume a single uniform block of text
        # OEM 3: Default, based on what is available
        custom_config = f"--psm 6 --oem 3 -l eng+fra --dpi 300 -c preserve_interword_spaces=1"
        text = pytesseract.image_to_string(binary, config=custom_config).strip()

        # If we don't get any text, try with the original image
        if not text:
            logger.info(f"No text found with binary image, trying with original image")
            text = pytesseract.image_to_string(img, config=custom_config).strip()

        # Process the OCR text
        return _process_ocr_text(text, image_path.name)
    except Exception as e:
        logger.error(f"Error extracting data from {image_path}: {e}")
        return []

def _process_ocr_text(text: str, image_name: str) -> List[Dict]:
    """
    Process OCR text to extract names and identifiers.

    Args:
        text: OCR text
        image_name: Name of the image file (for logging)

    Returns:
        List of dictionaries with 'text' and 'identifier' keys
    """
    # Log the raw OCR text for debugging
    logger.info(f"Raw OCR text from {image_name} (first 200 chars): {text[:200]}")

    # Split by lines and filter out empty lines
    lines = [line.strip() for line in text.split('\n') if line.strip()]

    # Skip the first line if it looks like a header
    if lines and ('Nom et prénom' in lines[0] or 'Identifiant' in lines[0] or 'Nom' in lines[0]):
        lines = lines[1:]

    # Detect if this is a Type3 document (single column format)
    # Type3 documents typically have names without identifiers
    is_type3 = _detect_type3_format(lines, image_name)

    if is_type3:
        return _process_type3_text(lines, image_name)
    else:
        return _process_type1_type2_text(lines, image_name)

def _detect_type3_format(lines: List[str], image_name: str) -> bool:
    """
    Detect if the OCR text is from a Type3 document (single column format).

    Args:
        lines: List of text lines
        image_name: Name of the image file (for logging)

    Returns:
        True if this appears to be Type3 format, False otherwise
    """
    if not lines:
        return False

    # Count lines that look like they have identifiers (contain asterisks and numbers)
    lines_with_identifiers = 0
    lines_with_names_only = 0

    for line in lines:
        # Skip very short lines
        if len(line) < 3:
            continue

        # Check if line contains identifier patterns
        if re.search(r'\*{3,}', line) or re.search(r'\d{5,}', line):
            lines_with_identifiers += 1
        # Check if line looks like a name (contains letters, reasonable length)
        elif (len(line) > 5 and len(line) < 60 and
              any(c.isalpha() for c in line) and
              ' ' in line and
              not any(keyword in line.upper() for keyword in ['LISTE', 'TITULAIRES', 'PAGE', 'JORT'])):
            lines_with_names_only += 1

    # If most lines are names without identifiers, it's likely Type3
    total_content_lines = lines_with_identifiers + lines_with_names_only
    if total_content_lines > 0:
        name_only_ratio = lines_with_names_only / total_content_lines
        logger.info(f"Type3 detection for {image_name}: {lines_with_names_only} name-only lines, "
                   f"{lines_with_identifiers} lines with identifiers, ratio: {name_only_ratio:.2f}")
        return name_only_ratio > 0.7  # If more than 70% are name-only lines

    return False

def _process_type3_text(lines: List[str], image_name: str) -> List[Dict]:
    """
    Process OCR text for Type3 documents (single column format).

    Args:
        lines: List of text lines
        image_name: Name of the image file (for logging)

    Returns:
        List of dictionaries with 'text' key (no identifier for Type3)
    """
    result = []

    for line in lines:
        # Skip very short lines (likely noise)
        if len(line) < 3:
            continue

        # Skip lines that are mostly numbers (likely page numbers or other metadata)
        if len(re.sub(r'[0-9\s]', '', line)) < len(line) * 0.5:
            continue

        # Skip lines that look like headers or titles
        if any(keyword in line.upper() for keyword in ['LISTE', 'TITULAIRES', 'AYANTS', 'DROIT', 'PAGE', 'JORT']):
            continue

        # Clean the name
        cleaned_name = _clean_type3_name(line)

        if cleaned_name and len(cleaned_name) > 2:
            result.append({
                'text': cleaned_name,
                'identifier': ''  # Type3 documents don't have identifiers
            })

    logger.info(f"Extracted {len(result)} Type3 names from {image_name}")
    return result

def _clean_type3_name(name: str) -> str:
    """
    Clean and standardize a name from Type3 document.

    Args:
        name: Raw name string from OCR

    Returns:
        Cleaned name string
    """
    if not name:
        return ""

    # Remove extra whitespace
    cleaned = re.sub(r'\s+', ' ', name.strip())

    # Remove common OCR artifacts
    cleaned = re.sub(r'[|_~`@#$%^&*()+=\[\]{}\\;:"<>?/]', '', cleaned)

    # Remove standalone numbers (likely page numbers or artifacts)
    if re.match(r'^\d+$', cleaned.strip()):
        return ""

    # Proper case formatting for names
    words = cleaned.split()
    formatted_words = []

    for word in words:
        if not word:
            continue

        # Handle special prefixes and particles
        lower_case_words = {
            'de', 'du', 'des', 'le', 'la', 'les', 'ben', 'ibn', 'el', 'al',
            'von', 'van', 'da', 'di', 'del', 'della', 'dello'
        }

        if word.lower() in lower_case_words:
            formatted_words.append(word.lower())
        else:
            # Capitalize first letter, keep rest as is (to preserve intentional casing)
            if len(word) > 1:
                formatted_words.append(word[0].upper() + word[1:].lower())
            else:
                formatted_words.append(word.upper())

    cleaned = ' '.join(formatted_words)

    # Remove any remaining non-printable characters
    cleaned = ''.join(char for char in cleaned if char.isprintable())

    # Final validation - must contain at least one letter
    if not any(c.isalpha() for c in cleaned):
        return ""

    # Must be reasonable length for a name
    if len(cleaned) < 2 or len(cleaned) > 100:
        return ""

    return cleaned.strip()

def _process_type1_type2_text(lines: List[str], image_name: str) -> List[Dict]:
    """
    Process OCR text for Type1/Type2 documents (with identifiers).

    Args:
        lines: List of text lines
        image_name: Name of the image file (for logging)

    Returns:
        List of dictionaries with 'text' and 'identifier' keys
    """
    result = []
    for line in lines:
        # Try to split by tab first
        parts = line.split('\t')

        # If no tab, try to split by multiple spaces
        if len(parts) == 1:
            parts = re.split(r'\s{2,}', line)

        # If still only one part, try to find a pattern like "*****" followed by digits
        if len(parts) == 1:
            # Look for patterns like "*****123" or "***** 123"
            match = re.search(r'(\*{5}\s*\d+)', line)
            if match:
                # Split at the beginning of the match
                idx = line.find(match.group(0))
                if idx > 0:
                    parts = [line[:idx].strip(), match.group(0).strip()]

        # If we have at least 2 parts, assume the first is the name and the second is the identifier
        if len(parts) >= 2:
            name = parts[0].strip()
            identifier = parts[1].strip()

            # Skip empty names or identifiers
            if name and identifier:
                # Clean up the identifier (remove spaces between asterisks and numbers)
                identifier = re.sub(r'(\*+)\s+(\d+)', r'\1\2', identifier)

                # Check if the identifier has the expected format (5 asterisks followed by numbers)
                if not re.match(r'\*{5}\d+', identifier):
                    # If not, try to extract just the digits and format properly
                    digits = ''.join(c for c in identifier if c.isdigit())
                    if digits:
                        identifier = f"*****{digits}"

                # Add to result
                result.append({
                    'text': name,
                    'identifier': identifier
                })

    logger.info(f"Extracted {len(result)} Type1/Type2 entries from {image_name}")
    return result

def main(input_dir: str, headers: str, output_file: str) -> None:
    """
    Main entry point for the JORTPROC-OCRLoc subproject.

    Args:
        input_dir: Directory containing the subtable images
        headers: Comma-separated list of headers for the CSV file
        output_file: Path to the output CSV file
    """
    try:
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Process OCR
        output_path = process_ocr_local(input_dir, headers, output_file)
        logger.info(f"Successfully processed OCR and saved results to {output_path}")
    except Exception as e:
        logger.error(f"Error processing OCR: {e}")
        raise

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Process OCR on images using local OCR code")
    parser.add_argument("input_dir", help="Directory containing the subtable images")
    parser.add_argument("headers", help="Comma-separated list of headers for the CSV file")
    parser.add_argument("output_file", help="Path to the output CSV file")

    args = parser.parse_args()
    main(args.input_dir, args.headers, args.output_file)
