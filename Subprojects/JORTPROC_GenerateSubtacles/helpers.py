"""
Helper functions for the JORTPROC-GenerateSubtacles subproject.

This module contains functions copied from the main project to make the subproject standalone.
"""
import cv2
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple, Union
from pathlib import Path

# Document type definitions
DOCUMENT_TYPE1 = "type1"  # Two columns with hidden table separator
DOCUMENT_TYPE2 = "type2"  # Multiple columns with explicit headers
DOCUMENT_TYPE3 = "type3"  # Single column format
DOCUMENT_TYPE4 = "type4"  # Three columns of names without headers

logger = logging.getLogger(__name__)

def preprocess_image(image: np.ndarray) -> np.ndarray:
    """Preprocess an image for table detection and OCR.

    Args:
        image: Input image as numpy array

    Returns:
        Preprocessed image as numpy array
    """
    # Convert to grayscale if needed
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    # Apply bilateral filter to reduce noise while preserving edges
    denoised = cv2.bilateralFilter(gray, 9, 75, 75)

    # Apply adaptive histogram equalization for better contrast
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    enhanced = clahe.apply(denoised)

    # Apply adaptive thresholding
    binary = cv2.adaptiveThreshold(
        enhanced,
        255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY,
        11,
        2
    )

    # Apply morphological operations to clean up the image
    kernel = np.ones((2, 2), np.uint8)
    processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

    return processed

def detect_tables(image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    """Detect tables in an image.

    Args:
        image: Preprocessed image as numpy array

    Returns:
        List of table bounding boxes as (x, y, width, height) tuples
    """
    # Convert to binary if not already
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    # Apply thresholding if not already binary
    if np.max(gray) > 1:
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    else:
        binary = gray

    # Get image dimensions
    height, width = binary.shape

    # Detect horizontal lines
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (int(width/10), 1))
    detected_horizontal = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)

    # Detect vertical lines
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, int(height/10)))
    detected_vertical = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel, iterations=2)

    # Combine horizontal and vertical lines
    table_mask = cv2.add(detected_horizontal, detected_vertical)

    # Dilate to connect nearby lines
    kernel = np.ones((3, 3), np.uint8)
    table_mask = cv2.dilate(table_mask, kernel, iterations=2)

    # Find contours of potential tables
    contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter contours by size
    tables = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        # Tables are typically larger than small text blocks
        if w > width * 0.1 and h > height * 0.05:
            tables.append((x, y, w, h))

    return tables

def extract_table_images(image: np.ndarray, tables: List[Tuple[int, int, int, int]]) -> List[np.ndarray]:
    """Extract table images from the original image.

    Args:
        image: Original image as numpy array
        tables: List of table bounding boxes as (x, y, width, height) tuples

    Returns:
        List of cropped table images
    """
    table_images = []

    for x, y, w, h in tables:
        # Add a small margin around the table
        margin = 5
        x_min = max(0, x - margin)
        y_min = max(0, y - margin)
        x_max = min(image.shape[1], x + w + margin)
        y_max = min(image.shape[0], y + h + margin)

        # Crop the table from the original image
        table_img = image[y_min:y_max, x_min:x_max]

        # Skip empty or invalid images
        if table_img.size > 0 and table_img.shape[0] > 0 and table_img.shape[1] > 0:
            table_images.append(table_img)

    return table_images

def optimize_for_name_ocr(image: np.ndarray) -> np.ndarray:
    """Apply a series of optimizations specifically for name OCR.

    Args:
        image: Input image as numpy array

    Returns:
        Optimized image as numpy array with the same number of channels as the input
    """
    # Check if the image is empty
    if image is None or image.size == 0:
        return np.zeros((100, 100), dtype=np.uint8)  # Return a small empty image

    # Store original image shape and type
    original_shape = image.shape
    is_color = len(original_shape) == 3

    # Convert to grayscale if needed
    if is_color:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    # Apply bilateral filter to reduce noise while preserving edges
    denoised = cv2.bilateralFilter(gray, 9, 75, 75)

    # Apply CLAHE for better contrast
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    enhanced = clahe.apply(denoised)

    # Sharpen the image to enhance text edges
    kernel = np.array([[-1, -1, -1],
                       [-1,  9, -1],
                       [-1, -1, -1]])
    sharpened = cv2.filter2D(enhanced, -1, kernel)

    # Apply Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(sharpened, (3, 3), 0)

    # Apply Otsu's thresholding for better binarization
    _, binary_otsu = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # Apply adaptive thresholding with more conservative parameters
    binary_adaptive = cv2.adaptiveThreshold(
        blurred,
        255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY,
        11,
        2
    )

    # Combine the two binary images (logical OR)
    binary = cv2.bitwise_or(binary_otsu, binary_adaptive)

    # Apply morphological operations to clean up the image
    kernel = np.ones((1, 1), np.uint8)
    cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

    # Dilate slightly to make text more prominent
    kernel = np.ones((1, 1), np.uint8)
    dilated = cv2.dilate(cleaned, kernel, iterations=1)

    # Convert back to color if the input was color
    if is_color:
        result = cv2.cvtColor(dilated, cv2.COLOR_GRAY2BGR)
    else:
        result = dilated

    return result
