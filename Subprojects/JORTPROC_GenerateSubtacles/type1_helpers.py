"""
Helper functions for Type 1 document processing in the JORTPROC-GenerateSubtacles subproject.

This module contains functions copied from the main project to make the subproject standalone.
"""
import cv2
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Optional, Tuple, Union
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def detect_big_table_boundaries(image: np.ndarray) -> Tuple[int, int, int, int]:
    """Detect the boundaries of the big table in the image.

    Args:
        image: The input image

    Returns:
        Tuple of (x_min, y_min, x_max, y_max) coordinates
    """
    try:
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Apply thresholding to get binary image
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Calculate horizontal and vertical projections
        h_projection = np.sum(binary, axis=1)
        v_projection = np.sum(binary, axis=0)

        # Normalize projections
        if np.max(h_projection) > 0:
            h_projection = h_projection / np.max(h_projection)
        if np.max(v_projection) > 0:
            v_projection = v_projection / np.max(v_projection)

        # Find the boundaries of the table
        height, width = binary.shape

        # Force content width to be 70% of page width
        # This is a heuristic that works well for Type 1 documents
        logger.info("Forcing content width to 70% of page width")
        content_width = int(width * 0.7)
        x_min = int((width - content_width) / 2)
        x_max = x_min + content_width

        # Find the top boundary (y_min)
        # Skip the top 10% of the image (header)
        start_y = int(height * 0.1)
        y_min = start_y
        for i in range(start_y, height):
            if h_projection[i] > 0.1:  # Threshold for content
                y_min = i
                break

        # Find the bottom boundary (y_max)
        # Skip the bottom 10% of the image (footer)
        end_y = int(height * 0.9)
        y_max = end_y
        for i in range(height - 1, end_y, -1):
            if h_projection[i] > 0.1:  # Threshold for content
                y_max = i
                break

        # Ensure the boundaries are valid
        x_min = max(0, x_min)
        y_min = max(0, y_min)
        x_max = min(width, x_max)
        y_max = min(height, y_max)

        logger.info(f"Detected table boundaries: x_min={x_min}, y_min={y_min}, x_max={x_max}, y_max={y_max}")

        return x_min, y_min, x_max, y_max

    except Exception as e:
        logger.error(f"Error detecting table boundaries: {e}")
        # Return default values (full image)
        height, width = image.shape[:2]
        return 0, 0, width, height

def preprocess_image_for_type1(image: np.ndarray) -> Dict[str, np.ndarray]:
    """Preprocess the image for Type 1 document OCR.

    Args:
        image: The input image

    Returns:
        Dictionary containing different versions of the preprocessed image
    """
    try:
        # Make a deep copy of the original image to avoid modifying it
        original = image.copy()

        # Convert to grayscale if needed
        if len(original.shape) == 3:
            # Use high-quality grayscale conversion with proper weighting
            gray = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
        else:
            # Make a deep copy to avoid reference issues
            gray = original.copy()

        # Create only the essential versions of the image for OCR
        versions = {
            'original': original
        }

        # Create inverted version (often better for OCR)
        inverted = cv2.bitwise_not(gray)
        versions['inverted'] = inverted

        # Return only the essential versions for later processing
        return versions

    except Exception as e:
        logger.error(f"Error preprocessing image: {e}")
        # If an error occurs, return the original image as a fallback
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return {
                'original': image.copy(),  # Use copy to ensure no reference issues
                'inverted': cv2.bitwise_not(gray)
            }
        else:
            return {
                'original': image.copy(),  # Use copy to ensure no reference issues
                'inverted': cv2.bitwise_not(image.copy())
            }

def detect_white_separator_spaces(image: np.ndarray) -> List[int]:
    """Detect white separator spaces between columns in the image.

    This function analyzes the image to find vertical white spaces that separate columns.

    Args:
        image: The input image

    Returns:
        List of x-coordinates representing the middle of white separator spaces
    """
    try:
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Get image dimensions
        height, width = gray.shape

        # Apply thresholding to get binary image (text is black, background is white)
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Calculate vertical projection (sum of black pixels in each column)
        v_projection = np.sum(binary, axis=0) / 255

        # Normalize projection
        if np.max(v_projection) > 0:
            v_projection = v_projection / np.max(v_projection)

        # Apply smoothing to reduce noise
        v_projection_smooth = np.convolve(v_projection, np.ones(10)/10, mode='same')

        # Calculate the ideal positions for 2 white spaces (at 1/3 and 2/3 of the image width)
        ideal_pos1 = width / 3
        ideal_pos2 = 2 * width / 3

        # Define search windows around the ideal positions (20% of width)
        window_size = width * 0.2
        window1_start = max(0, int(ideal_pos1 - window_size/2))
        window1_end = min(width, int(ideal_pos1 + window_size/2))
        window2_start = max(0, int(ideal_pos2 - window_size/2))
        window2_end = min(width, int(ideal_pos2 + window_size/2))

        # Find the minimum density point in each window
        min_val1 = 1.0
        min_pos1 = window1_start
        for i in range(window1_start, window1_end):
            if v_projection_smooth[i] < min_val1:
                min_val1 = v_projection_smooth[i]
                min_pos1 = i

        min_val2 = 1.0
        min_pos2 = window2_start
        for i in range(window2_start, window2_end):
            if v_projection_smooth[i] < min_val2:
                min_val2 = v_projection_smooth[i]
                min_pos2 = i

        # Check if the minimum values are below a threshold (indicating a white space)
        white_spaces = []
        threshold = 0.3  # Adjust this threshold as needed

        if min_val1 < threshold:
            white_spaces.append(min_pos1)

        if min_val2 < threshold:
            white_spaces.append(min_pos2)

        # If we couldn't find white spaces using the threshold, just use the minimum points
        if not white_spaces:
            white_spaces = [min_pos1, min_pos2]

        # Sort white spaces by position
        white_spaces.sort()

        # Log the detected white spaces
        logger.info(f"Detected {len(white_spaces)} white separator spaces at positions: {white_spaces}")

        return white_spaces

    except Exception as e:
        logger.error(f"Error detecting white separator spaces: {e}")
        return []

def crop_image_into_sub_tables(image: np.ndarray, sub_table_boundaries: List[Tuple[int, int]], page_num: int, pre_ocr_tables_dir: Optional[Path] = None) -> List[Dict]:
    """Crop the image into sub-tables based on the detected boundaries.

    Args:
        image: The original image
        sub_table_boundaries: List of (left_x, right_x) tuples defining sub-table boundaries
        page_num: The page number for logging
        pre_ocr_tables_dir: Directory to save pre-OCR table images (optional)

    Returns:
        List of dictionaries containing different versions of the cropped sub-table images
    """
    try:
        # Get image dimensions
        height, width = image.shape[:2]

        # Crop the image into sub-tables
        sub_table_images = []

        for i, (left_x, right_x) in enumerate(sub_table_boundaries, 1):
            # Ensure we have valid coordinates
            left_x = max(0, int(left_x))
            right_x = min(width, int(right_x))

            # Crop the image with high quality
            # Use a deep copy to ensure no reference issues and maintain full quality
            sub_table_img = np.array(image[:, left_x:right_x], copy=True)

            # Create only the essential versions for OCR
            # 1. Convert to grayscale if it's a color image
            if len(sub_table_img.shape) == 3:
                gray = cv2.cvtColor(sub_table_img, cv2.COLOR_BGR2GRAY)
            else:
                gray = sub_table_img.copy()

            # 2. Create an inverted version (often better for OCR)
            inverted = cv2.bitwise_not(gray)

            # Save only the essential versions of the cropped image if a directory is provided
            if pre_ocr_tables_dir:
                pre_ocr_tables_dir.mkdir(parents=True, exist_ok=True)

                # Save the original cropped image with high quality
                original_path = pre_ocr_tables_dir / f"page{page_num}_type1_sub_table_{i}_cropped.png"
                cv2.imwrite(str(original_path), sub_table_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])  # Use highest quality
                logger.info(f"Saved original sub-table {i} image to {original_path}")

                # Save only the inverted version which often works best for OCR
                inverted_path = pre_ocr_tables_dir / f"page{page_num}_type1_sub_table_{i}_inverted.png"
                cv2.imwrite(str(inverted_path), inverted, [cv2.IMWRITE_PNG_COMPRESSION, 0])

            # Add only the essential versions to the result list
            sub_table_images.append({
                'original': sub_table_img,
                'inverted': inverted,
                'index': i
            })

        return sub_table_images

    except Exception as e:
        logger.error(f"Error cropping image into sub-tables: {e}")
        # Return the original image as a fallback
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        return [{
            'original': image,
            'inverted': cv2.bitwise_not(gray),
            'index': 0
        }]
