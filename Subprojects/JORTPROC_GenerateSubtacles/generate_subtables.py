"""
Generate subtables from PDF pages.

This module is responsible for transforming a PDF page into isolated subtables ready for OCR.
"""
import logging
import os
from pathlib import Path
from typing import List, Dict, Optional, Union, Tuple
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pdf2image import convert_from_path

# Import local helpers - use absolute imports for standalone execution
try:
    # When imported as a module
    from .helpers import (
        preprocess_image,
        detect_tables,
        extract_table_images,
        optimize_for_name_ocr,
        DOCUMENT_TYPE1, DOCUMENT_TYPE2, DOCUMENT_TYPE3, DOCUMENT_TYPE4
    )
    from .type1_helpers import (
        detect_big_table_boundaries,
        preprocess_image_for_type1,
        detect_white_separator_spaces,
        crop_image_into_sub_tables
    )
except ImportError:
    # When run as a standalone script
    from helpers import (
        preprocess_image,
        detect_tables,
        extract_table_images,
        optimize_for_name_ocr,
        DOCUMENT_TYPE1, DOCUMENT_TYPE2, DOCUMENT_TYPE3, DOCUMENT_TYPE4
    )
    from type1_helpers import (
        detect_big_table_boundaries,
        preprocess_image_for_type1,
        detect_white_separator_spaces,
        crop_image_into_sub_tables
    )

logger = logging.getLogger(__name__)

def generate_subtables(pdf_path: Union[str, Path], page_num: int, document_type: str, output_dir: Union[str, Path]) -> Path:
    """
    Generate subtables from a PDF page.

    Args:
        pdf_path: Path to the PDF file
        page_num: Page number to process (1-based)
        document_type: Type of document (e.g., 'type1', 'type2')
        output_dir: Directory to save the output images

    Returns:
        Path to the directory containing the generated subtable images
    """
    # Convert paths to Path objects
    pdf_path = Path(pdf_path)
    output_dir = Path(output_dir)

    # Create output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)

    # Create a directory for this page
    page_dir = output_dir / f"page{page_num}"
    page_dir.mkdir(exist_ok=True)

    logger.info(f"Processing page {page_num} from {pdf_path}")
    logger.info(f"Document type: {document_type}")
    logger.info(f"Output directory: {page_dir}")

    # Convert PDF page to image
    dpi = 300  # High quality for better OCR
    images = convert_from_path(
        pdf_path,
        dpi=dpi,
        first_page=page_num,
        last_page=page_num,
        thread_count=4,
        use_cropbox=True,
        strict=False
    )

    if not images:
        logger.error(f"Failed to convert page {page_num} from {pdf_path}")
        return page_dir

    # Get the page image
    img_np = np.array(images[0])
    img_cv = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)

    # Process based on document type
    if document_type.lower() == DOCUMENT_TYPE1.lower():
        _process_type1_page(img_cv, page_num, page_dir)
    elif document_type.lower() == DOCUMENT_TYPE2.lower():
        _process_type2_page(img_cv, page_num, page_dir)
    elif document_type.lower() == DOCUMENT_TYPE3.lower():
        _process_type3_page(img_cv, page_num, page_dir)
    else:
        _process_default_page(img_cv, page_num, page_dir)

    logger.info(f"Generated subtables for page {page_num} in {page_dir}")
    return page_dir

def _process_type2_page(img_cv: np.ndarray, page_num: int, output_dir: Path) -> None:
    """
    Process a Type2 document page (multiple columns with explicit headers).

    Unlike Type1 which has 3 subtables, Type2 has a single table with multiple columns.
    For Type2, we process each column separately, similar to how we handle subtables in Type1.

    Args:
        img_cv: OpenCV image of the page
        page_num: Page number
        output_dir: Directory to save the output images
    """
    logger.info(f"Processing Type2 page {page_num}")

    # Convert to grayscale if needed
    if len(img_cv.shape) == 3:
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    else:
        gray = img_cv.copy()

    # Apply thresholding to get binary image
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # Calculate horizontal and vertical projections
    h_projection = np.sum(binary, axis=1)
    v_projection = np.sum(binary, axis=0)

    # Normalize projections
    if np.max(h_projection) > 0:
        h_projection = h_projection / np.max(h_projection)
    if np.max(v_projection) > 0:
        v_projection = v_projection / np.max(v_projection)

    # Find the boundaries of the table
    height, width = binary.shape

    # Find the top boundary (y_min)
    # Skip the top 5% of the image (header)
    start_y = int(height * 0.05)
    y_min = start_y
    for i in range(start_y, height):
        if h_projection[i] > 0.1:  # Threshold for content
            y_min = i
            break

    # Find the bottom boundary (y_max)
    # Start from the bottom and move up until we find content
    y_max = height - 1
    for i in range(height - 1, 0, -1):
        if h_projection[i] > 0.1:  # Threshold for content
            # Add a small margin (10 pixels) to ensure we capture all content
            # This is reduced from 20 pixels to minimize empty space
            y_max = i + 10
            break

    # Add a check to remove excessive empty space at the bottom
    # If we have more than 5% of the image as empty space at the bottom, trim it
    empty_space_threshold = int(height * 0.05)
    if y_max > height - empty_space_threshold:
        y_max = height - 1  # Keep full height if empty space is reasonable

    # Ensure y_max doesn't exceed image height
    y_max = min(height - 1, y_max)

    # Find the left boundary (x_min)
    # Use a lower threshold for type2 documents to capture all columns
    x_min = 0
    for i in range(width):
        if v_projection[i] > 0.05:  # Lower threshold for type2
            x_min = max(0, i - 10)  # Add a small margin
            break

    # Find the right boundary (x_max)
    x_max = width - 1
    for i in range(width - 1, 0, -1):
        if v_projection[i] > 0.05:  # Lower threshold for type2
            x_max = min(width, i + 10)  # Add a small margin
            break

    # Ensure the boundaries are valid
    x_min = max(0, x_min)
    y_min = max(0, y_min)
    x_max = min(width, x_max)
    y_max = min(height, y_max)

    logger.info(f"Detected table boundaries: x_min={x_min}, y_min={y_min}, x_max={x_max}, y_max={y_max}")

    # Crop the big table from the page
    big_table_img = img_cv[y_min:y_max, x_min:x_max]

    # Save the cropped big table
    big_table_path = output_dir / f"page{page_num}_big_table_cropped.png"
    cv2.imwrite(str(big_table_path), big_table_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])

    # Convert to grayscale if needed
    if len(big_table_img.shape) == 3:
        gray_table = cv2.cvtColor(big_table_img, cv2.COLOR_BGR2GRAY)
    else:
        gray_table = big_table_img.copy()

    # Create an inverted version of the image (white text on black background)
    inverted = cv2.bitwise_not(gray_table)

    # Save the inverted image for the whole table (for backward compatibility)
    inverted_lined_path = output_dir / f"page{page_num}_table1_inverted_lined.png"
    cv2.imwrite(str(inverted_lined_path), inverted, [cv2.IMWRITE_PNG_COMPRESSION, 0])

    logger.info(f"Saved inverted lined image to {inverted_lined_path}")

    # Now detect column boundaries to process each column separately
    # Type2 documents typically have 3 columns: NOMS & PRENOMS, CIN, ADRESSE

    # Get the width of the cropped table
    table_width = x_max - x_min

    # For type2 documents, we'll use a more direct approach to detect the vertical lines
    # This is more robust than using projections or fixed boundaries

    # Load the inverted lined image that we just saved
    inverted_lined_img = cv2.imread(str(inverted_lined_path))
    if inverted_lined_img is None:
        logger.error(f"Failed to load inverted lined image: {inverted_lined_path}")
        # Fall back to using the original image
        inverted_lined_img = big_table_img.copy()

    # Convert to grayscale if needed
    if len(inverted_lined_img.shape) == 3:
        gray_table = cv2.cvtColor(inverted_lined_img, cv2.COLOR_BGR2GRAY)
    else:
        gray_table = inverted_lined_img.copy()

    # Apply thresholding to get binary image
    _, binary_table = cv2.threshold(gray_table, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # Get the total width of the table
    total_width = binary_table.shape[1]

    # For type2 documents, we know there are exactly 3 columns
    # Let's use the vertical projection to find the column boundaries

    # Calculate vertical projection (sum of white pixels in each column)
    v_projection = np.sum(binary_table, axis=0)

    # Normalize projection
    if np.max(v_projection) > 0:
        v_projection = v_projection / np.max(v_projection)

    # Apply Gaussian blur to smooth the projection
    v_projection_smooth = np.convolve(v_projection, np.ones(15)/15, mode='same')

    # Use the exact values for the column boundaries based on the observed values in the vertical projection
    # These values correspond to the valleys in the density graph
    column_boundaries = [565, 1029]  # Fixed values based on observation

    # Find all local minima in the projection (for visualization only)
    local_minima = []
    for i in range(1, len(v_projection_smooth) - 1):
        if v_projection_smooth[i] < v_projection_smooth[i-1] and v_projection_smooth[i] < v_projection_smooth[i+1]:
            # Include all local minima for visualization
            local_minima.append((i, v_projection_smooth[i]))

    # Sort local minima by position (x-coordinate)
    local_minima.sort(key=lambda x: x[0])

    # Save the vertical projection for visualization
    projection_path = output_dir / f"page{page_num}_vertical_projection.png"
    plt.figure(figsize=(12, 6))
    plt.plot(v_projection_smooth)
    plt.title("Vertical Projection with Local Minima")
    plt.xlabel("X-coordinate")
    plt.ylabel("Normalized Density")
    plt.grid(True)

    # Add vertical lines at the exact column boundaries
    plt.axvline(x=column_boundaries[0], color='r', linestyle='--', label=f'Column Boundary 1 ({column_boundaries[0]})')
    plt.axvline(x=column_boundaries[1], color='g', linestyle='--', label=f'Column Boundary 2 ({column_boundaries[1]})')

    # Mark all local minima on the plot
    for x, y in local_minima:
        plt.plot(x, y, 'bo', markersize=4)

    plt.legend()
    plt.tight_layout()
    plt.savefig(str(projection_path))
    plt.close()
    logger.info(f"Saved vertical projection to {projection_path}")

    # Log all found minima for debugging
    logger.info(f"Found {len(local_minima)} local minima in vertical projection")
    if len(local_minima) > 0:
        logger.info(f"Minima positions: {[x[0] for x in local_minima]}")
        logger.info(f"Minima values: {[x[1] for x in local_minima]}")

    # Log that we're using the exact values
    logger.info(f"Using exact values at {column_boundaries} as column boundaries")

    # Ensure we have exactly 2 boundaries
    if len(column_boundaries) < 2:
        column_boundaries = [int(total_width * 1/3), int(total_width * 2/3)]
    elif len(column_boundaries) > 2:
        column_boundaries = column_boundaries[:2]

    # Ensure the boundaries are in the correct order
    column_boundaries.sort()

    # Ensure they are integers
    column_boundaries = [int(boundary) for boundary in column_boundaries]

    # Calculate the current widths of each column
    col1_width = column_boundaries[0]  # Width of first column
    col2_width = column_boundaries[1] - column_boundaries[0]  # Width of second column
    col3_width = total_width - column_boundaries[1]  # Width of third column

    # Log the column widths
    logger.info(f"Column widths: col1={col1_width}, col2={col2_width}, col3={col3_width}")

    # Log the column boundaries
    logger.info(f"Final column boundaries at {column_boundaries} for type2 document")

    # Save the column boundaries to a file for visualization
    boundaries_path = output_dir / "column_boundaries.txt"
    with open(boundaries_path, 'w') as f:
        f.write(','.join(map(str, column_boundaries)))
    logger.info(f"Saved column boundaries to {boundaries_path}")

    # For column splitting, we need to use the same coordinates as for detection
    # The column boundaries are relative to the inverted lined image, not the original image
    # So we don't need to add x_min to them
    column_edges = [0] + column_boundaries + [binary_table.shape[1]]

    logger.info(f"Detected {len(column_boundaries)} column boundaries at {column_boundaries}")

    # Process each column separately
    for i in range(len(column_edges) - 1):
        # Crop the column
        col_x_min = column_edges[i]
        col_x_max = column_edges[i+1]

        # Add a small margin to ensure we capture all content
        col_x_min = max(0, col_x_min - 5)
        col_x_max = min(binary_table.shape[1], col_x_max + 5)

        # Crop the column from the inverted lined image
        # This ensures consistency between detection and extraction
        column_img = inverted_lined_img[:, col_x_min:col_x_max]

        # Convert to grayscale if needed
        if len(column_img.shape) == 3:
            gray_column = cv2.cvtColor(column_img, cv2.COLOR_BGR2GRAY)
        else:
            gray_column = column_img.copy()

        # The image is already inverted (white text on black background)
        inverted_column = gray_column.copy()

        # Add "#NA#" text to empty cells to ensure consistent row detection across columns
        # This is crucial for type2 documents to maintain alignment between columns
        # and to ensure that small content like "-" characters are properly detected

        # Apply binary thresholding
        _, binary = cv2.threshold(gray_column, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Detect horizontal lines
        height, width = inverted_column.shape[:2]
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (width // 10, 1))
        horizontal_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)

        # Find contours of horizontal lines
        contours, _ = cv2.findContours(horizontal_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Extract y-coordinates of horizontal lines
        row_boundaries = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            # Only consider lines that span at least half the width
            if w > width // 2:
                # Use the middle of the line as the boundary
                row_y = y + h // 2
                row_boundaries.append(row_y)

        # Sort row boundaries by y-coordinate
        row_boundaries.sort()

        # Add top and bottom boundaries
        row_boundaries = [0] + row_boundaries + [height]

        # Create a copy of the inverted column for adding "NA" text
        inverted_column_with_na = inverted_column.copy()

        # For each cell (area between two horizontal lines), check if it's empty
        for j in range(len(row_boundaries) - 1):
            # Define the cell area
            cell_top = row_boundaries[j] + 2  # Add a small margin
            cell_bottom = row_boundaries[j + 1] - 2  # Subtract a small margin

            # Skip if the cell is too small
            if cell_bottom - cell_top < 10:
                continue

            # Extract the cell
            cell = gray_column[cell_top:cell_bottom, :]

            # Check if the cell is empty or has minimal content (few white pixels)
            # This is important for detecting cells with small content like "-" characters
            white_pixel_count = np.sum(cell > 128)
            cell_area = cell.shape[0] * cell.shape[1]

            # First check if the cell has very few white pixels (less than 2.2% of the area)
            # This catches completely empty cells or cells with very minimal content
            if white_pixel_count < cell_area * 0.022:  # Slightly more aggressive than 2%
                is_empty = True
                logger.info(f"Detected empty cell by white pixel count: {white_pixel_count}/{cell_area} = {white_pixel_count/cell_area:.4f}")
            else:
                # Check if the cell contains only minimal content like "-" characters
                # First, apply thresholding to get a binary image for better contour detection
                _, binary_cell = cv2.threshold(cell, 128, 255, cv2.THRESH_BINARY)

                # Find contours in the cell to identify distinct shapes
                contours, _ = cv2.findContours(binary_cell, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                # Calculate the total area of all contours to determine how much content is present
                total_contour_area = sum(cv2.contourArea(contour) for contour in contours)

                # Check if there are no contours at all (completely empty cell)
                if len(contours) == 0:
                    is_empty = True
                    logger.info(f"Detected empty cell by zero contours")
                    continue

                # Check if there are very few contours with minimal area
                if len(contours) <= 2 and total_contour_area < cell_area * 0.02:
                    is_empty = True
                    logger.info(f"Detected empty cell by few contours with minimal area: contours={len(contours)}, area={total_contour_area}/{cell_area} = {total_contour_area/cell_area:.4f}")
                    continue

                # Check if the total contour area is very small relative to the cell area
                # This is a moderate check to catch cells with minimal content
                if total_contour_area < cell_area * 0.045:  # Slightly more aggressive than 4%
                    # For very small content, do additional checks to confirm it's likely a dash or empty

                    # Check if this might be a dash pattern like "----" or "---"
                    # For dash patterns, we typically see:
                    # 1. Multiple small contours (one for each dash)
                    # 2. Contours that are horizontally aligned
                    # 3. Contours that have similar heights
                    # 4. Or a single contour with a very wide and short shape (connected dashes)

                    # First, check for a single wide dash pattern (like "---" as one connected shape)
                    if len(contours) == 1:
                        x, y, w, h = cv2.boundingRect(contours[0])
                        # Check if it's a wide, short shape (typical of a dash line)
                        # Slightly more aggressive width/height ratio
                        if w > cell.shape[1] * 0.18 and h < cell.shape[0] * 0.22 and w > h * 2.5:  # Slightly more aggressive
                            is_empty = True
                            logger.info(f"Detected single wide dash pattern: w={w}, h={h}, cell_width={cell.shape[1]}, cell_height={cell.shape[0]}")
                            continue

                    # Next, check for multiple separate dashes
                    if len(contours) > 0 and len(contours) <= 6:  # Up to 6 dashes (slightly more aggressive)
                        # Get bounding rectangles for all contours
                        bounding_rects = [cv2.boundingRect(contour) for contour in contours]

                        # Check if all contours are small
                        all_small = all(w < cell.shape[1] * 0.22 and h < cell.shape[0] * 0.32 for _, _, w, h in bounding_rects)

                        # Check if contours are horizontally aligned (similar y-coordinates)
                        if all_small and len(bounding_rects) > 1:
                            y_coords = [y + h/2 for _, y, _, h in bounding_rects]
                            y_mean = sum(y_coords) / len(y_coords)
                            # Slightly more aggressive alignment check
                            y_aligned = all(abs(y - y_mean) < cell.shape[0] * 0.22 for y in y_coords)

                            # Check if contours have similar heights
                            heights = [h for _, _, _, h in bounding_rects]
                            h_mean = sum(heights) / len(heights)
                            # Slightly more aggressive height similarity check
                            h_similar = all(abs(h - h_mean) < h_mean * 0.55 for h in heights)

                            # If all conditions are met, this is likely a dash pattern
                            if y_aligned and h_similar:
                                is_empty = True
                                logger.info(f"Detected multiple dash pattern with {len(contours)} dashes: heights={heights}, y_coords={y_coords}")
                                continue

                    # Also check for a pattern where the total content is centered and consists mainly of dashes
                    # This catches cases where the OCR might see "---" as a single entity
                    # Calculate the center of mass of all contours
                    contour_centers = []
                    for contour in contours:
                        M = cv2.moments(contour)
                        if M["m00"] != 0:
                            cx = int(M["m10"] / M["m00"])
                            cy = int(M["m01"] / M["m00"])
                            contour_centers.append((cx, cy))

                    # If we have centers, check if they're clustered in the middle
                    if contour_centers:
                        # Calculate average center
                        avg_x = sum(x for x, _ in contour_centers) / len(contour_centers)

                        # Check if center is in the middle portion of the cell (slightly more aggressive)
                        if cell.shape[1] * 0.3 < avg_x < cell.shape[1] * 0.7:  # Slightly more aggressive middle portion
                            is_empty = True
                            logger.info(f"Detected centered dash pattern: avg_x={avg_x}, cell_width={cell.shape[1]}, total_contour_area={total_contour_area}, cell_area={cell_area}")
                            continue

                # Check if this might be a single character like "-"
                # We consider it minimal content if there are few contours (≤3) and they occupy very little area
                is_minimal_content = (len(contours) <= 3 and total_contour_area < cell_area * 0.035)  # Slightly more aggressive values

                # Log the contour information for debugging
                if is_minimal_content:
                    logger.info(f"Detected minimal content: contours={len(contours)}, total_contour_area={total_contour_area}, cell_area={cell_area}, ratio={total_contour_area/cell_area:.4f}")

                # Consider the cell empty if it has minimal content
                # This ensures that cells with just "-" or similar small characters are treated as empty
                is_empty = is_minimal_content

            # If the cell is empty or has minimal content, add "#NA#" text
            # This ensures that the OCR engine will detect something in every cell
            # For column 2 (CIN) in type2 documents, use a more aggressive approach
            if is_empty or (i == 1 and total_contour_area < cell_area * 0.05):  # More aggressive for CIN column
                # Add "#NA#" text to the cell with distinctive markers (#) to make it easy to filter out later
                font = cv2.FONT_HERSHEY_SIMPLEX
                text = "#NA#"  # Using #NA# instead of just NA makes it more distinctive and easier to filter

                # Use a smaller font size for better visibility without being too large
                font_scale = 0.7  # Reduced from 0.8 to make it smaller
                thickness = 2
                text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]

                # Calculate position to center the text in the cell for better visibility
                text_x = (width - text_size[0]) // 2
                text_y = (cell_top + cell_bottom) // 2 + text_size[1] // 2

                # Draw the text in white on the black background
                cv2.putText(inverted_column_with_na, text, (text_x, text_y), font, font_scale, (255, 255, 255), thickness)

                # No box around the text - removed for cleaner appearance

        # Save the inverted image with "#NA#" text for empty cells
        # Using lossless PNG compression (level 0) to maintain image quality for OCR
        column_path = output_dir / f"page{page_num}_table1_column{i+1}_inverted_lined.png"
        cv2.imwrite(str(column_path), inverted_column_with_na, [cv2.IMWRITE_PNG_COMPRESSION, 0])

        logger.info(f"Saved column {i+1} to {column_path}")

def _process_type3_page(img_cv: np.ndarray, page_num: int, output_dir: Path) -> None:
    """
    Process a Type3 document page (single column format).

    Type3 documents have a single column of names listed one per line.
    We need to detect the content boundaries and create a single processed image.

    Args:
        img_cv: OpenCV image of the page
        page_num: Page number
        output_dir: Directory to save the output images
    """
    logger.info(f"Processing Type3 page {page_num}")

    # Convert to grayscale if needed
    if len(img_cv.shape) == 3:
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    else:
        gray = img_cv.copy()

    # Apply thresholding to get binary image
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # Calculate horizontal and vertical projections
    h_projection = np.sum(binary, axis=1)
    v_projection = np.sum(binary, axis=0)

    # Normalize projections
    if np.max(h_projection) > 0:
        h_projection = h_projection / np.max(h_projection)
    if np.max(v_projection) > 0:
        v_projection = v_projection / np.max(v_projection)

    # Find the boundaries of the content area
    height, width = binary.shape

    # Find the top boundary (y_min)
    # Skip the top 5% of the image (potential header)
    start_y = int(height * 0.05)
    y_min = start_y
    for i in range(start_y, height):
        if h_projection[i] > 0.1:  # Threshold for content
            y_min = max(0, i - 20)  # Add margin
            break

    # Find the bottom boundary (y_max)
    y_max = height - 1
    for i in range(height - 1, 0, -1):
        if h_projection[i] > 0.1:  # Threshold for content
            y_max = min(height - 1, i + 20)  # Add margin
            break

    # Find the left boundary (x_min)
    x_min = 0
    for i in range(width):
        if v_projection[i] > 0.05:  # Lower threshold for single column
            x_min = max(0, i - 10)  # Add small margin
            break

    # Find the right boundary (x_max)
    x_max = width - 1
    for i in range(width - 1, 0, -1):
        if v_projection[i] > 0.05:  # Lower threshold for single column
            x_max = min(width - 1, i + 10)  # Add small margin
            break

    # Ensure the boundaries are valid
    x_min = max(0, x_min)
    y_min = max(0, y_min)
    x_max = min(width, x_max)
    y_max = min(height, y_max)

    logger.info(f"Detected Type3 content boundaries: x_min={x_min}, y_min={y_min}, x_max={x_max}, y_max={y_max}")

    # Crop the content area from the page
    content_img = img_cv[y_min:y_max, x_min:x_max]

    # Save the cropped content area
    content_path = output_dir / f"page{page_num}_content_cropped.png"
    cv2.imwrite(str(content_path), content_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])

    # Convert to grayscale if needed
    if len(content_img.shape) == 3:
        gray_content = cv2.cvtColor(content_img, cv2.COLOR_BGR2GRAY)
    else:
        gray_content = content_img.copy()

    # Create an inverted version of the image (white text on black background)
    inverted = cv2.bitwise_not(gray_content)

    # Add cell markers (#CE#) to help with OCR cell detection
    inverted_with_markers = _add_cell_markers_type3(inverted.copy())

    # For Type3, divide the single column into 4 subtables vertically (side by side)
    # Type3 documents have a single column, so we divide it into 4 vertical strips (left to right)
    # Each subtable holds the full height of the page and is next to each other horizontally
    content_height = inverted_with_markers.shape[0]
    content_width = inverted_with_markers.shape[1]

    # Define 4 subtable boundaries (divide vertically into 4 equal parts)
    subtable_width = content_width // 4
    subtable_boundaries = [
        (0, subtable_width),                            # Subtable 1: leftmost quarter
        (subtable_width, 2 * subtable_width),          # Subtable 2: second quarter
        (2 * subtable_width, 3 * subtable_width),      # Subtable 3: third quarter
        (3 * subtable_width, content_width)            # Subtable 4: rightmost quarter
    ]

    logger.info(f"Dividing Type3 content vertically into 4 subtables with boundaries: {subtable_boundaries}")

    # Create and save each subtable
    for subtable_idx, (x_start, x_end) in enumerate(subtable_boundaries, 1):
        # Crop the subtable from the inverted image with markers (vertical division - full height, partial width)
        subtable_img = inverted_with_markers[:, x_start:x_end]  # All rows, specific columns

        # Add some padding if the subtable is too small
        if subtable_img.shape[1] < 50:  # Minimum width of 50 pixels
            padding = 50 - subtable_img.shape[1]
            subtable_img = cv2.copyMakeBorder(subtable_img, 0, 0, 0, padding, cv2.BORDER_CONSTANT, value=0)

        # Save the subtable as inverted_lined image (following the naming convention)
        subtable_path = output_dir / f"page{page_num}_table{subtable_idx}_inverted_lined.png"
        cv2.imwrite(str(subtable_path), subtable_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])

        logger.info(f"Saved Type3 subtable {subtable_idx} to {subtable_path} (size: {subtable_img.shape})")

    logger.info(f"Created 4 Type3 vertical subtables for page {page_num}")

def _process_type1_page(img_cv: np.ndarray, page_num: int, output_dir: Path) -> None:
    """
    Process a Type1 document page.

    Args:
        img_cv: OpenCV image of the page
        page_num: Page number
        output_dir: Directory to save the output images
    """
    logger.info(f"Processing Type1 page {page_num}")

    # Step 1: Detect the big table boundaries in the page
    x, y, x_max, y_max = detect_big_table_boundaries(img_cv)
    w = x_max - x
    h = y_max - y
    logger.info(f"Detected big table at x={x}, y={y}, width={w}, height={h}")

    # Crop the big table from the page
    big_table_img = img_cv[y:y+h, x:x+w]

    # Save the cropped big table
    big_table_path = output_dir / f"page{page_num}_big_table_cropped.png"
    cv2.imwrite(str(big_table_path), big_table_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])

    # Step 2: Preprocess the cropped big table image to enhance text visibility
    preprocessed_versions = preprocess_image_for_type1(big_table_img)
    preprocessed_img = preprocessed_versions['original']

    # Step 3: Detect white separator spaces to define sub-table boundaries
    white_spaces = detect_white_separator_spaces(preprocessed_img)

    # Define sub-table boundaries
    if len(white_spaces) == 2:
        # Sort white spaces by position
        white_spaces.sort()

        # Define sub-table boundaries using the white spaces
        sub_table_boundaries = [
            (0, white_spaces[0]),                    # Sub-table 1: from left edge to first white space
            (white_spaces[0], white_spaces[1]),      # Sub-table 2: between the two white spaces
            (white_spaces[1], preprocessed_img.shape[1])  # Sub-table 3: from second white space to right edge
        ]
        logger.info(f"Using detected white spaces at positions {white_spaces} to define sub-table boundaries")
    elif len(white_spaces) > 2:
        # If we found more than 2 white spaces, find the 2 that are most evenly distributed
        image_width = preprocessed_img.shape[1]
        ideal_pos1 = image_width / 3
        ideal_pos2 = 2 * image_width / 3

        # Find the white spaces closest to the ideal positions
        best_spaces = []
        for ideal_pos in [ideal_pos1, ideal_pos2]:
            closest_space = min(white_spaces, key=lambda x: abs(x - ideal_pos))
            best_spaces.append(closest_space)

        # Ensure the spaces are unique and sorted
        best_spaces = sorted(set(best_spaces))

        if len(best_spaces) == 2:
            # Define sub-table boundaries using the best white spaces
            sub_table_boundaries = [
                (0, best_spaces[0]),                    # Sub-table 1: from left edge to first white space
                (best_spaces[0], best_spaces[1]),       # Sub-table 2: between the two white spaces
                (best_spaces[1], preprocessed_img.shape[1])  # Sub-table 3: from second white space to right edge
            ]
            logger.info(f"Using best white spaces at positions {best_spaces} to define sub-table boundaries")
        else:
            # Default boundaries
            image_width = preprocessed_img.shape[1]
            sub_table_boundaries = [
                (0, image_width // 3),                    # Sub-table 1: from left edge to 1/3
                (image_width // 3, 2 * image_width // 3), # Sub-table 2: from 1/3 to 2/3
                (2 * image_width // 3, image_width)       # Sub-table 3: from 2/3 to right edge
            ]
            logger.info(f"Using default sub-table boundaries at positions {[b[1] for b in sub_table_boundaries[:-1]]}")
    else:
        # Default boundaries
        image_width = preprocessed_img.shape[1]
        sub_table_boundaries = [
            (0, image_width // 3),                    # Sub-table 1: from left edge to 1/3
            (image_width // 3, 2 * image_width // 3), # Sub-table 2: from 1/3 to 2/3
            (2 * image_width // 3, image_width)       # Sub-table 3: from 2/3 to right edge
        ]
        logger.info(f"Using default sub-table boundaries at positions {[b[1] for b in sub_table_boundaries[:-1]]}")

    # Step 4: Crop the big table image into sub-tables
    logger.info(f"Cropping big table into {len(sub_table_boundaries)} sub-tables")
    sub_table_images = crop_image_into_sub_tables(preprocessed_img, sub_table_boundaries, page_num, output_dir)

    # Save the inverted lined versions of the sub-tables
    for sub_table_idx, sub_table_img_dict in enumerate(sub_table_images, 1):
        # Create inverted lined images for each sub-table
        if 'inverted' in sub_table_img_dict:
            inverted_img = sub_table_img_dict['inverted']

            # Create a copy of the inverted image for drawing lines
            inverted_lined_img = inverted_img.copy()

            # Get image dimensions
            height, width = inverted_img.shape[:2]

            # Detect column boundary (vertical line)
            # Create horizontal projection (sum of white pixels in each column)
            h_projection = np.sum(inverted_img, axis=0)

            # Smooth the projection to reduce noise
            h_projection_smooth = np.convolve(h_projection, np.ones(15)/15, mode='same')

            # Start from 3/4 of the width (assuming identifier column is on the right side)
            start_search = int(width * 3/4)

            # Find the minimum value in the projection between 1/4 and 3/4 of the width
            min_value = float('inf')
            min_index = start_search

            for i in range(start_search, int(width * 1/4), -1):
                if h_projection_smooth[i] < min_value:
                    min_value = h_projection_smooth[i]
                    min_index = i

            # Set the column boundary at the minimum point
            column_boundary = min_index

            # Detect row boundaries (horizontal lines)
            # Detect horizontal lines using morphological operations
            if len(inverted_img.shape) == 3:
                gray = cv2.cvtColor(inverted_img, cv2.COLOR_BGR2GRAY)
            else:
                gray = inverted_img.copy()

            # Apply binary thresholding
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # Detect horizontal lines
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (width // 10, 1))
            horizontal_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)

            # Find contours of horizontal lines
            contours, _ = cv2.findContours(horizontal_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Extract y-coordinates of horizontal lines
            row_boundaries = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                # Only consider lines that span at least half the width
                if w > width // 2:
                    # Use the middle of the line as the boundary
                    row_y = y + h // 2
                    row_boundaries.append(row_y)

            # Sort row boundaries by y-coordinate
            row_boundaries.sort()

            # Draw all lines to create a closed table with 2-pixel thickness
            # Draw vertical column boundary line
            cv2.line(inverted_lined_img, (column_boundary, 0), (column_boundary, height), (255, 255, 255), 2)

            # Draw horizontal row boundary lines
            for row_y in row_boundaries:
                cv2.line(inverted_lined_img, (0, row_y), (width, row_y), (255, 255, 255), 2)

            # Draw borders to close the table with 2-pixel thickness
            # Left border
            cv2.line(inverted_lined_img, (0, 0), (0, height), (255, 255, 255), 2)
            # Right border
            cv2.line(inverted_lined_img, (width-2, 0), (width-2, height), (255, 255, 255), 2)
            # Top border
            cv2.line(inverted_lined_img, (0, 0), (width, 0), (255, 255, 255), 2)
            # Bottom border
            cv2.line(inverted_lined_img, (0, height-2), (width, height-2), (255, 255, 255), 2)

            # Save the inverted lined image
            inverted_lined_path = output_dir / f"page{page_num}_type1_sub_table_{sub_table_idx}_inverted_lined.png"
            cv2.imwrite(str(inverted_lined_path), inverted_lined_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            logger.info(f"Saved inverted lined image to {inverted_lined_path} with column boundary at x={column_boundary} and {len(row_boundaries)} row boundaries")

def _process_default_page(img_cv: np.ndarray, page_num: int, output_dir: Path) -> None:
    """
    Process a default document page.

    Args:
        img_cv: OpenCV image of the page
        page_num: Page number
        output_dir: Directory to save the output images
    """
    logger.info(f"Processing default page {page_num}")

    # Preprocess the image
    preprocessed = preprocess_image(img_cv)

    # Detect tables in the preprocessed image
    tables = detect_tables(preprocessed)
    logger.info(f"Detected {len(tables)} tables on page {page_num}")

    # Extract and save each table
    for i, (x, y, w, h) in enumerate(tables, 1):
        # Extract the table image
        table_img = img_cv[y:y+h, x:x+w]

        # Optimize the image for OCR
        optimized = optimize_for_name_ocr(table_img)

        # Create a copy of the optimized image for drawing lines
        lined_img = optimized.copy()

        # Get image dimensions
        height, width = lined_img.shape[:2]

        # Detect column boundary (vertical line)
        # Create horizontal projection (sum of white pixels in each column)
        h_projection = np.sum(optimized, axis=0)

        # Smooth the projection to reduce noise
        h_projection_smooth = np.convolve(h_projection, np.ones(15)/15, mode='same')

        # Start from 3/4 of the width (assuming identifier column is on the right side)
        start_search = int(width * 3/4)

        # Find the minimum value in the projection between 1/4 and 3/4 of the width
        min_value = float('inf')
        min_index = start_search

        for j in range(start_search, int(width * 1/4), -1):
            if h_projection_smooth[j] < min_value:
                min_value = h_projection_smooth[j]
                min_index = j

        # Set the column boundary at the minimum point
        column_boundary = min_index

        # Detect row boundaries (horizontal lines)
        # Detect horizontal lines using morphological operations
        if len(optimized.shape) == 3:
            gray = cv2.cvtColor(optimized, cv2.COLOR_BGR2GRAY)
        else:
            gray = optimized.copy()

        # Apply binary thresholding
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Detect horizontal lines
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (width // 10, 1))
        horizontal_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)

        # Find contours of horizontal lines
        contours, _ = cv2.findContours(horizontal_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Extract y-coordinates of horizontal lines
        row_boundaries = []
        for contour in contours:
            x_c, y_c, w_c, h_c = cv2.boundingRect(contour)
            # Only consider lines that span at least half the width
            if w_c > width // 2:
                # Use the middle of the line as the boundary
                row_y = y_c + h_c // 2
                row_boundaries.append(row_y)

        # Sort row boundaries by y-coordinate
        row_boundaries.sort()

        # Draw all lines to create a closed table with 2-pixel thickness
        # Draw vertical column boundary line
        cv2.line(lined_img, (column_boundary, 0), (column_boundary, height), (255, 255, 255), 2)

        # Draw horizontal row boundary lines
        for row_y in row_boundaries:
            cv2.line(lined_img, (0, row_y), (width, row_y), (255, 255, 255), 2)

        # Draw borders to close the table with 2-pixel thickness
        # Left border
        cv2.line(lined_img, (0, 0), (0, height), (255, 255, 255), 2)
        # Right border
        cv2.line(lined_img, (width-2, 0), (width-2, height), (255, 255, 255), 2)
        # Top border
        cv2.line(lined_img, (0, 0), (width, 0), (255, 255, 255), 2)
        # Bottom border
        cv2.line(lined_img, (0, height-2), (width, height-2), (255, 255, 255), 2)

        # Save the lined image
        table_path = output_dir / f"page{page_num}_table{i}_inverted_lined.png"
        cv2.imwrite(str(table_path), lined_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
        logger.info(f"Saved inverted lined image to {table_path} with column boundary at x={column_boundary} and {len(row_boundaries)} row boundaries")

def _add_cell_markers_type3(inverted_img: np.ndarray) -> np.ndarray:
    """
    Add #CE# (Cell End) markers at the bottom right of each detected cell in Type3 documents.

    This helps OCR identify cell boundaries by providing visual markers that can be detected
    and used to merge text between markers.

    Args:
        inverted_img: Inverted image (white text on black background)

    Returns:
        Image with #CE# markers added at the bottom right of each detected cell
    """
    try:
        logger.info("Adding #CE# cell markers to Type3 document")

        # Create a copy to work with
        marked_img = inverted_img.copy()
        height, width = marked_img.shape[:2]

        # Convert to binary for text detection
        _, binary = cv2.threshold(inverted_img, 127, 255, cv2.THRESH_BINARY)

        # Calculate horizontal projection to find text lines
        h_projection = np.sum(binary, axis=1)

        # Smooth the projection to reduce noise but preserve line separation
        kernel_size = 3
        h_projection_smooth = np.convolve(h_projection, np.ones(kernel_size)/kernel_size, mode='same')

        # Find text line boundaries using a more sensitive approach
        text_lines = []
        in_text = False
        line_start = 0

        # Parameters for line detection
        min_line_height = 8  # Reduced minimum height for better detection
        text_threshold = np.max(h_projection_smooth) * 0.1  # 10% of max intensity
        gap_threshold = 3  # Minimum gap between lines

        for y in range(height):
            has_text = h_projection_smooth[y] > text_threshold

            if has_text and not in_text:
                # Start of a text line
                line_start = y
                in_text = True
            elif not has_text and in_text:
                # End of a text line
                line_end = y
                line_height = line_end - line_start

                if line_height >= min_line_height:
                    text_lines.append((line_start, line_end))

                in_text = False

        # Handle case where text goes to the end of the image
        if in_text:
            line_height = height - line_start
            if line_height >= min_line_height:
                text_lines.append((line_start, height))

        # Merge lines that are too close together (likely same text line)
        merged_lines = []
        for i, (start, end) in enumerate(text_lines):
            if i == 0:
                merged_lines.append((start, end))
            else:
                prev_start, prev_end = merged_lines[-1]
                gap = start - prev_end

                if gap <= gap_threshold:
                    # Merge with previous line
                    merged_lines[-1] = (prev_start, end)
                else:
                    merged_lines.append((start, end))

        text_lines = merged_lines

        logger.info(f"Detected {len(text_lines)} text lines for cell marker placement")

        # Add #CE# marker at the bottom right of each text line
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.4
        font_thickness = 1
        marker_text = "#CE#"

        # Get text size for positioning
        (text_width, text_height), baseline = cv2.getTextSize(marker_text, font, font_scale, font_thickness)

        for i, (line_start, line_end) in enumerate(text_lines):
            # Calculate position for the marker
            # Place it at the bottom right of the text line with some margin
            marker_x = width - text_width - 10  # 10 pixels from right edge
            marker_y = line_end - 5  # 5 pixels above the bottom of the text line

            # Ensure the marker is within image bounds
            marker_x = max(0, min(marker_x, width - text_width))
            marker_y = max(text_height, min(marker_y, height - 5))

            # Add the #CE# marker in white (255) on the inverted image
            cv2.putText(marked_img, marker_text, (marker_x, marker_y),
                       font, font_scale, (255, 255, 255), font_thickness, cv2.LINE_AA)

            logger.debug(f"Added #CE# marker {i+1} at position ({marker_x}, {marker_y}) for text line {line_start}-{line_end}")

        logger.info(f"Successfully added {len(text_lines)} #CE# cell markers to Type3 document")
        return marked_img

    except Exception as e:
        logger.error(f"Error adding cell markers to Type3 document: {e}")
        # Return original image if marker addition fails
        return inverted_img

def main(pdf_path: str, page_num: int, document_type: str, output_dir: str) -> None:
    """
    Main entry point for the JORTPROC-GenerateSubtables subproject.

    Args:
        pdf_path: Path to the PDF file
        page_num: Page number to process (1-based)
        document_type: Type of document (e.g., 'type1', 'type2')
        output_dir: Directory to save the output images
    """
    try:
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Generate subtables
        output_path = generate_subtables(pdf_path, page_num, document_type, output_dir)
        logger.info(f"Successfully generated subtables in {output_path}")
    except Exception as e:
        logger.error(f"Error generating subtables: {e}")
        raise

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Generate subtables from PDF pages")
    parser.add_argument("pdf_path", help="Path to the PDF file")
    parser.add_argument("page_num", type=int, help="Page number to process (1-based)")
    parser.add_argument("document_type", help="Type of document (e.g., 'type1', 'type2')")
    parser.add_argument("output_dir", help="Directory to save the output images")

    args = parser.parse_args()
    main(args.pdf_path, args.page_num, args.document_type, args.output_dir)
