"""
Process OCR on images using OCR.space code.

This module takes all images in a folder and processes OCR on them with OCR.space code.
"""
import logging
import os
import json
import re
import time
from pathlib import Path
from typing import List, Dict, Optional, Union, Tuple
import pandas as pd
import requests
import tempfile
import cv2
import numpy as np

logger = logging.getLogger(__name__)

class OCRSpaceClient:
    """Client for the OCR.space API."""

    def __init__(self, api_key: str = 'K888957'):
        """Initialize the OCR.space API client.

        Args:
            api_key: OCR.space API key
        """
        self.api_key = api_key
        self.base_url = 'https://api.ocr.space/parse/image'

    def _resize_image_for_ocr_space(self, file_path: Path, max_size_kb: int = 1000) -> Tuple[Path, bool]:
        """Optimize an image to fit within the OCR.space file size limit.

        Args:
            file_path: Path to the image file
            max_size_kb: Maximum file size in KB (OCR.space API limit is 1MB)

        Returns:
            Tuple of (path to optimized image, whether optimization was needed)
        """
        file_path = Path(file_path)
        if not file_path.exists():
            logger.error(f"File {file_path} does not exist")
            return file_path, False

        # Check current file size
        file_size_kb = os.path.getsize(file_path) / 1024

        # If file is already smaller than 5MB, return the original path
        if file_size_kb < max_size_kb:
            logger.info(f"File {file_path} is {file_size_kb:.1f} KB (< 5MB), no optimization needed")
            return file_path, False

        logger.info(f"Image {file_path} is {file_size_kb:.1f} KB (>= 5MB), optimizing for OCR.space")

        # Read the image
        img = cv2.imread(str(file_path))
        if img is None:
            logger.error(f"Could not read image {file_path}")
            return file_path, False

        # Create a temporary file for the optimized image
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            temp_path = Path(temp_file.name)

        # First try: optimize the PNG without resizing using lossless compression
        # Try different compression levels to find the optimal one that keeps the file under 5MB
        for compression_level in [0, 1, 2, 3, 4, 5]:
            cv2.imwrite(str(temp_path), img, [cv2.IMWRITE_PNG_COMPRESSION, compression_level])
            optimized_size_kb = os.path.getsize(temp_path) / 1024

            if optimized_size_kb <= max_size_kb:
                logger.info(f"Optimized image with lossless compression level {compression_level}, new size: {optimized_size_kb:.1f} KB")
                return temp_path, True

        # If lossless compression alone doesn't work, we need to resize
        # Calculate the scaling factor based on file size
        # Use a less aggressive scaling factor to preserve image quality
        target_size_kb = max_size_kb * 0.98  # 2% safety margin
        scale_factor = np.sqrt(target_size_kb / file_size_kb)

        # Resize the image with high-quality interpolation
        new_width = int(img.shape[1] * scale_factor)
        new_height = int(img.shape[0] * scale_factor)

        # Use INTER_LANCZOS4 for downscaling (highest quality)
        resized_img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

        # Apply a slight sharpening to enhance text edges after resizing
        # This helps maintain text clarity after resizing
        kernel = np.array([[-1, -1, -1],
                          [-1,  9, -1],
                          [-1, -1, -1]]) / 5.0  # Mild sharpening
        sharpened_img = cv2.filter2D(resized_img, -1, kernel)

        # Save the resized image with lossless compression
        # Try different compression levels to find the optimal one
        for compression_level in [0, 1, 2, 3, 4, 5]:
            cv2.imwrite(str(temp_path), sharpened_img, [cv2.IMWRITE_PNG_COMPRESSION, compression_level])
            new_size_kb = os.path.getsize(temp_path) / 1024

            if new_size_kb <= max_size_kb:
                logger.info(f"Resized image to {new_width}x{new_height} with lossless compression level {compression_level}, new size: {new_size_kb:.1f} KB")
                return temp_path, True

        # If we still can't get under 5MB, use a more aggressive compression
        cv2.imwrite(str(temp_path), sharpened_img, [cv2.IMWRITE_PNG_COMPRESSION, 9])  # Maximum compression
        final_size_kb = os.path.getsize(temp_path) / 1024

        if final_size_kb <= max_size_kb:
            logger.info(f"Resized image to {new_width}x{new_height} with maximum compression, new size: {final_size_kb:.1f} KB")
            return temp_path, True

        # If we still can't get under 5MB, try more aggressive resizing
        scale_factor *= 0.9  # Reduce by another 10%
        new_width = int(img.shape[1] * scale_factor)
        new_height = int(img.shape[0] * scale_factor)

        final_img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

        # Apply sharpening to the final image
        final_img = cv2.filter2D(final_img, -1, kernel)

        # Save with lossless compression
        cv2.imwrite(str(temp_path), final_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
        final_size_kb = os.path.getsize(temp_path) / 1024

        logger.info(f"Final resize to {new_width}x{new_height} with lossless compression, new size: {final_size_kb:.1f} KB")
        return temp_path, True

    def process_file(self, file_path: Union[str, Path]) -> Optional[Dict]:
        """Process a local image file with OCR.space API.

        Args:
            file_path: Path to the image file

        Returns:
            OCR result as a dictionary
        """
        file_path = Path(file_path)

        try:
            # Resize the image if needed to fit within OCR.space file size limits (5MB)
            temp_file_created = False
            resized_path = file_path

            try:
                # Only optimize if larger than or equal to 1MB
                resized_path, temp_file_created = self._resize_image_for_ocr_space(file_path, max_size_kb=1000)
            except Exception as e:
                logger.error(f"Error optimizing image: {e}")
                # Continue with the original file if there's an error
                resized_path = file_path
                temp_file_created = False

            logger.info(f"Sending request to OCR.space API for file: {resized_path}")
            logger.info(f"File size: {os.path.getsize(resized_path) / 1024:.1f} KB")

            # Prepare headers
            headers = {
                'apikey': self.api_key
            }

            try:
                # Open the file and prepare the request
                with open(resized_path, 'rb') as f:
                    # Prepare files and form data
                    files = {
                        'file': (resized_path.name, f, 'image/png')
                    }

                    # Form data with the specific parameters requested
                    data = {
                        'language': 'fre',  # French
                        'isTable': 'true',  # Detect table
                        'OCREngine': '2',   # OCR Engine 2
                        'FileType': '.png', # PNG file type
                        'scale': 'false',   # No auto enlarge
                        'isOverlayRequired': 'true',
                        'IsCreateSearchablePDF': 'false',
                        'isSearchablePdfHideTextLayer': 'true',
                        'detectOrientation': 'false'
                    }

                    # Send the request
                    response = requests.post(
                        self.base_url,
                        headers=headers,
                        files=files,
                        data=data,
                        timeout=30  # Add timeout to avoid hanging
                    )

                logger.info(f"Response status code: {response.status_code}")
            except Exception as e:
                logger.error(f"Exception during request: {e}")
                return None

            if response.status_code != 200:
                logger.error(f"OCR.space API error: {response.status_code} - {response.text}")
                return None

            response_content = response.content.decode()
            logger.info(f"OCR.space API response: {response_content[:200]}...")  # Log first 200 chars

            try:
                result = json.loads(response_content)
                if 'ParsedResults' in result and result['ParsedResults']:
                    logger.info(f"OCR.space API parsed {len(result['ParsedResults'])} results")
                    for i, parsed_result in enumerate(result['ParsedResults']):
                        if 'ParsedText' in parsed_result:
                            text_preview = parsed_result['ParsedText'][:100].replace('\n', ' ')
                            logger.info(f"Result {i+1} text preview: {text_preview}...")
                else:
                    logger.warning("OCR.space API returned no parsed results")
                return result
            except json.JSONDecodeError as e:
                logger.error(f"Error decoding OCR.space API response: {e}")
                return None
        except Exception as e:
            logger.error(f"Error processing file with OCR.space API: {e}")
            return None
        finally:
            # Clean up temporary file if created
            if temp_file_created and resized_path.exists():
                try:
                    os.unlink(resized_path)
                    logger.debug(f"Deleted temporary file {resized_path}")
                except Exception as e:
                    logger.warning(f"Error deleting temporary file {resized_path}: {e}")

    def extract_text(self, ocr_result: Optional[Dict], is_column: bool = False, column_index: int = 0) -> List[Dict]:
        """Extract text from OCR.space API result.

        Args:
            ocr_result: OCR.space API result dictionary
            is_column: Whether this is a column from a type2 document
            column_index: Index of the column (0 for names, 1 for identifiers, 2 for addresses)

        Returns:
            List of dictionaries with 'text' and 'identifier' keys
        """
        if not ocr_result or 'ParsedResults' not in ocr_result:
            return []

        extracted_data = []
        for result in ocr_result['ParsedResults']:
            if result.get('ParsedText'):
                # Split by lines and filter out empty lines
                lines = [line.strip() for line in result['ParsedText'].split('\n') if line.strip()]

                # Skip the first line as it's the header (column names)
                if lines and ('Nom et prénom' in lines[0] or 'Identifiant' in lines[0] or
                             'NOMS & PRENOMS' in lines[0] or 'CIN' in lines[0] or
                             'ADRESSE' in lines[0] or 'ADRESE' in lines[0] or
                             'CIN (trois derniers chiffres)' in lines[0]):
                    lines = lines[1:]

                # If this is a column from a type2 document, process it differently
                if is_column:
                    # For columns, each line is a separate item
                    for line in lines:
                        # Skip empty lines
                        if not line.strip():
                            continue

                        # Skip header-like lines to avoid including headers in the data
                        # This is important for type2 documents where the OCR might detect headers in the column images
                        if (line.strip() in ['NOMS & PRENOMS', 'CIN', 'CIN (trois derniers chiffres)', 'ADRESSE', 'ADRESE']):
                            continue

                        # For column 1 (CIN), check if the line contains a tab character
                        # This might happen if the OCR.space API detects both CIN and ADRESSE in the same line
                        if column_index == 1 and '\t' in line:
                            # Split the line by tab
                            parts = line.split('\t')
                            # The first part is the CIN
                            extracted_data.append({'identifier': parts[0].strip()})
                        else:
                            # Create a dictionary with the appropriate key based on the column index
                            if column_index == 0:  # Names column
                                extracted_data.append({'text': line.strip()})
                            elif column_index == 1:  # Identifiers column
                                extracted_data.append({'identifier': line.strip()})
                            elif column_index == 2:  # Addresses column
                                extracted_data.append({'address': line.strip()})

                    return extracted_data

                # Check document type based on content
                is_type2 = False
                is_type3 = False

                if lines and any(header in lines[0] for header in ["NOMS & PRENOMS", "CIN", "ADRESSE"]):
                    is_type2 = True
                else:
                    # Check if this is a type3 document (single column format)
                    is_type3 = self._detect_type3_format(lines)

                if is_type3:
                    # For type3 documents, handle multiline cells using TextOverlay data
                    # Check if we have TextOverlay data for better multiline detection
                    if 'TextOverlay' in result and 'Lines' in result['TextOverlay']:
                        # Use advanced multiline detection with TextOverlay
                        merged_cells = self._merge_multiline_cells_type3(result['TextOverlay']['Lines'])
                        for cell in merged_cells:
                            cleaned_name = self._clean_type3_name(cell['text'])
                            if cleaned_name:
                                extracted_data.append({
                                    'text': cleaned_name,
                                    'identifier': ''  # Type3 documents don't have identifiers
                                })
                    else:
                        # Fallback to simple line processing if no TextOverlay
                        for line in lines:
                            # Only minimal filtering - let OCR quality improvements handle the rest
                            cleaned_name = self._clean_type3_name(line)
                            if cleaned_name:
                                extracted_data.append({
                                    'text': cleaned_name,
                                    'identifier': ''  # Type3 documents don't have identifiers
                                })

                    # For type3, we've already processed all data, so skip the processed_lines section
                    continue
                elif is_type2:
                    # For type2 documents, process each line directly without combining multi-line names
                    processed_lines = lines
                else:
                    # For type1 documents, process lines to handle multi-line names
                    processed_lines = []
                    i = 0
                    while i < len(lines):
                        parts = lines[i].split('\t')

                        # If we have a line with only a name (no identifier or empty identifier)
                        if len(parts) == 1 or (len(parts) >= 2 and not parts[1].strip()):
                            # This is likely part of a multi-line name
                            current_name = parts[0].strip()
                            combined_name = current_name
                            next_identifier = ""

                            # Look ahead up to 3 lines to find an identifier
                            j = 1
                            while i + j < len(lines) and j <= 3:
                                next_parts = lines[i + j].split('\t')

                                # If this line has an identifier, use it
                                if len(next_parts) >= 2 and next_parts[1].strip():
                                    next_name = next_parts[0].strip()
                                    next_identifier = next_parts[1].strip()

                                    # Combine all names up to this point
                                    combined_name = f"{combined_name} {next_name}"

                                    # Add the combined entry
                                    processed_lines.append(f"{combined_name}\t{next_identifier}")

                                    # Skip all the lines we've processed
                                    i += j + 1
                                    break
                                else:
                                    # This line also has no identifier, add it to the combined name
                                    if len(next_parts) >= 1:
                                        combined_name = f"{combined_name} {next_parts[0].strip()}"
                                    j += 1

                            # If we didn't find an identifier, just add the combined name without an identifier
                            if not next_identifier and j > 1:
                                # We combined some lines but didn't find an identifier
                                # Skip the lines we've processed
                                i += j
                                continue
                            elif not next_identifier:
                                # We didn't find any lines to combine, just add this line as is
                                processed_lines.append(lines[i])
                                i += 1
                                continue
                        else:
                            # This line has both a name and an identifier, add it as is
                            processed_lines.append(lines[i])
                            i += 1

                # Now process the cleaned lines
                for line in processed_lines:
                    # OCR.space uses tabs to separate columns
                    parts = line.split('\t')

                    if len(parts) >= 2:
                        # If we have at least 2 parts, assume the first is the name and the second is the identifier
                        name = parts[0].strip()
                        identifier = parts[1].strip()

                        # For type1 documents, remove spaces between asterisks and numbers in the identifier
                        if not is_type2 and identifier:
                            # Replace spaces between asterisks and numbers
                            identifier = re.sub(r'(\*+)\s+(\d+)', r'\1\2', identifier)

                        # Skip empty names or identifiers
                        if name and identifier:
                            extracted_data.append({
                                'text': name,
                                'identifier': identifier
                            })

        return extracted_data

    def _detect_type3_format(self, lines: List[str]) -> bool:
        """
        Detect if the OCR text is from a Type3 document (single column format).

        Args:
            lines: List of text lines

        Returns:
            True if this appears to be Type3 format, False otherwise
        """
        if not lines:
            return False

        # Count lines that look like they have identifiers (contain asterisks and numbers)
        lines_with_identifiers = 0
        lines_with_names_only = 0

        for line in lines:
            # Skip very short lines
            if len(line) < 3:
                continue

            # Check if line contains identifier patterns
            if re.search(r'\*{3,}', line) or re.search(r'\d{5,}', line):
                lines_with_identifiers += 1
            # Check if line looks like a name (contains letters, reasonable length)
            elif (len(line) > 5 and len(line) < 60 and
                  any(c.isalpha() for c in line) and
                  ' ' in line and
                  not any(keyword in line.upper() for keyword in ['LISTE', 'TITULAIRES', 'PAGE', 'JORT'])):
                lines_with_names_only += 1

        # If most lines are names without identifiers, it's likely Type3
        total_content_lines = lines_with_identifiers + lines_with_names_only
        if total_content_lines > 0:
            name_only_ratio = lines_with_names_only / total_content_lines
            logger.info(f"Type3 detection: {lines_with_names_only} name-only lines, "
                       f"{lines_with_identifiers} lines with identifiers, ratio: {name_only_ratio:.2f}")
            return name_only_ratio > 0.7  # If more than 70% are name-only lines

        return False

    def _clean_type3_name(self, name: str) -> str:
        """
        Minimal cleaning for Type3 document names.
        Focus on OCR quality enhancement rather than text manipulation.

        Args:
            name: Raw name string from OCR

        Returns:
            Minimally cleaned name string
        """
        if not name:
            return ""

        # Only basic whitespace normalization
        cleaned = re.sub(r'\s+', ' ', name.strip())

        # Skip empty lines after whitespace normalization
        if not cleaned:
            return ""

        # Only reject obvious non-name content (pure numbers, very short strings)
        if re.match(r'^\d+$', cleaned.strip()) or len(cleaned) < 2:
            return ""

        # Minimal validation - must contain at least one letter
        if not any(c.isalpha() for c in cleaned):
            return ""

        # Return with minimal processing to preserve OCR output
        return cleaned

    def _merge_multiline_cells_type3(self, lines: List[Dict], x_threshold: int = 10, y_threshold: int = 3) -> List[Dict]:
        """
        Merge multiline cells in Type3 documents using TextOverlay data.

        Based on spatial proximity analysis:
        - Horizontal alignment: Lines with similar Left coordinates belong to same cell
        - Vertical proximity: Small gaps between lines indicate continuation

        Args:
            lines: List of line dictionaries from TextOverlay.Lines
            x_threshold: Maximum horizontal difference to consider lines aligned (pixels) - default 10
            y_threshold: Maximum vertical gap to consider lines as same cell (pixels) - default 3
                        Based on ground truth analysis: multiline gaps are 0-3px, normal line spacing is 4-9px

        Returns:
            List of merged cell dictionaries with 'text' key
        """
        if not lines:
            return []

        try:
            merged_cells = []
            current_cell = None

            # Sort lines by vertical position (MinTop)
            sorted_lines = sorted(lines, key=lambda x: x.get('MinTop', 0))

            for i, line in enumerate(sorted_lines):
                # Skip empty lines
                if not line.get('LineText', '').strip():
                    continue

                # Calculate the leftmost coordinate of the line
                words = line.get('Words', [])
                if not words:
                    continue

                left = min(word.get('Left', 0) for word in words)

                if i == 0 or current_cell is None:
                    # Initialize the first cell
                    current_cell = {
                        'text': line['LineText'].strip(),
                        'left': left,
                        'top': line.get('MinTop', 0),
                        'height': line.get('MaxHeight', 30),
                        'words': words.copy()
                    }
                    continue

                # Get previous line info for comparison
                prev_line = None
                for j in range(i-1, -1, -1):
                    if sorted_lines[j].get('LineText', '').strip():
                        prev_line = sorted_lines[j]
                        break

                if prev_line is None:
                    continue

                # Calculate spatial relationships
                prev_words = prev_line.get('Words', [])
                if not prev_words:
                    continue

                prev_left = min(word.get('Left', 0) for word in prev_words)
                prev_bottom = prev_line.get('MinTop', 0) + prev_line.get('MaxHeight', 30)
                current_top = line.get('MinTop', 0)
                vertical_gap = current_top - prev_bottom

                # Check if lines should be merged
                horizontal_aligned = abs(left - prev_left) <= x_threshold
                vertical_close = vertical_gap <= y_threshold

                # Additional validation: prevent over-merging by checking merged text length
                # If the current cell is already quite long, be more conservative about merging
                current_text_length = len(current_cell['text']) if current_cell else 0
                new_text_length = len(line['LineText'].strip())
                combined_length = current_text_length + new_text_length + 1  # +1 for space

                # Be more conservative if the combined text would be very long (likely multiple names)
                length_check_passed = combined_length <= 80  # Reasonable limit for a single name

                if horizontal_aligned and vertical_close and length_check_passed:
                    # Merge with current cell
                    current_cell['text'] += " " + line['LineText'].strip()
                    current_cell['height'] = max(current_cell['height'], line.get('MaxHeight', 30))
                    current_cell['words'].extend(words)

                    logger.info(f"MERGED: '{line['LineText'].strip()}' with previous cell. "
                               f"Gap: {vertical_gap}px, Left diff: {abs(left - prev_left)}px, Length: {combined_length}")
                else:
                    # Save current cell and start new one
                    if current_cell:
                        merged_cells.append(current_cell)

                    current_cell = {
                        'text': line['LineText'].strip(),
                        'left': left,
                        'top': line.get('MinTop', 0),
                        'height': line.get('MaxHeight', 30),
                        'words': words.copy()
                    }

                    # Determine why merging was rejected
                    rejection_reason = []
                    if not horizontal_aligned:
                        rejection_reason.append(f"H_misaligned({abs(left - prev_left)}px)")
                    if not vertical_close:
                        rejection_reason.append(f"V_far({vertical_gap}px)")
                    if not length_check_passed:
                        rejection_reason.append(f"Too_long({combined_length}chars)")

                    logger.info(f"NEW CELL: '{line['LineText'].strip()}'. "
                               f"Gap: {vertical_gap}px, Left diff: {abs(left - prev_left)}px, "
                               f"Rejected: {', '.join(rejection_reason)}")

            # Add the last cell
            if current_cell:
                merged_cells.append(current_cell)

            logger.info(f"Merged {len(sorted_lines)} lines into {len(merged_cells)} cells for Type3 document")

            # Log some examples of merged cells
            for i, cell in enumerate(merged_cells[:5]):  # Log first 5 cells
                logger.debug(f"Cell {i+1}: '{cell['text']}' (Left: {cell['left']}, Top: {cell['top']})")

            return merged_cells

        except Exception as e:
            logger.error(f"Error merging multiline cells for Type3: {e}")
            # Fallback: return each line as separate cell
            fallback_cells = []
            for line in lines:
                if line.get('LineText', '').strip():
                    fallback_cells.append({'text': line['LineText'].strip()})
            return fallback_cells

def process_ocr_space(input_dir: Union[str, Path], headers: str, output_file: Union[str, Path], api_key: str = 'K888957') -> Path:
    """
    Process OCR on all images in a folder using OCR.space API.

    Args:
        input_dir: Directory containing the subtable images
        headers: Comma-separated list of headers for the CSV file
        output_file: Path to the output CSV file
        api_key: OCR.space API key

    Returns:
        Path to the output CSV file
    """
    # Convert paths to Path objects
    input_dir = Path(input_dir)
    output_file = Path(output_file)

    # Create output directory if it doesn't exist
    output_file.parent.mkdir(parents=True, exist_ok=True)

    # Parse headers
    header_list = [h.strip() for h in headers.split(',')]
    logger.info(f"Using headers: {header_list}")

    # Create OCR.space client
    client = OCRSpaceClient(api_key=api_key)

    # Find all PNG images in the input directory
    # Check if this is a type2 document with individual columns
    column_files = sorted(list(input_dir.glob('*_table1_column*_inverted_lined.png')))

    # If we found column files, this is a type2 document with individual columns
    if column_files:
        logger.info(f"Found {len(column_files)} column images in {input_dir}")
        image_files = column_files
        is_type2_columns = True
    else:
        # Otherwise, use the regular inverted lined images
        image_files = sorted(list(input_dir.glob('*_inverted_lined.png')))
        logger.info(f"Found {len(image_files)} images in {input_dir}")
        is_type2_columns = False

    # Process each image
    all_data = []
    column_data = [[] for _ in range(3)]  # For type2 columns: names, CIN, addresses

    for img_path in image_files:
        logger.info(f"Processing {img_path}")

        # Process the image with OCR.space
        ocr_result = client.process_file(img_path)

        # Extract text from the OCR result
        if ocr_result:
            # If this is a type2 document with individual columns
            if is_type2_columns:
                # Determine which column this is
                column_match = re.search(r'column(\d+)', str(img_path))
                if column_match:
                    column_index = int(column_match.group(1)) - 1
                    # Extract text with column-specific processing
                    data = client.extract_text(ocr_result, is_column=True, column_index=column_index)
                    # Store the data in the appropriate column
                    column_data[column_index].extend(data)
                    logger.info(f"Extracted {len(data)} items from column {column_index+1}")

                    # Print the first few items for debugging
                    if data:
                        logger.info(f"First 3 items from column {column_index+1}: {data[:3]}")
            else:
                # For regular processing, just add the data to the list
                data = client.extract_text(ocr_result)
                all_data.extend(data)

            # Add a delay to avoid hitting API rate limits
            time.sleep(1)
        else:
            logger.warning(f"No OCR result for {img_path}")

    # If this is a type2 document with individual columns, combine the data
    if is_type2_columns:
        # Get the number of rows in each column
        col1_rows = len(column_data[0])
        col2_rows = len(column_data[1])
        col3_rows = len(column_data[2])

        # Print the number of items in each column for debugging
        logger.info(f"Column 1 (names) has {col1_rows} items")
        logger.info(f"Column 2 (CIN) has {col2_rows} items")
        logger.info(f"Column 3 (addresses) has {col3_rows} items")

        # Check if the number of rows is significantly different between columns
        max_diff = max(abs(col1_rows - col2_rows), abs(col1_rows - col3_rows), abs(col2_rows - col3_rows))

        # Log a warning if there's a significant difference, but don't raise an error
        if max_diff > 5:  # Just a threshold for logging
            logger.warning(f"Column row count mismatch: Column 1: {col1_rows}, Column 2: {col2_rows}, "
                          f"Column 3: {col3_rows}. Max difference: {max_diff}")
            logger.warning("This may result in misaligned data in the final table.")

            # If any column has significantly fewer rows than the others,
            # it likely means that empty cells were not properly detected and filled with #NA#
            # Add empty rows to match the number of rows in the column with the most rows
            max_rows = max(col1_rows, col2_rows, col3_rows)

            # Add empty rows to column 1 if needed
            if col1_rows < max_rows:
                logger.info(f"Adding {max_rows - col1_rows} empty rows to column 1 (names)")
                for _ in range(max_rows - col1_rows):
                    column_data[0].append({'text': '#NA#'})

            # Add empty rows to column 2 if needed
            if col2_rows < max_rows:
                logger.info(f"Adding {max_rows - col2_rows} empty rows to column 2 (CIN)")
                for _ in range(max_rows - col2_rows):
                    column_data[1].append({'identifier': '#NA#'})

            # Add empty rows to column 3 if needed
            if col3_rows < max_rows:
                logger.info(f"Adding {max_rows - col3_rows} empty rows to column 3 (addresses)")
                for _ in range(max_rows - col3_rows):
                    column_data[2].append({'address': '#NA#'})

        # Determine the maximum number of rows
        max_rows = max(col1_rows, col2_rows, col3_rows)

        # Combine the columns into rows
        for i in range(max_rows):
            row_data = {}

            # Get the name from column 1 (if available)
            if i < len(column_data[0]) and column_data[0][i].get('text'):
                row_data['text'] = column_data[0][i]['text']
            else:
                row_data['text'] = ""

            # Get the identifier from column 2 (if available)
            if i < len(column_data[1]) and column_data[1][i].get('identifier'):
                row_data['identifier'] = column_data[1][i]['identifier']
            else:
                row_data['identifier'] = ""

            # Get the address from column 3 (if available)
            if i < len(column_data[2]) and column_data[2][i].get('address'):
                row_data['address'] = column_data[2][i]['address']
            else:
                row_data['address'] = ""

            # Add the row to the all_data list
            all_data.append(row_data)

        logger.info(f"Combined {len(all_data)} rows from {len(column_data)} columns")

    # Create a DataFrame from the extracted data
    if all_data:
        # Check if this is a type2 document with 3 columns
        if is_type2_columns and len(header_list) == 3 and 'address' in all_data[0]:
            # For type2 documents with individual columns
            df = pd.DataFrame(all_data, columns=['text', 'identifier', 'address'])
            # Rename columns to match the provided headers
            df.columns = header_list
        # If we have exactly one header, this might be a type3 document (single column)
        elif len(header_list) == 1:
            # For type3 documents, we only have names (text column)
            df = pd.DataFrame(all_data, columns=['text'])
            # Rename column to match the provided header
            df.columns = header_list
        # If we have exactly two headers, assume they are 'text' and 'identifier'
        elif len(header_list) == 2:
            df = pd.DataFrame(all_data, columns=['text', 'identifier'])
            # Rename columns to match the provided headers
            df.columns = header_list
        else:
            # Otherwise, use the default column names
            df = pd.DataFrame(all_data)
            # If we have more headers than columns, add empty columns
            for i in range(len(df.columns), len(header_list)):
                df[f'column_{i}'] = ''
            # Rename columns to match the provided headers
            df.columns = header_list[:len(df.columns)]

        # Normalize all variations of #NA# to a consistent format
        na_variations = ['NA', 'na', 'N/A', 'n/a', '#NA#', '#NA', 'NA#', '#NA*', '#NAH', '-']

        # Clean up any tab characters in the data and normalize NA values
        for col in df.columns:
            if df[col].dtype == 'object':  # Only process string columns
                # Replace tab characters and clean up any "#NA#" text that might be appended
                df[col] = df[col].astype(str).str.replace('\t#NA#', '', regex=False)
                df[col] = df[col].str.replace('\t', ' ', regex=False)

                # Normalize all variations of NA to a consistent format
                for na_var in na_variations:
                    df[col] = df[col].str.replace(f'^{na_var}$', '', regex=True)

        # Remove rows where all values are NA, empty, or None
        df = df.replace(['NA', 'na', 'N/A', 'n/a', '#NA#', '#NA', 'NA#', '#NA*', '#NAH', '-'], np.nan)  # Convert NA text to NaN
        df = df.dropna(how='all')  # Drop rows where all values are NaN

        # Filter out rows with headers or placeholder values
        # This is important for type2 documents where we added "#NA#" text to empty cells
        if len(df) > 0:
            # Remove rows where the first column is empty or NaN
            df = df[~df.iloc[:, 0].isna()]

            # Remove rows where the first column is a header or contains placeholder values
            # This ensures that only actual data rows are included in the final output
            headers_and_placeholders = ['NOMS & PRENOMS', 'CIN', 'CIN (trois derniers chiffres)', 'ADRESSE', 'ADRESE',
                                       'NA', 'na', 'N/A', 'n/a', '#NA#', '#NA', 'NA#', '#NA*', '#NAH', '-', '']
            mask = ~df.iloc[:, 0].str.strip().isin(headers_and_placeholders)
            df = df[mask]

        # Replace NaN values with empty strings for better readability
        df = df.fillna('').replace('nan', '')

        # Save the DataFrame to CSV
        df.to_csv(output_file, index=False)
        logger.info(f"Saved {len(df)} rows to {output_file}")
    else:
        # Create an empty DataFrame with the provided headers
        df = pd.DataFrame(columns=header_list)
        df.to_csv(output_file, index=False)
        logger.info(f"No data extracted. Saved empty CSV to {output_file}")

    return output_file

def main(input_dir: str, headers: str, output_file: str, api_key: str = 'K888957') -> None:
    """
    Main entry point for the JORTPROC-OCRSpace subproject.

    Args:
        input_dir: Directory containing the subtable images
        headers: Comma-separated list of headers for the CSV file
        output_file: Path to the output CSV file
        api_key: OCR.space API key
    """
    try:
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Process OCR
        output_path = process_ocr_space(input_dir, headers, output_file, api_key)
        logger.info(f"Successfully processed OCR and saved results to {output_path}")
    except Exception as e:
        logger.error(f"Error processing OCR: {e}")
        raise

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Process OCR on images using OCR.space API")
    parser.add_argument("input_dir", help="Directory containing the subtable images")
    parser.add_argument("headers", help="Comma-separated list of headers for the CSV file")
    parser.add_argument("output_file", help="Path to the output CSV file")
    parser.add_argument("--api_key", default="K888957", help="OCR.space API key")

    args = parser.parse_args()
    main(args.input_dir, args.headers, args.output_file, args.api_key)
