#!/usr/bin/env python3
"""
Test script to verify #CE# cell markers are working correctly in Type3 documents.
This script simulates OCR results with #CE# markers to test the cell detection logic.
"""

import logging
from typing import List, Dict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simulate_ocr_with_markers() -> List[Dict]:
    """
    Simulate OCR results that include #CE# markers.
    This represents what OCR.space would return when processing images with cell markers.
    """
    # Simulate OCR lines with #CE# markers
    simulated_lines = [
        {"LineText": "BEN CHEIKH LARBI AMINE #CE#", "MinTop": 10, "Words": []},
        {"LineText": "HABIBI LAAROUSSI BEN", "MinTop": 40, "Words": []},
        {"LineText": "MOHAMED #CE#", "MinTop": 70, "Words": []},
        {"LineText": "SAAD SARRA #CE#", "MinTop": 100, "Words": []},
        {"LineText": "HADDAD HEDI #CE#", "MinTop": 130, "Words": []},
        {"LineText": "ZIDI FREDJ #CE#", "MinTop": 160, "Words": []},
        {"LineText": "DRAOUIL NIDHAL B MOHAMED", "MinTop": 190, "Words": []},
        {"LineText": "TAHER #CE#", "MinTop": 220, "Words": []},
        {"LineText": "KRIMI BASSEM BEN BOUJEMAA #CE#", "MinTop": 250, "Words": []},
        {"LineText": "MRABTI OLFA #CE#", "MinTop": 280, "Words": []},
        {"LineText": "BELGACEM HELLA BENT", "MinTop": 310, "Words": []},
        {"LineText": "MOHAMED #CE#", "MinTop": 340, "Words": []},
        {"LineText": "ERRACHI SIHEM BENT", "MinTop": 370, "Words": []},
        {"LineText": "ABDERRAHMEN #CE#", "MinTop": 400, "Words": []},
    ]
    return simulated_lines

def merge_cells_using_markers(lines: List[Dict]) -> List[Dict]:
    """
    Merge cells using #CE# (Cell End) markers.
    This is the core logic that would be used in the OCR processing.
    """
    try:
        logger.info("Merging cells using #CE# markers")
        
        if not lines:
            return []

        # Sort lines by vertical position (MinTop)
        sorted_lines = sorted(lines, key=lambda x: x.get('MinTop', 0))
        
        # Process lines and detect #CE# markers
        merged_cells = []
        current_cell_text = []
        
        for line in sorted_lines:
            line_text = line.get('LineText', '').strip()
            
            # Check if this line contains a #CE# marker (any variation)
            has_marker = any(marker in line_text for marker in ['#CE#', '#CE+', '#CE*', '#CE'])
            
            if has_marker:
                # Remove the marker from the text
                cleaned_text = line_text
                for marker in ['#CE#', '#CE+', '#CE*', '#CE']:
                    cleaned_text = cleaned_text.replace(marker, '').strip()
                
                # Add the cleaned text to current cell (if not empty)
                if cleaned_text:
                    current_cell_text.append(cleaned_text)
                
                # End current cell and start new one
                if current_cell_text:
                    cell_text = ' '.join(current_cell_text).strip()
                    if cell_text:  # Only add non-empty cells
                        merged_cells.append({'text': cell_text})
                        logger.info(f"MARKER_CELL: '{cell_text}' (ended by #CE# marker)")
                
                # Reset for next cell
                current_cell_text = []
            else:
                # Add line to current cell
                current_cell_text.append(line_text)
        
        # Handle the last cell (if no marker at the end)
        if current_cell_text:
            cell_text = ' '.join(current_cell_text).strip()
            if cell_text:
                merged_cells.append({'text': cell_text})
                logger.info(f"FINAL_CELL: '{cell_text}' (no ending marker)")
        
        logger.info(f"MARKER_BASED: Merged {len(sorted_lines)} lines into {len(merged_cells)} cells using #CE# markers")
        return merged_cells
        
    except Exception as e:
        logger.error(f"Error merging cells using markers: {e}")
        return []

def test_marker_based_merging():
    """Test the marker-based cell merging logic."""
    print("🧪 TESTING #CE# MARKER-BASED CELL DETECTION")
    print("=" * 60)
    
    # Get simulated OCR results with markers
    ocr_lines = simulate_ocr_with_markers()
    print(f"📄 Simulated OCR input: {len(ocr_lines)} lines")
    
    # Test marker-based merging
    merged_cells = merge_cells_using_markers(ocr_lines)
    
    print(f"\n📊 RESULTS:")
    print(f"   Input lines: {len(ocr_lines)}")
    print(f"   Output cells: {len(merged_cells)}")
    print(f"   Compression ratio: {len(ocr_lines)/len(merged_cells):.1f}x")
    
    print(f"\n✅ EXTRACTED NAMES:")
    for i, cell in enumerate(merged_cells, 1):
        print(f"   {i:2d}. {cell['text']}")
    
    # Expected results
    expected_names = [
        "BEN CHEIKH LARBI AMINE",
        "HABIBI LAAROUSSI BEN MOHAMED", 
        "SAAD SARRA",
        "HADDAD HEDI",
        "ZIDI FREDJ",
        "DRAOUIL NIDHAL B MOHAMED TAHER",
        "KRIMI BASSEM BEN BOUJEMAA",
        "MRABTI OLFA",
        "BELGACEM HELLA BENT MOHAMED",
        "ERRACHI SIHEM BENT ABDERRAHMEN"
    ]
    
    print(f"\n🎯 EXPECTED NAMES:")
    for i, name in enumerate(expected_names, 1):
        print(f"   {i:2d}. {name}")
    
    # Verify results
    extracted_names = [cell['text'] for cell in merged_cells]
    matches = sum(1 for name in extracted_names if name in expected_names)
    accuracy = matches / len(expected_names) * 100 if expected_names else 0
    
    print(f"\n📈 ACCURACY:")
    print(f"   Matches: {matches}/{len(expected_names)}")
    print(f"   Accuracy: {accuracy:.1f}%")
    
    if accuracy >= 90:
        print("   🎉 EXCELLENT performance!")
    elif accuracy >= 80:
        print("   ✅ GOOD performance!")
    elif accuracy >= 70:
        print("   ⚠️  FAIR performance")
    else:
        print("   ❌ POOR performance")

if __name__ == "__main__":
    test_marker_based_merging()
