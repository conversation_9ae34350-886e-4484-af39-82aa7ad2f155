#!/usr/bin/env python3
"""Run all tests for the JortExtractor system."""
import os
import sys
import argparse
import logging
import time
import datetime
import subprocess
from pathlib import Path

# Configure logging
log_file = f"test_run_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("test_runner")

def run_tests(test_type=None, verbose=False, parallel=False, max_workers=None):
    """Run tests of the specified type.
    
    Args:
        test_type: Type of tests to run (unit, integration, performance, etc.)
        verbose: Whether to show verbose output
        parallel: Whether to run tests in parallel
        max_workers: Maximum number of worker processes for parallel execution
    
    Returns:
        True if all tests passed, False otherwise
    """
    start_time = time.time()
    
    # Create command
    cmd = [sys.executable, "tests/run_tests.py"]
    
    if test_type:
        cmd.extend(["--type", test_type])
    
    if verbose:
        cmd.append("--verbose")
    
    if parallel:
        cmd.append("--parallel")
        
        if max_workers:
            cmd.extend(["--workers", str(max_workers)])
    
    # Run the tests
    logger.info(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # Log the output
    if result.stdout:
        logger.info(result.stdout)
    
    if result.stderr:
        logger.error(result.stderr)
    
    # Calculate duration
    end_time = time.time()
    duration = end_time - start_time
    
    logger.info(f"Tests completed in {duration:.2f} seconds")
    
    # Return True if tests passed
    return result.returncode == 0

def run_all_test_types(verbose=False, parallel=False, max_workers=None):
    """Run all types of tests.
    
    Args:
        verbose: Whether to show verbose output
        parallel: Whether to run tests in parallel
        max_workers: Maximum number of worker processes for parallel execution
    
    Returns:
        True if all tests passed, False otherwise
    """
    test_types = ["unit", "integration", "performance", "robustness", "security", "logging"]
    results = {}
    
    for test_type in test_types:
        logger.info(f"Running {test_type} tests...")
        success = run_tests(test_type, verbose, parallel, max_workers)
        results[test_type] = success
        
        if not success:
            logger.error(f"{test_type} tests failed")
    
    # Log summary
    logger.info("Test Summary:")
    for test_type, success in results.items():
        status = "PASSED" if success else "FAILED"
        logger.info(f"  {test_type}: {status}")
    
    # Return True if all tests passed
    return all(results.values())

def main():
    """Main function to parse arguments and run tests."""
    parser = argparse.ArgumentParser(description="Run JortExtractor tests")
    parser.add_argument("--type", choices=["unit", "integration", "performance", "robustness", "security", "logging", "all"],
                        default="all", help="Type of tests to run")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--parallel", action="store_true", help="Run tests in parallel")
    parser.add_argument("--workers", type=int, help="Maximum number of worker processes for parallel execution")
    
    args = parser.parse_args()
    
    # Run tests
    if args.type == "all":
        success = run_all_test_types(args.verbose, args.parallel, args.workers)
    else:
        success = run_tests(args.type, args.verbose, args.parallel, args.workers)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
