#!/usr/bin/env python3
"""
BENCHMARKING TOOL ONLY: Compare OCR results between our system and OCR.space API.

This script processes pre-OCR images with both our OCR system and the OCR.space API,
then compares the results and generates a report. This is for benchmarking and
evaluation purposes only.

IMPORTANT: This tool is NEVER used as an alternative to our own OCR implementation.
It is solely for benchmarking and identifying areas for improvement in our own OCR.
The OCR.space API is used only as a reference point, not as a fallback or replacement.
"""
import argparse
import datetime
import json
import logging
import os
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional

import cv2
import numpy as np
import pandas as pd
import pytesseract
from tabulate import tabulate

from jortextractor.core.pipeline import JortPDFProcessor
from jortextractor.core.image_processor import optimize_for_name_ocr
from jortextractor.core.name_validator import normalize_text
from jortextractor.benchmarks.ocr_space_client import OCRSpaceClient

# Configure logging
log_file = f"ocr_comparison_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("ocr_comparison")

class OCRComparator:
    """Class to compare OCR results between our system and OCR.space API for benchmarking only.

    This class is used solely for benchmarking and evaluation purposes. It compares
    the results of our OCR implementation with OCR.space to identify areas for improvement.

    WARNING: This is for testing and benchmarking only! The OCR.space API is never
    used as an alternative to our own OCR implementation in the main processing pipeline.
    """

    def __init__(self,
                 pre_ocr_dir: str = "pre-ocr-tables",
                 output_dir: str = "ocr_comparison_results",
                 api_key: str = "K888957"):
        """Initialize the OCR comparator.

        Args:
            pre_ocr_dir: Directory containing pre-OCR images
            output_dir: Directory to store comparison results
            api_key: OCR.space API key
        """
        self.pre_ocr_dir = Path(pre_ocr_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Create processor
        self.processor = JortPDFProcessor()

        # Create OCR.space client
        self.ocr_space_client = OCRSpaceClient(api_key=api_key)

        # Store results
        self.results = []

    def _extract_text_with_our_ocr(self, image_path: Path) -> List[str]:
        """Extract text from an image using our OCR system.

        Args:
            image_path: Path to the image

        Returns:
            List of extracted text strings
        """
        try:
            # Read image
            img = cv2.imread(str(image_path))
            if img is None:
                logger.error(f"Failed to read image: {image_path}")
                return []

            # Convert to grayscale if needed
            if len(img.shape) == 3:
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                gray = img.copy()

            # Optimize image for OCR
            optimized = optimize_for_name_ocr(gray)

            # Apply OCR
            custom_config = f"--psm 6 --oem 3 -l eng+fra --dpi 300"
            text = pytesseract.image_to_string(optimized, config=custom_config).strip()

            # Split by lines and filter out empty lines
            lines = [line.strip() for line in text.split('\n') if line.strip()]

            return lines
        except Exception as e:
            logger.error(f"Error extracting text with our OCR: {e}")
            return []

    def compare_image(self, image_path: Path) -> Dict:
        """Compare OCR results for a single image.

        Args:
            image_path: Path to the image

        Returns:
            Dictionary with comparison results
        """
        logger.info(f"Comparing OCR for image: {image_path}")

        # Extract text with our OCR
        our_texts = self._extract_text_with_our_ocr(image_path)

        # Extract text with OCR.space
        ocr_space_result = self.ocr_space_client.process_file(image_path)
        ocr_space_texts = self.ocr_space_client.extract_text(ocr_space_result)

        # Normalize texts for comparison
        our_texts_norm = [normalize_text(text) for text in our_texts]
        ocr_space_texts_norm = [normalize_text(text) for text in ocr_space_texts]

        # Find matches
        matched = 0
        matched_texts = []

        for i, text in enumerate(our_texts_norm):
            if text in ocr_space_texts_norm:
                matched += 1
                matched_texts.append(our_texts[i])

        # Calculate metrics
        total_ours = len(our_texts)
        total_ocr_space = len(ocr_space_texts)

        precision = matched / total_ours if total_ours > 0 else 0
        recall = matched / total_ocr_space if total_ocr_space > 0 else 0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

        # Create result dictionary
        result = {
            'image_path': str(image_path),
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'total_ours': total_ours,
            'total_ocr_space': total_ocr_space,
            'matched': matched,
            'our_texts': our_texts,
            'ocr_space_texts': ocr_space_texts,
            'matched_texts': matched_texts
        }

        # Log comparison
        logger.info(f"OCR Comparison for {image_path.name}:")
        logger.info(f"  Precision: {precision:.2f}")
        logger.info(f"  Recall: {recall:.2f}")
        logger.info(f"  F1 Score: {f1_score:.2f}")
        logger.info(f"  Our texts: {total_ours}, OCR.space texts: {total_ocr_space}, Matched: {matched}")

        return result

    def compare_all_images(self, limit: Optional[int] = None) -> List[Dict]:
        """Compare OCR results for all images in the pre-OCR directory.

        Args:
            limit: Maximum number of images to process (to avoid API rate limits)

        Returns:
            List of comparison result dictionaries
        """
        # Find all images
        images = list(self.pre_ocr_dir.glob("*.png"))

        if limit:
            images = images[:limit]

        logger.info(f"Found {len(images)} images to compare")

        # Compare each image
        for img_path in images:
            result = self.compare_image(img_path)
            self.results.append(result)

            # Save individual result
            self._save_image_report(result)

            # Wait to avoid hitting API rate limits
            time.sleep(1)

        # Generate summary report
        self._generate_summary_report()

        return self.results

    def _save_image_report(self, result: Dict) -> None:
        """Save a report for a single image comparison.

        Args:
            result: Comparison result dictionary
        """
        image_name = Path(result['image_path']).name
        report_path = self.output_dir / f"{image_name}_comparison.json"

        with open(report_path, 'w') as f:
            json.dump(result, f, indent=2)

    def _generate_summary_report(self) -> None:
        """Generate a summary report of all comparisons."""
        if not self.results:
            logger.warning("No results to generate summary report")
            return

        # Create DataFrame
        df = pd.DataFrame([
            {
                'image': Path(r['image_path']).name,
                'precision': r['precision'],
                'recall': r['recall'],
                'f1_score': r['f1_score'],
                'our_texts': r['total_ours'],
                'ocr_space_texts': r['total_ocr_space'],
                'matched': r['matched']
            }
            for r in self.results
        ])

        # Save CSV
        csv_path = self.output_dir / "ocr_comparison_summary.csv"
        df.to_csv(csv_path, index=False)

        # Calculate averages
        avg_precision = df['precision'].mean()
        avg_recall = df['recall'].mean()
        avg_f1 = df['f1_score'].mean()

        # Create summary table
        table_data = [
            ["Average", f"{avg_precision:.2f}", f"{avg_recall:.2f}", f"{avg_f1:.2f}"]
        ]

        for _, row in df.iterrows():
            table_data.append([
                row['image'],
                f"{row['precision']:.2f}",
                f"{row['recall']:.2f}",
                f"{row['f1_score']:.2f}"
            ])

        # Print summary table
        print("\nOCR Comparison Summary:")
        print(tabulate(
            table_data,
            headers=["Image", "Precision", "Recall", "F1 Score"],
            tablefmt="grid"
        ))

        logger.info(f"Saved summary report to {csv_path}")


def main():
    """Main function to parse arguments and run OCR comparison."""
    parser = argparse.ArgumentParser(description="Compare OCR results between our system and OCR.space API")
    parser.add_argument("--pre-ocr-dir", default="pre-ocr-tables",
                        help="Directory containing pre-OCR images")
    parser.add_argument("--output-dir", default="ocr_comparison_results",
                        help="Directory to store comparison results")
    parser.add_argument("--api-key", default="K888957",
                        help="OCR.space API key")
    parser.add_argument("--limit", type=int, default=5,
                        help="Maximum number of images to process (to avoid API rate limits)")

    args = parser.parse_args()

    # Create comparator
    comparator = OCRComparator(
        pre_ocr_dir=args.pre_ocr_dir,
        output_dir=args.output_dir,
        api_key=args.api_key
    )

    # Run comparison
    comparator.compare_all_images(limit=args.limit)


if __name__ == "__main__":
    main()
