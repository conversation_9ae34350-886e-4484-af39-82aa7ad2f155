#!/usr/bin/env python3
"""
OCR Benchmarking Tool

This script benchmarks our OCR implementation against ground truth data and
optionally against OCR.space API for comparison purposes.

IMPORTANT: The OCR.space API is used ONLY for benchmarking and comparison.
It is NEVER used as an alternative to our own OCR implementation in the main
processing pipeline. The purpose of this comparison is solely to identify areas
for improvement in our own OCR implementation.
"""
import argparse
import logging
import sys
import glob
from pathlib import Path

from jortextractor.benchmarks.ocr_benchmark import OCRBenchmark

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ocr_benchmark.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("ocr_benchmark")

def main():
    """Main function to parse arguments and run OCR benchmarking."""
    parser = argparse.ArgumentParser(
        description="Benchmark OCR performance on pre-OCR images",
        epilog="IMPORTANT: OCR.space is used for benchmarking only, never as an alternative to our own OCR."
    )
    parser.add_argument("--image-dir", default="pre-ocr-tables",
                        help="Directory containing pre-OCR images")
    parser.add_argument("--output-dir", default="ocr_benchmark_results",
                        help="Directory to store benchmark results")
    parser.add_argument("--compare-with-ocr-space", action="store_true",
                        help="Compare with OCR.space API (for benchmarking only, never used as an alternative)")
    parser.add_argument("--ocr-space-api-key", default="K888957",
                        help="OCR.space API key (for benchmarking only)")
    parser.add_argument("--limit", type=int, default=None,
                        help="Maximum number of images to process")
    parser.add_argument("--image-pattern", default="*.png",
                        help="Pattern to match image files")
    parser.add_argument("--no-html", action="store_true",
                        help="Disable HTML report generation")
    parser.add_argument("--open-report", action="store_true",
                        help="Open the HTML report in a browser after generation")

    args = parser.parse_args()

    if args.compare_with_ocr_space:
        print("\nWARNING: OCR.space comparison is for BENCHMARKING ONLY!")
        print("The OCR.space API is NEVER used as an alternative to our own OCR implementation.")
        print("It is used solely for benchmarking and comparison to identify areas for improvement.\n")

    # Create benchmark
    benchmark = OCRBenchmark(
        output_dir=args.output_dir,
        compare_with_ocr_space=args.compare_with_ocr_space,
        ocr_space_api_key=args.ocr_space_api_key
    )

    # Find images
    image_dir = Path(args.image_dir)
    image_paths = list(image_dir.glob(args.image_pattern))

    if not image_paths:
        logger.error(f"No images found in {image_dir} matching pattern {args.image_pattern}")
        return

    logger.info(f"Found {len(image_paths)} images to benchmark")

    # Run benchmark
    results = benchmark.benchmark_images(image_paths, limit=args.limit, generate_html=not args.no_html)

    # Open HTML report in browser if requested
    if args.compare_with_ocr_space and not args.no_html:
        report_path = Path(args.output_dir) / "ocr_comparison_report.html"
        if report_path.exists():
            print(f"\nHTML report generated with original images: {report_path}")
            print("The report includes the original images for better comparison.")

            if args.open_report:
                import webbrowser
                print(f"Opening HTML report in browser...")
                webbrowser.open(f"file://{report_path.absolute()}")
        else:
            print(f"\nHTML report not found: {report_path}")

    # Print path to detailed text report
    if args.compare_with_ocr_space:
        detailed_report_path = Path(args.output_dir) / "detailed_comparison.txt"
        if detailed_report_path.exists():
            print(f"\nDetailed text report saved to: {detailed_report_path}")
            print("Use 'cat' or a text editor to view the detailed comparison.")

if __name__ == "__main__":
    main()
