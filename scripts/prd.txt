# JortDataExtractor - Product Requirements Document

## Project Overview
JortDataExtractor is a Python application designed to extract and process tables from Tunisian Official Gazette (JORT) PDFs. The application focuses on extracting names and identifiers from these documents using OCR (Optical Character Recognition) technology. The system processes PDF documents, extracts tables, performs OCR on the tables, and outputs structured data.

## Core Features

### PDF Processing
- Process PDF documents from the Tunisian Official Gazette
- Detect and classify different document types
- Extract pages containing relevant tables
- Convert PDF pages to high-quality images for OCR processing

### Image Processing
- Preprocess images to optimize for OCR accuracy
- Detect table structures within images
- Apply image enhancement techniques (deskewing, border removal, sharpening)
- Generate inverted lined images with 2px line thickness for table borders
- Resize images only if they are ≥ 5MB, using lossless compression for PNG images

### OCR Processing
- Perform OCR on preprocessed images to extract text
- Focus on extracting names and identifiers from tables
- Validate and correct extracted names
- Handle multi-line names and complex table structures
- Support French language text recognition

### Data Validation and Correction
- Normalize extracted text
- Validate names against expected patterns
- Correct common OCR errors in names
- Ensure identifiers are properly formatted
- Filter and standardize name entries

### Parallel Processing
- Support parallel processing of multiple documents
- Optimize performance for large batches of PDFs

### Testing and Benchmarking
- Comprehensive test suite for all components
- OCR benchmarking tools for testing purposes only
- Compare OCR results with ground truth data
- Generate benchmark reports

## Technical Requirements

### Code Structure
- Modular, maintainable code architecture
- Clear separation of concerns
- Proper error handling and logging
- Comprehensive documentation

### Performance
- Efficient processing of large PDF documents
- Optimized image processing algorithms
- Memory-efficient operations

### Dependencies
- Python 3.x
- OpenCV for image processing
- Tesseract OCR for text recognition
- PDF processing libraries
- Minimal external dependencies

## Implementation Notes

### OCR Implementation
- Use only local OCR implementation for production
- OCR.space API used only for benchmarking and testing
- OCR.space parameters: detectTable=yes, ocrEngine=2, fileFormat=png, autoEnlarge=no, language=french
- OCR benchmarking functionality should only be part of the test suite

### Image Processing
- Use only _inverted_lined.png for both local OCR and OCR.space
- Use 2px line thickness for table borders
- Apply lossless compression for PNG images

### Data Handling
- Names should not be manipulated after OCR as it defeats the project's purpose
- The compare_with_ground_truth function should be separate from OCR clients

## Future Enhancements
- Support for additional document types
- Enhanced table detection algorithms
- Improved name validation and correction
- Web interface for document processing
- API for integration with other systems
