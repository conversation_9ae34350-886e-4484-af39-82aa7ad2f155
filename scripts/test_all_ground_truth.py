#!/usr/bin/env python3
"""
Comprehensive ground truth testing script for JortExtractor.

This script automatically tests all document types against their corresponding
ground truth data in the ProjectContext/DataExamples/ directory.
It can also compare our OCR results with OCR.space API for benchmarking purposes only.

IMPORTANT: The OCR.space API is used ONLY for benchmarking and comparison.
It is NEVER used as an alternative to our own OCR implementation in the main
processing pipeline. The purpose of this comparison is solely to identify areas
for improvement in our own OCR implementation.
"""

import os
import sys
import argparse
import logging
import time
import datetime
import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from tabulate import tabulate

from jortextractor.core.pipeline import JortPDFProcessor
from jortextractor.benchmarks.ocr_space_client import OCRSpaceClient

# Configure logging
log_file = f"ground_truth_test_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("ground_truth_tester")

class GroundTruthTester:
    """Class to test OCR results against ground truth data for all document types.

    This class provides methods to test our OCR implementation against ground truth data.
    It can optionally compare our OCR results with OCR.space API for benchmarking purposes only.

    WARNING: The OCR.space API is NEVER used as an alternative to our own OCR implementation.
    It is used solely for benchmarking and comparison to identify areas for improvement.
    """

    def __init__(self,
                 data_dir: str = "ProjectContext/DataExamples",
                 output_dir: str = "ground_truth_test_results",
                 compare_with_ocr_space: bool = False,
                 ocr_space_api_key: str = "K888957"):
        """Initialize the ground truth tester.

        Args:
            data_dir: Directory containing PDF and ground truth data
            output_dir: Directory to store test results
            compare_with_ocr_space: Whether to compare results with OCR.space API
            ocr_space_api_key: OCR.space API key
        """
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.results = []
        self.compare_with_ocr_space = compare_with_ocr_space
        self.ocr_space_results = []

        # Create processor
        self.processor = JortPDFProcessor()

        # Enable pre-OCR image saving
        self.processor.config['save_debug_images'] = True
        self.processor.config['save_pre_ocr_tables'] = True
        self.processor.config['save_pre_ocr_columns'] = True

        # Set directories
        self.processor.temp_dir = Path('temp_images')
        self.processor.pre_ocr_tables_dir = Path('pre-ocr-tables')
        self.processor.pre_ocr_columns_dir = Path('pre-ocr-columns')

        # Create directories
        self.processor.temp_dir.mkdir(parents=True, exist_ok=True)
        self.processor.pre_ocr_tables_dir.mkdir(parents=True, exist_ok=True)
        self.processor.pre_ocr_columns_dir.mkdir(parents=True, exist_ok=True)

        # Log the directories
        logger.info(f"Saving debug images to: {self.processor.temp_dir}")
        logger.info(f"Saving pre-OCR table images to: {self.processor.pre_ocr_tables_dir}")
        logger.info(f"Saving pre-OCR column images to: {self.processor.pre_ocr_columns_dir}")

        # Create OCR.space client if needed
        if compare_with_ocr_space:
            self.ocr_space_client = OCRSpaceClient(api_key=ocr_space_api_key)
            # Create directory for OCR.space comparison results
            self.ocr_space_dir = self.output_dir / "ocr_space_comparison"
            self.ocr_space_dir.mkdir(parents=True, exist_ok=True)

    def find_pdf_ground_truth_pairs(self) -> List[Tuple[Path, Path]]:
        """Find all PDF files and their corresponding ground truth CSV files.

        Returns:
            List of tuples containing (pdf_path, ground_truth_path)
        """
        pdf_files = list(self.data_dir.glob("*.pdf"))
        pairs = []

        for pdf_path in pdf_files:
            # Find corresponding ground truth files
            pdf_stem = pdf_path.stem
            ground_truth_paths = list(self.data_dir.glob(f"{pdf_stem}*.csv"))

            if ground_truth_paths:
                # Use the first matching CSV file
                pairs.append((pdf_path, ground_truth_paths[0]))
            else:
                logger.warning(f"No ground truth file found for {pdf_path}")

        return pairs

    def test_document(self, pdf_path: Path, ground_truth_path: Path) -> Dict[str, Any]:
        """Test a document against ground truth.

        Args:
            pdf_path: Path to the PDF document
            ground_truth_path: Path to the ground truth data

        Returns:
            Dictionary with test results
        """
        logger.info(f"Testing {pdf_path} against {ground_truth_path}")

        # Process the document - always use test_ocr=True to ensure OCR is performed
        start_time = time.time()
        # Extract document type from filename
        doc_type = None
        if "type1" in pdf_path.stem:
            doc_type = "type1"
        elif "type2" in pdf_path.stem:
            doc_type = "type2"
        elif "type3" in pdf_path.stem:
            doc_type = "type3"
        elif "type4" in pdf_path.stem:
            doc_type = "type4"

        # Process the PDF with the correct document type and ground truth path
        result = self.processor.process_pdf(pdf_path, doc_type, ground_truth_path, test_ocr=True)
        end_time = time.time()

        # Extract test metrics
        test_result = {
            "pdf_path": str(pdf_path),
            "ground_truth_path": str(ground_truth_path),
            "processing_time": end_time - start_time,
            "status": result.get("status", "unknown"),
            "metrics": {},
        }

        # Check if processing was successful
        if result.get("status") != "success":
            logger.error(f"Processing failed: {result.get('status')}")
            test_result["error"] = result.get("status")
            return test_result

        # Extract metrics
        if "comparison_stats" in result:
            test_result["metrics"] = result["comparison_stats"]

            # Extract missed names and false positives
            if "missed_names" in result:
                test_result["missed_names"] = result["missed_names"]

            if "false_positives" in result:
                test_result["false_positives"] = result["false_positives"]

            if "near_matches" in result:
                test_result["near_matches"] = result["near_matches"]

        # Save the result
        self.results.append(test_result)
        self._save_report(test_result)

        # Compare with OCR.space if enabled
        if self.compare_with_ocr_space:
            self._compare_with_ocr_space(pdf_path, ground_truth_path, test_result)

        return test_result

    def _compare_with_ocr_space(self, pdf_path: Path, ground_truth_path: Path, test_result: Dict[str, Any]) -> None:
        """Compare our OCR results with OCR.space API for benchmarking purposes only.

        This method is used solely for benchmarking and comparison. The OCR.space API
        is NEVER used as an alternative to our own OCR implementation in the main
        processing pipeline.

        Args:
            pdf_path: Path to the PDF document
            ground_truth_path: Path to the ground truth data
            test_result: Our test result dictionary
        """
        logger.info(f"Comparing with OCR.space API: {pdf_path}")

        # Get pre-OCR images
        pre_ocr_images = list(self.processor.pre_ocr_tables_dir.glob(f"*{pdf_path.stem}*.png"))

        if not pre_ocr_images:
            logger.warning(f"No pre-OCR images found for {pdf_path}")
            return

        # Process a subset of images to avoid API rate limits
        ocr_space_results = []

        for img_path in pre_ocr_images[:3]:  # Limit to first 3 images
            logger.info(f"Processing image with OCR.space: {img_path}")

            # Get OCR.space result
            ocr_space_result = self.ocr_space_client.process_file(img_path)

            if not ocr_space_result:
                logger.error(f"Failed to get OCR.space result for {img_path}")
                continue

            # Extract text
            ocr_space_texts = self.ocr_space_client.extract_text(ocr_space_result)

            # Save result
            result_dict = {
                "image_path": str(img_path),
                "pdf_path": str(pdf_path),
                "ocr_space_result": ocr_space_result,
                "ocr_space_texts": ocr_space_texts
            }

            ocr_space_results.append(result_dict)

            # Save individual result
            result_path = self.ocr_space_dir / f"{img_path.stem}_ocr_space.json"
            with open(result_path, "w") as f:
                # We need to convert the result to a serializable format
                serializable_result = {
                    "image_path": str(img_path),
                    "pdf_path": str(pdf_path),
                    "ocr_space_texts": ocr_space_texts
                }
                json.dump(serializable_result, f, indent=2)

            # Wait to avoid hitting API rate limits
            time.sleep(1)

        # Add OCR.space results to test result
        test_result["ocr_space_comparison"] = {
            "num_images_processed": len(ocr_space_results),
            "images": [r["image_path"] for r in ocr_space_results]
        }

        # Save updated test result
        self._save_report(test_result)

    def test_all_documents(self) -> List[Dict[str, Any]]:
        """Test all documents against ground truth.

        Returns:
            List of dictionaries with test results
        """
        # Find all PDF and ground truth pairs
        pairs = self.find_pdf_ground_truth_pairs()

        logger.info(f"Found {len(pairs)} PDF/ground truth pairs")

        # Test each document
        for pdf_path, ground_truth_path in pairs:
            self.test_document(pdf_path, ground_truth_path)

        # Generate summary report
        self._generate_summary_report()

        return self.results

    def _save_report(self, result: Dict[str, Any]) -> None:
        """Save a test report for a single document.

        Args:
            result: Test result dictionary
        """
        pdf_path = Path(result["pdf_path"])
        report_path = self.output_dir / f"{pdf_path.stem}_test_report.json"

        with open(report_path, "w") as f:
            json.dump(result, f, indent=2)

    def _generate_summary_report(self) -> None:
        """Generate a summary report of all test results."""
        if not self.results:
            logger.warning("No test results to summarize")
            return

        # Create summary CSV
        csv_path = self.output_dir / "summary_report.csv"

        # Create summary table
        table_data = []
        headers = ["Document Type", "Precision", "Recall", "F1 Score", "Matched/Extracted", "Ground Truth", "Char Accuracy"]

        for result in self.results:
            pdf_name = Path(result["pdf_path"]).stem
            doc_type = "Unknown"

            # Determine document type from filename
            if "type1" in pdf_name:
                doc_type = "Type 1"
            elif "type2" in pdf_name:
                doc_type = "Type 2"
            elif "type3" in pdf_name:
                doc_type = "Type 3"
            elif "type4" in pdf_name:
                doc_type = "Type 4"

            metrics = result.get("metrics", {})
            precision = metrics.get("precision", 0) * 100
            recall = metrics.get("recall", 0) * 100
            f1_score = metrics.get("f1_score", 0) * 100
            matched = metrics.get("matched", 0)
            total_extracted = metrics.get("total_extracted", 0)
            total_truth = metrics.get("total_truth", 0)
            char_accuracy = metrics.get("avg_char_accuracy", 0) * 100

            table_data.append([
                doc_type,
                f"{precision:.1f}%",
                f"{recall:.1f}%",
                f"{f1_score:.1f}%",
                f"{matched}/{total_extracted}",
                total_truth,
                f"{char_accuracy:.1f}%"
            ])

        # Print summary table
        print("\nGround Truth Test Summary:")
        print(tabulate(table_data, headers=headers, tablefmt="grid"))

        # Save CSV report
        with open(csv_path, "w", newline="") as f:
            f.write(",".join([
                "Document Type",
                "PDF File",
                "Ground Truth File",
                "Status",
                "Processing Time (s)",
                "Precision",
                "Recall",
                "F1 Score",
                "Exact Matches",
                "Fuzzy Matches",
                "Total Extracted",
                "Total Truth",
                "Matched",
                "Missed",
                "False Positives",
                "Char Accuracy",
                "Correctable Matches",
                "Potential Recall"
            ]) + "\n")

            for result in self.results:
                pdf_path = Path(result["pdf_path"])
                pdf_name = pdf_path.stem
                doc_type = "Unknown"

                # Determine document type from filename
                if "type1" in pdf_name:
                    doc_type = "Type 1"
                elif "type2" in pdf_name:
                    doc_type = "Type 2"
                elif "type3" in pdf_name:
                    doc_type = "Type 3"
                elif "type4" in pdf_name:
                    doc_type = "Type 4"

                metrics = result.get("metrics", {})

                f.write(",".join([
                    doc_type,
                    result["pdf_path"],
                    result["ground_truth_path"],
                    result["status"],
                    f"{result['processing_time']:.2f}",
                    f"{metrics.get('precision', 0):.4f}",
                    f"{metrics.get('recall', 0):.4f}",
                    f"{metrics.get('f1_score', 0):.4f}",
                    str(metrics.get("exact_matches", 0)),
                    str(metrics.get("fuzzy_matches", 0)),
                    str(metrics.get("total_extracted", 0)),
                    str(metrics.get("total_truth", 0)),
                    str(metrics.get("matched", 0)),
                    str(metrics.get("missed", 0)),
                    str(metrics.get("false_positives", 0)),
                    f"{metrics.get('avg_char_accuracy', 0):.4f}",
                    str(metrics.get("correctable_matches", 0)),
                    f"{metrics.get('potential_recall', 0):.4f}"
                ]) + "\n")

        logger.info(f"Summary report saved to {csv_path}")

def main():
    """Main function to parse arguments and run ground truth tests."""
    parser = argparse.ArgumentParser(description="Test JortExtractor against ground truth for all document types")
    parser.add_argument("--data-dir", default="ProjectContext/DataExamples",
                        help="Directory containing PDF and ground truth data")
    parser.add_argument("--output-dir", default="ground_truth_test_results",
                        help="Directory to store test results")
    parser.add_argument("--compare-with-ocr-space", action="store_true",
                        help="Compare results with OCR.space API (for benchmarking only, never used as an alternative)")
    parser.add_argument("--ocr-space-api-key", default="K888957",
                        help="OCR.space API key (for benchmarking only)")

    args = parser.parse_args()

    if args.compare_with_ocr_space:
        print("\nWARNING: OCR.space comparison is for BENCHMARKING ONLY!")
        print("The OCR.space API is NEVER used as an alternative to our own OCR implementation.")
        print("It is used solely for benchmarking and comparison to identify areas for improvement.\n")

    # Create tester
    tester = GroundTruthTester(
        data_dir=args.data_dir,
        output_dir=args.output_dir,
        compare_with_ocr_space=args.compare_with_ocr_space,
        ocr_space_api_key=args.ocr_space_api_key
    )

    # Run tests
    tester.test_all_documents()

if __name__ == "__main__":
    main()
