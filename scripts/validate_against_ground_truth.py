#!/usr/bin/env python3
"""
Ground truth validation script for JortExtractor.

This script processes test documents and compares the results with ground truth data,
generating detailed reports on accuracy metrics.
"""

import os
import sys
import argparse
import logging
import time
import datetime
import json
import csv
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional

from jortextractor.core.pipeline import JortPDFProcessor
from jortextractor.core.name_validator import normalize_text

# Configure logging
log_file = f"ground_truth_validation_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("ground_truth_validator")

class GroundTruthValidator:
    """Class to validate OCR results against ground truth data."""

    def __init__(self, ground_truth_dir: str = "ProjectContext/DataExamples",
                 output_dir: str = "ground_truth_validation_reports"):
        """Initialize the ground truth validator.

        Args:
            ground_truth_dir: Directory containing ground truth data
            output_dir: Directory to store validation reports
        """
        self.ground_truth_dir = Path(ground_truth_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.results = []

        # Create processor
        self.processor = JortPDFProcessor()

        # Enable pre-OCR image saving
        self.processor.pre_ocr_dir = self.output_dir / "pre_ocr_images"
        self.processor.pre_ocr_dir.mkdir(parents=True, exist_ok=True)

        # Enable temporary directory for debug images
        self.processor.temp_dir = self.output_dir / "debug_images"
        self.processor.temp_dir.mkdir(parents=True, exist_ok=True)

    def validate_document(self, pdf_path: Path, ground_truth_path: Path) -> Dict[str, Any]:
        """Validate a document against ground truth.

        Args:
            pdf_path: Path to the PDF document
            ground_truth_path: Path to the ground truth data

        Returns:
            Dictionary with validation results
        """
        logger.info(f"Validating {pdf_path} against {ground_truth_path}")

        # Process the document
        start_time = time.time()
        result = self.processor.process_pdf(pdf_path, ground_truth_path)
        end_time = time.time()

        # Extract validation metrics
        validation_result = {
            "pdf_path": str(pdf_path),
            "ground_truth_path": str(ground_truth_path),
            "processing_time": end_time - start_time,
            "status": result["status"],
            "metrics": {},
            "missed_names": [],
            "false_positives": [],
        }

        # Check if processing was successful
        if result["status"] != "success":
            logger.error(f"Processing failed: {result['status']}")
            validation_result["error"] = result["status"]
            return validation_result

        # Extract metrics
        if "comparison_stats" in result:
            validation_result["metrics"] = result["comparison_stats"]

            # Extract missed names and false positives
            if "missed_names" in result:
                validation_result["missed_names"] = result["missed_names"]

            if "false_positives" in result:
                validation_result["false_positives"] = result["false_positives"]

        # Save the result
        self.results.append(validation_result)
        self._save_report(validation_result)

        return validation_result

    def validate_all_documents(self) -> List[Dict[str, Any]]:
        """Validate all documents against ground truth.

        Returns:
            List of dictionaries with validation results
        """
        # Find all PDF files
        pdf_files = list(self.ground_truth_dir.glob("*.pdf"))

        logger.info(f"Found {len(pdf_files)} PDF files")

        # Validate each document
        for pdf_path in pdf_files:
            # Find corresponding ground truth file
            pdf_stem = pdf_path.stem
            ground_truth_paths = list(self.ground_truth_dir.glob(f"{pdf_stem}*.csv"))

            if not ground_truth_paths:
                logger.warning(f"No ground truth file found for {pdf_path}")
                continue

            ground_truth_path = ground_truth_paths[0]
            self.validate_document(pdf_path, ground_truth_path)

        # Generate summary report
        self._generate_summary_report()

        return self.results

    def _save_report(self, result: Dict[str, Any]) -> None:
        """Save a validation report.

        Args:
            result: Validation results
        """
        # Create report filename
        pdf_path = Path(result["pdf_path"])
        report_name = f"{pdf_path.stem}_validation.json"
        report_path = self.output_dir / report_name

        # Save as JSON
        with open(report_path, "w") as f:
            json.dump(result, f, indent=2)

        logger.info(f"Saved validation report to {report_path}")

        # Create visualization
        self._create_visualization(result)

    def _create_visualization(self, result: Dict[str, Any]) -> None:
        """Create visualization of validation results.

        Args:
            result: Validation results
        """
        # Skip if no metrics
        if not result.get("metrics"):
            return

        # Extract metrics
        metrics = result["metrics"]
        pdf_path = Path(result["pdf_path"])

        # Create bar chart of precision, recall, and F1 score
        plt.figure(figsize=(10, 6))
        plt.bar(["Precision", "Recall", "F1 Score"],
                [metrics.get("precision", 0), metrics.get("recall", 0), metrics.get("f1_score", 0)])
        plt.ylim(0, 1)
        plt.title(f"Validation Metrics for {pdf_path.stem}")
        plt.ylabel("Score")

        # Save the chart
        chart_path = self.output_dir / f"{pdf_path.stem}_metrics.png"
        plt.savefig(chart_path)
        plt.close()

        # Create pie chart of matched vs. missed names
        plt.figure(figsize=(8, 8))
        matched = metrics.get("matched", 0)
        missed = metrics.get("total_truth", 0) - matched
        plt.pie([matched, missed], labels=["Matched", "Missed"], autopct="%1.1f%%")
        plt.title(f"Name Extraction Results for {pdf_path.stem}")

        # Save the chart
        chart_path = self.output_dir / f"{pdf_path.stem}_extraction.png"
        plt.savefig(chart_path)
        plt.close()

    def _generate_summary_report(self) -> None:
        """Generate a summary report of all validations."""
        # Create summary CSV
        csv_path = self.output_dir / "summary_report.csv"

        with open(csv_path, "w", newline="") as f:
            writer = csv.writer(f)

            # Write header
            writer.writerow([
                "PDF File",
                "Ground Truth File",
                "Status",
                "Processing Time (s)",
                "Precision",
                "Recall",
                "F1 Score",
                "Total Extracted",
                "Total Truth",
                "Matched"
            ])

            # Write data
            for result in self.results:
                metrics = result.get("metrics", {})
                writer.writerow([
                    result["pdf_path"],
                    result["ground_truth_path"],
                    result["status"],
                    f"{result['processing_time']:.2f}",
                    f"{metrics.get('precision', 0):.4f}",
                    f"{metrics.get('recall', 0):.4f}",
                    f"{metrics.get('f1_score', 0):.4f}",
                    metrics.get("total_extracted", 0),
                    metrics.get("total_truth", 0),
                    metrics.get("matched", 0)
                ])

        logger.info(f"Saved summary report to {csv_path}")

        # Create summary visualization
        self._create_summary_visualization()

    def _create_summary_visualization(self) -> None:
        """Create summary visualization of all validation results."""
        # Skip if no results
        if not self.results:
            return

        # Extract metrics
        pdf_names = [Path(result["pdf_path"]).stem for result in self.results]
        precision = [result.get("metrics", {}).get("precision", 0) for result in self.results]
        recall = [result.get("metrics", {}).get("recall", 0) for result in self.results]
        f1_score = [result.get("metrics", {}).get("f1_score", 0) for result in self.results]

        # Create bar chart of precision, recall, and F1 score for all documents
        plt.figure(figsize=(12, 8))
        x = np.arange(len(pdf_names))
        width = 0.25

        plt.bar(x - width, precision, width, label="Precision")
        plt.bar(x, recall, width, label="Recall")
        plt.bar(x + width, f1_score, width, label="F1 Score")

        plt.xlabel("Document")
        plt.ylabel("Score")
        plt.title("Validation Metrics for All Documents")
        plt.xticks(x, pdf_names, rotation=45, ha="right")
        plt.ylim(0, 1)
        plt.legend()
        plt.tight_layout()

        # Save the chart
        chart_path = self.output_dir / "summary_metrics.png"
        plt.savefig(chart_path)
        plt.close()

        # Create average metrics chart
        avg_precision = np.mean(precision)
        avg_recall = np.mean(recall)
        avg_f1_score = np.mean(f1_score)

        plt.figure(figsize=(8, 6))
        plt.bar(["Precision", "Recall", "F1 Score"], [avg_precision, avg_recall, avg_f1_score])
        plt.ylim(0, 1)
        plt.title("Average Validation Metrics")
        plt.ylabel("Score")

        # Save the chart
        chart_path = self.output_dir / "average_metrics.png"
        plt.savefig(chart_path)
        plt.close()

def main():
    """Main function to parse arguments and run ground truth validation."""
    parser = argparse.ArgumentParser(description="Validate JortExtractor against ground truth")
    parser.add_argument("--ground-truth-dir", default="ProjectContext/DataExamples",
                        help="Directory containing ground truth data")
    parser.add_argument("--output-dir", default="ground_truth_validation_reports",
                        help="Directory to store validation reports")

    args = parser.parse_args()

    # Create validator
    validator = GroundTruthValidator(
        ground_truth_dir=args.ground_truth_dir,
        output_dir=args.output_dir
    )

    # Run validation
    validator.validate_all_documents()

    logger.info("Ground truth validation complete")

if __name__ == "__main__":
    main()
