#!/usr/bin/env python3
"""
Test evaluation script for JortExtractor.

This script runs tests and evaluates their quality, generating reports for each test file.
"""

import os
import sys
import argparse
import unittest
import importlib
import inspect
import logging
import time
import datetime
import json
import csv
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
import traceback

# Configure logging
log_file = f"test_evaluation_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("test_evaluator")

# Define test categories
TEST_CATEGORIES = {
    "unit": "tests/unit",
    "integration": "tests/integration",
    "performance": "tests/performance",
    "robustness": "tests/robustness",
    "security": "tests/security",
    "logging": "tests/logging",
}

class TestEvaluator:
    """Class to evaluate test quality and generate reports."""

    def __init__(self, output_dir: str = "test_evaluation_reports"):
        """Initialize the test evaluator.

        Args:
            output_dir: Directory to store evaluation reports
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.results = []

    def evaluate_test_file(self, test_file: Path) -> Dict[str, Any]:
        """Evaluate a single test file.

        Args:
            test_file: Path to the test file

        Returns:
            Dictionary with evaluation results
        """
        logger.info(f"Evaluating test file: {test_file}")

        # Basic test file information
        result = {
            "test_file": str(test_file),
            "test_category": self._determine_category(test_file),
            "component_tested": self._determine_component(test_file),
            "evaluation_date": datetime.datetime.now().strftime("%Y-%m-%d"),
            "purpose_analysis": {},
            "test_design": {},
            "implementation_quality": {},
            "execution_results": {},
            "ground_truth_validation": {},
            "enhancement_plan": {},
        }

        # Load the test module
        module_name = self._get_module_name(test_file)
        try:
            # Try to import the module
            try:
                module = importlib.import_module(module_name)
            except ImportError as ie:
                logger.error(f"Could not import module {module_name}: {ie}")
                result["error"] = f"Import error: {str(ie)}"
                result["traceback"] = traceback.format_exc()
                self.results.append(result)
                self._save_report(result)
                return result

            # Analyze purpose
            result["purpose_analysis"] = self._analyze_purpose(module)

            # Evaluate test design
            result["test_design"] = self._evaluate_test_design(module)

            # Evaluate implementation quality
            result["implementation_quality"] = self._evaluate_implementation_quality(module)

            # Run the tests and record results
            result["execution_results"] = self._run_tests(module)

            # Evaluate ground truth validation
            result["ground_truth_validation"] = self._evaluate_ground_truth_validation(module)

            # Create enhancement plan
            result["enhancement_plan"] = self._create_enhancement_plan(result)

        except Exception as e:
            logger.error(f"Error evaluating {test_file}: {e}")
            result["error"] = str(e)
            result["traceback"] = traceback.format_exc()

        # Save the result
        self.results.append(result)
        self._save_report(result)

        return result

    def evaluate_all_tests(self, test_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Evaluate all tests of the specified type.

        Args:
            test_type: Type of tests to evaluate (unit, integration, etc.)

        Returns:
            List of dictionaries with evaluation results
        """
        if test_type in TEST_CATEGORIES:
            logger.info(f"Evaluating {test_type} tests...")
            test_dir = Path(TEST_CATEGORIES[test_type])
        else:
            logger.info("Evaluating all tests...")
            test_dir = Path("tests")

        # Find all test files
        test_files = []
        for root, _, files in os.walk(test_dir):
            for file in files:
                if file.startswith("test_") and file.endswith(".py") and "test_utils.py" not in file:
                    test_files.append(Path(root) / file)

        logger.info(f"Found {len(test_files)} test files")

        # Evaluate each test file
        for test_file in test_files:
            self.evaluate_test_file(test_file)

        # Generate summary report
        self._generate_summary_report()

        return self.results

    def _determine_category(self, test_file: Path) -> str:
        """Determine the category of a test file.

        Args:
            test_file: Path to the test file

        Returns:
            Category of the test file
        """
        for category, directory in TEST_CATEGORIES.items():
            if directory in str(test_file):
                return category
        return "unknown"

    def _determine_component(self, test_file: Path) -> str:
        """Determine the component tested by a test file.

        Args:
            test_file: Path to the test file

        Returns:
            Component tested by the test file
        """
        filename = test_file.name
        if filename.startswith("test_"):
            component = filename[5:].replace(".py", "")
            return component
        return "unknown"

    def _get_module_name(self, test_file: Path) -> str:
        """Get the module name for a test file.

        Args:
            test_file: Path to the test file

        Returns:
            Module name
        """
        # Handle both absolute and relative paths
        try:
            rel_path = test_file.relative_to(Path.cwd())
        except ValueError:
            # If test_file is already a relative path
            rel_path = test_file

        module_name = str(rel_path).replace("/", ".").replace("\\", ".").replace(".py", "")
        return module_name

    def _analyze_purpose(self, module) -> Dict[str, str]:
        """Analyze the purpose of a test module.

        Args:
            module: Test module

        Returns:
            Dictionary with purpose analysis
        """
        # Get module docstring
        module_doc = module.__doc__ or ""

        # Get test classes
        test_classes = [obj for name, obj in inspect.getmembers(module)
                       if inspect.isclass(obj) and issubclass(obj, unittest.TestCase)]

        # Get test methods
        test_methods = []
        for cls in test_classes:
            methods = [getattr(cls, name) for name in dir(cls)
                      if name.startswith("test_") and callable(getattr(cls, name))]
            test_methods.extend(methods)

        # Extract purposes from docstrings
        stated_purpose = module_doc.strip().split("\n")[0] if module_doc else "Not specified"

        # Analyze what's actually being tested
        actual_feature = []
        for method in test_methods:
            doc = method.__doc__ or ""
            if doc:
                actual_feature.append(doc.strip().split("\n")[0])

        # Identify potential coverage gaps
        coverage_gaps = []

        return {
            "stated_purpose": stated_purpose,
            "actual_feature": actual_feature,
            "coverage_gaps": coverage_gaps
        }

    def _evaluate_test_design(self, module) -> Dict[str, Dict[str, str]]:
        """Evaluate the design of tests in a module.

        Args:
            module: Test module

        Returns:
            Dictionary with test design evaluation
        """
        # Get test classes
        test_classes = [obj for name, obj in inspect.getmembers(module)
                       if inspect.isclass(obj) and issubclass(obj, unittest.TestCase)]

        # Evaluate test design criteria
        result = {
            "single_feature": {"status": "Unknown", "notes": ""},
            "clear_assertions": {"status": "Unknown", "notes": ""},
            "appropriate_fixtures": {"status": "Unknown", "notes": ""},
            "independence": {"status": "Unknown", "notes": ""},
            "cleanup": {"status": "Unknown", "notes": ""},
        }

        # Check for setUp and tearDown methods
        has_setup = any(hasattr(cls, "setUp") for cls in test_classes)
        has_teardown = any(hasattr(cls, "tearDown") for cls in test_classes)

        if has_setup:
            result["appropriate_fixtures"]["status"] = "Yes"
            result["appropriate_fixtures"]["notes"] = "Uses setUp method"
        else:
            result["appropriate_fixtures"]["status"] = "No"
            result["appropriate_fixtures"]["notes"] = "No setUp method found"

        if has_teardown:
            result["cleanup"]["status"] = "Yes"
            result["cleanup"]["notes"] = "Uses tearDown method"
        else:
            result["cleanup"]["status"] = "No"
            result["cleanup"]["notes"] = "No tearDown method found"

        return result

    def _evaluate_implementation_quality(self, module) -> Dict[str, Dict[str, str]]:
        """Evaluate the implementation quality of tests in a module.

        Args:
            module: Test module

        Returns:
            Dictionary with implementation quality evaluation
        """
        # Evaluate implementation quality criteria
        result = {
            "avoids_hardcoded_values": {"status": "Unknown", "notes": ""},
            "avoids_implementation_assumptions": {"status": "Unknown", "notes": ""},
            "normal_execution_paths": {"status": "Unknown", "notes": ""},
            "appropriate_mocking": {"status": "Unknown", "notes": ""},
            "error_handling": {"status": "Unknown", "notes": ""},
        }

        # Check for use of mocking
        module_src = inspect.getsource(module)
        if "patch" in module_src or "MagicMock" in module_src:
            result["appropriate_mocking"]["status"] = "Yes"
            result["appropriate_mocking"]["notes"] = "Uses mocking"
        else:
            result["appropriate_mocking"]["status"] = "No"
            result["appropriate_mocking"]["notes"] = "No mocking found"

        # Check for error handling
        if "try" in module_src and "except" in module_src:
            result["error_handling"]["status"] = "Yes"
            result["error_handling"]["notes"] = "Uses try-except blocks"
        else:
            result["error_handling"]["status"] = "No"
            result["error_handling"]["notes"] = "No error handling found"

        return result

    def _run_tests(self, module) -> Dict[str, Any]:
        """Run tests in a module and record results.

        Args:
            module: Test module

        Returns:
            Dictionary with test execution results
        """
        # Create a test suite
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)

        # Run the tests
        result = unittest.TestResult()
        start_time = time.time()
        suite.run(result)
        end_time = time.time()

        # Record results
        execution_results = {
            "pass_fail_status": "Pass" if result.wasSuccessful() else "Fail",
            "tests_run": result.testsRun,
            "errors": len(result.errors),
            "failures": len(result.failures),
            "skipped": len(result.skipped),
            "execution_time": end_time - start_time,
            "error_messages": [],
            "stack_traces": [],
        }

        # Record error messages and stack traces
        for test, trace in result.errors:
            execution_results["error_messages"].append(str(test))
            execution_results["stack_traces"].append(trace)

        for test, trace in result.failures:
            execution_results["error_messages"].append(str(test))
            execution_results["stack_traces"].append(trace)

        return execution_results

    def _evaluate_ground_truth_validation(self, module) -> Dict[str, Any]:
        """Evaluate ground truth validation in a module.

        Args:
            module: Test module

        Returns:
            Dictionary with ground truth validation evaluation
        """
        # Check if the module uses ground truth data
        module_src = inspect.getsource(module)
        uses_ground_truth = "ground_truth" in module_src.lower() or "groundtruth" in module_src.lower()

        result = {
            "uses_ground_truth": "Yes" if uses_ground_truth else "No",
            "ground_truth_source": "Unknown",
            "validation_metrics": {
                "recall": "Unknown",
                "precision": "Unknown",
                "f1_score": "Unknown",
            },
            "validation_issues": [],
        }

        # Try to determine ground truth source
        if uses_ground_truth:
            if "ProjectContext/DataExamples" in module_src:
                result["ground_truth_source"] = "ProjectContext/DataExamples"
            elif "test_data" in module_src:
                result["ground_truth_source"] = "tests/test_data"

        return result

    def _create_enhancement_plan(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Create an enhancement plan based on evaluation results.

        Args:
            result: Evaluation results

        Returns:
            Dictionary with enhancement plan
        """
        # Identify issues
        issues = []

        # Check test design issues
        for criterion, evaluation in result["test_design"].items():
            if evaluation["status"] == "No":
                issues.append(f"Test design issue: {criterion} - {evaluation['notes']}")

        # Check implementation quality issues
        for criterion, evaluation in result["implementation_quality"].items():
            if evaluation["status"] == "No":
                issues.append(f"Implementation quality issue: {criterion} - {evaluation['notes']}")

        # Check execution issues
        if result["execution_results"]["pass_fail_status"] == "Fail":
            issues.append(f"Execution issue: {result['execution_results']['failures']} failures, {result['execution_results']['errors']} errors")

        # Check ground truth validation issues
        if result["ground_truth_validation"]["uses_ground_truth"] == "No":
            issues.append("No ground truth validation")

        # Create enhancement plan
        enhancements = []
        for issue in issues:
            if "setUp" in issue:
                enhancements.append("Add setUp method to initialize test fixtures")
            elif "tearDown" in issue:
                enhancements.append("Add tearDown method to clean up after tests")
            elif "mocking" in issue:
                enhancements.append("Add appropriate mocking to isolate the component under test")
            elif "error handling" in issue:
                enhancements.append("Add error handling to make tests more robust")
            elif "ground truth" in issue:
                enhancements.append("Add ground truth validation to verify accuracy")
            elif "Execution issue" in issue:
                enhancements.append("Fix failing tests")

        # Determine priority
        priority = "High" if result["execution_results"]["pass_fail_status"] == "Fail" else "Medium"

        # Estimate effort
        effort = "Large" if len(enhancements) > 3 else "Medium" if len(enhancements) > 1 else "Small"

        return {
            "identified_issues": issues,
            "proposed_enhancements": enhancements,
            "priority": priority,
            "effort_estimate": effort,
            "dependencies": [],
        }

    def _save_report(self, result: Dict[str, Any]) -> None:
        """Save an evaluation report.

        Args:
            result: Evaluation results
        """
        # Create report filename
        test_file = Path(result["test_file"])
        report_name = f"{test_file.stem}_evaluation.json"
        report_path = self.output_dir / report_name

        # Save as JSON
        with open(report_path, "w") as f:
            json.dump(result, f, indent=2)

        logger.info(f"Saved evaluation report to {report_path}")

    def _generate_summary_report(self) -> None:
        """Generate a summary report of all evaluations."""
        # Create summary CSV
        csv_path = self.output_dir / "summary_report.csv"

        with open(csv_path, "w", newline="") as f:
            writer = csv.writer(f)

            # Write header
            writer.writerow([
                "Test File",
                "Category",
                "Component",
                "Pass/Fail",
                "Tests Run",
                "Failures",
                "Errors",
                "Uses Ground Truth",
                "Priority",
                "Effort"
            ])

            # Write data
            for result in self.results:
                writer.writerow([
                    result["test_file"],
                    result["test_category"],
                    result["component_tested"],
                    result["execution_results"]["pass_fail_status"],
                    result["execution_results"]["tests_run"],
                    result["execution_results"]["failures"],
                    result["execution_results"]["errors"],
                    result["ground_truth_validation"]["uses_ground_truth"],
                    result["enhancement_plan"]["priority"],
                    result["enhancement_plan"]["effort_estimate"]
                ])

        logger.info(f"Saved summary report to {csv_path}")

def main():
    """Main function to parse arguments and run test evaluation."""
    parser = argparse.ArgumentParser(description="Evaluate JortExtractor tests")
    parser.add_argument("--type", choices=list(TEST_CATEGORIES.keys()) + ["all"],
                        default="all", help="Type of tests to evaluate")
    parser.add_argument("--output-dir", default="test_evaluation_reports",
                        help="Directory to store evaluation reports")

    args = parser.parse_args()

    # Create evaluator
    evaluator = TestEvaluator(output_dir=args.output_dir)

    # Run evaluation
    evaluator.evaluate_all_tests(None if args.type == "all" else args.type)

    logger.info("Test evaluation complete")

if __name__ == "__main__":
    main()
