"""Post-processing functions for Type 1 documents."""

import re
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Union

logger = logging.getLogger(__name__)

def clean_name(name: str) -> str:
    """Return the name as is without any manipulation.

    Names should not be manipulated after OCR as it defeats the purpose of the project.
    The only allowed operations are:
    1. Split text based on column boundaries
    2. Format identifiers with "*****" prefix
    3. Extract only digits from identifiers
    4. Limit identifiers to 3 digits

    Args:
        name: The name from OCR

    Returns:
        The name without any manipulation
    """
    if not name:
        return ""

    # Return the name as is without any manipulation
    return name

def normalize_identifier(identifier: str) -> str:
    """Normalize an identifier by standardizing format.

    Args:
        identifier: The identifier to normalize

    Returns:
        The normalized identifier
    """
    if not identifier:
        return ""

    # If the identifier is just a single character like "*", it's likely incomplete
    if len(identifier.strip()) <= 1:
        return "*****"

    # Remove non-alphanumeric characters except asterisks and spaces
    identifier = re.sub(r'[^0-9A-Za-z\*\s]', '', identifier)

    # Replace multiple spaces with a single space
    identifier = re.sub(r'\s+', ' ', identifier)

    # Remove leading/trailing spaces
    identifier = identifier.strip()

    # If the identifier contains digits and doesn't start with asterisks,
    # format it as ***** followed by the digits
    if re.search(r'\d', identifier) and not identifier.startswith('*'):
        # Extract the digits
        digits = ''.join(re.findall(r'\d', identifier))

        # Format as ***** followed by the digits
        identifier = f"***** {digits}"

    # If the identifier doesn't have 5 asterisks at the beginning, add them
    if not identifier.startswith('*****'):
        # Count existing asterisks at the beginning
        asterisk_count = 0
        for char in identifier:
            if char == '*':
                asterisk_count += 1
            else:
                break

        # If there are some asterisks but not 5, replace them with exactly 5
        if 0 < asterisk_count < 5:
            identifier = identifier[asterisk_count:]  # Remove existing asterisks
            identifier = "***** " + identifier.lstrip()  # Add 5 asterisks
        # If there are no asterisks, add 5 at the beginning
        elif asterisk_count == 0 and identifier:
            identifier = "***** " + identifier

    # Ensure there's a space after the asterisks
    identifier = identifier.replace('*****', '***** ').replace('  ', ' ')

    # Remove any trailing spaces
    identifier = identifier.strip()

    return identifier

def is_valid_name(name: str) -> bool:
    """Check if a name is valid.

    Args:
        name: The name to check

    Returns:
        True if the name is valid, False otherwise
    """
    if not name:
        return False

    # Check if the name is too short
    if len(name) < 3:
        return False

    # Check if the name contains at least one letter
    if not re.search(r'[A-Za-z]', name):
        return False

    # Check if the name is a header or other non-name text
    if any(keyword in name.upper() for keyword in ["NOM", "PRENOM", "IDENTIFIANT", "PAGE"]):
        return False

    return True

def clean_type1_data(df: pd.DataFrame) -> pd.DataFrame:
    """Clean and standardize Type 1 data.

    Args:
        df: The DataFrame containing the extracted data

    Returns:
        The cleaned DataFrame with original order preserved
    """
    if df.empty:
        return df

    # Create a copy to avoid modifying the original DataFrame
    df_cleaned = df.copy()

    # Clean names and normalize identifiers
    if 'text' in df_cleaned.columns:
        df_cleaned['text'] = df_cleaned['text'].apply(clean_name)

    if 'identifier' in df_cleaned.columns:
        df_cleaned['identifier'] = df_cleaned['identifier'].apply(normalize_identifier)

    # Filter out invalid names
    if 'text' in df_cleaned.columns:
        df_cleaned = df_cleaned[df_cleaned['text'].apply(is_valid_name)]

    # Simply return the cleaned DataFrame, preserving the original order
    # This is the simplest approach that maintains the sequential processing of sub-tables
    return df_cleaned

def format_type1_output(df: pd.DataFrame) -> pd.DataFrame:
    """Format Type 1 output to match the ground truth format.

    Args:
        df: The DataFrame containing the extracted data

    Returns:
        The formatted DataFrame with only two columns and no headers
    """
    if df.empty:
        return df

    # Create a copy to avoid modifying the original DataFrame
    df_formatted = df.copy()

    # Keep only the text and identifier columns
    if 'text' in df_formatted.columns and 'identifier' in df_formatted.columns:
        df_formatted = df_formatted[['text', 'identifier']]

    # Rename columns to match ground truth
    df_formatted = df_formatted.rename(columns={'text': 'Nom et prénom', 'identifier': 'Identifiant'})

    return df_formatted
