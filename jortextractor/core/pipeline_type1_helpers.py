"""Helper methods for Type 1 document processing."""

import cv2
import numpy as np
import pytesseract
import matplotlib.pyplot as plt
from typing import Dict, List, Optional, Tuple, Union
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def detect_big_table_boundaries(image: np.ndarray) -> Tuple[int, int, int, int]:
    """Detect the boundaries of the big table in the image.

    Args:
        image: The input image

    Returns:
        Tuple of (x_min, y_min, x_max, y_max) coordinates
    """
    try:
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Apply thresholding to get binary image
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Calculate horizontal and vertical projections
        h_projection = np.sum(binary, axis=1)
        v_projection = np.sum(binary, axis=0)

        # Normalize projections
        if np.max(h_projection) > 0:
            h_projection = h_projection / np.max(h_projection)
        if np.max(v_projection) > 0:
            v_projection = v_projection / np.max(v_projection)

        # Find the boundaries of the table
        height, width = binary.shape

        # Force content width to be 70% of page width
        # This is a heuristic that works well for Type 1 documents
        logger.info("Forcing content width to 70% of page width")
        content_width = int(width * 0.7)
        x_min = int((width - content_width) / 2)
        x_max = x_min + content_width

        # Find the top boundary (y_min)
        # Skip the top 10% of the image (header)
        start_y = int(height * 0.1)
        y_min = start_y
        for i in range(start_y, height):
            if h_projection[i] > 0.1:  # Threshold for content
                y_min = i
                break

        # Find the bottom boundary (y_max)
        # Skip the bottom 10% of the image (footer)
        end_y = int(height * 0.9)
        y_max = end_y
        for i in range(height - 1, end_y, -1):
            if h_projection[i] > 0.1:  # Threshold for content
                y_max = i
                break

        # Ensure the boundaries are valid
        x_min = max(0, x_min)
        y_min = max(0, y_min)
        x_max = min(width, x_max)
        y_max = min(height, y_max)

        logger.info(f"Detected table boundaries: x_min={x_min}, y_min={y_min}, x_max={x_max}, y_max={y_max}")

        return x_min, y_min, x_max, y_max

    except Exception as e:
        logger.error(f"Error detecting table boundaries: {e}")
        # Return default values (full image)
        height, width = image.shape[:2]
        return 0, 0, width, height

def preprocess_image_for_type1(image: np.ndarray) -> Dict[str, np.ndarray]:
    """Preprocess the image for Type 1 document OCR.

    Args:
        image: The input image

    Returns:
        Dictionary containing different versions of the preprocessed image
    """
    try:
        # Make a deep copy of the original image to avoid modifying it
        original = image.copy()

        # Convert to grayscale if needed
        if len(original.shape) == 3:
            # Use high-quality grayscale conversion with proper weighting
            gray = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
        else:
            # Make a deep copy to avoid reference issues
            gray = original.copy()

        # Create only the essential versions of the image for OCR
        versions = {
            'original': original
        }

        # Create inverted version (often better for OCR)
        inverted = cv2.bitwise_not(gray)
        versions['inverted'] = inverted

        # Return only the essential versions for later processing
        return versions

    except Exception as e:
        logger.error(f"Error preprocessing image: {e}")
        # If an error occurs, return the original image as a fallback
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return {
                'original': image.copy(),  # Use copy to ensure no reference issues
                'inverted': cv2.bitwise_not(gray)
            }
        else:
            return {
                'original': image.copy(),  # Use copy to ensure no reference issues
                'inverted': cv2.bitwise_not(image.copy())
            }

def perform_ocr_for_type1(image_dict: Dict, page_num: int) -> Dict:
    """Perform OCR on the image for Type 1 documents.

    Args:
        image_dict: Dictionary containing different versions of the preprocessed image
        page_num: The page number for logging

    Returns:
        Dictionary with OCR results
    """
    try:
        # Use multiple PSM modes and image versions to get the best results
        ocr_results = []

        # Only use the most effective versions for OCR
        for version_name in ['original', 'inverted']:
            # Skip if this version is not available
            if version_name not in image_dict:
                continue

            img = image_dict[version_name]

            # PSM 6: Assume a single uniform block of text (best for most cases)
            custom_config = "--psm 6 --oem 3 -l eng+fra --dpi 300"
            result_psm6 = pytesseract.image_to_data(img, output_type=pytesseract.Output.DICT, config=custom_config)
            ocr_results.append((f"{version_name}_psm6", result_psm6))

            # PSM 4: Assume a single column of text (good for columnar data)
            custom_config = "--psm 4 --oem 3 -l eng+fra --dpi 300"
            result_psm4 = pytesseract.image_to_data(img, output_type=pytesseract.Output.DICT, config=custom_config)
            ocr_results.append((f"{version_name}_psm4", result_psm4))

        # Combine results from different PSM modes and image versions
        combined_result = {
            'text': [],
            'conf': [],
            'left': [],
            'top': [],
            'width': [],
            'height': [],
            'line_num': [],
            'block_num': [],
            'page_num': []
        }

        for version_name, result_dict in ocr_results:
            for i in range(len(result_dict['text'])):
                text = result_dict['text'][i]
                conf = int(result_dict['conf'][i])

                # Skip empty or low confidence text
                if not text.strip() or conf < 30:
                    continue

                combined_result['text'].append(text)
                combined_result['conf'].append(conf)
                combined_result['left'].append(result_dict['left'][i])
                combined_result['top'].append(result_dict['top'][i])
                combined_result['width'].append(result_dict['width'][i])
                combined_result['height'].append(result_dict['height'][i])
                combined_result['line_num'].append(result_dict['line_num'][i])
                combined_result['block_num'].append(result_dict['block_num'][i])
                combined_result['page_num'].append(page_num)

        return combined_result

    except Exception as e:
        logger.error(f"Error performing OCR on page {page_num}: {e}")
        return {
            'text': [],
            'conf': [],
            'left': [],
            'top': [],
            'width': [],
            'height': [],
            'line_num': [],
            'block_num': [],
            'page_num': []
        }

def detect_white_separator_spaces(image: np.ndarray) -> List[int]:
    """Detect white separator spaces between columns in the image.

    This function analyzes the image to find vertical white spaces that separate columns.

    Args:
        image: The input image

    Returns:
        List of x-coordinates representing the middle of white separator spaces
    """
    try:
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Get image dimensions
        height, width = gray.shape

        # Apply thresholding to get binary image (text is black, background is white)
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Calculate vertical projection (sum of black pixels in each column)
        v_projection = np.sum(binary, axis=0) / 255

        # Normalize projection
        if np.max(v_projection) > 0:
            v_projection = v_projection / np.max(v_projection)

        # Apply smoothing to reduce noise
        v_projection_smooth = np.convolve(v_projection, np.ones(10)/10, mode='same')

        # Save the projection profile for debugging
        plt.figure(figsize=(10, 4))
        plt.plot(v_projection_smooth)
        plt.title('Vertical Projection Profile')
        plt.xlabel('Column')
        plt.ylabel('Normalized Density')
        plt.savefig('vertical_projection.png')
        plt.close()

        # Calculate the ideal positions for 2 white spaces (at 1/3 and 2/3 of the image width)
        ideal_pos1 = width / 3
        ideal_pos2 = 2 * width / 3

        # Define search windows around the ideal positions (20% of width)
        window_size = width * 0.2
        window1_start = max(0, int(ideal_pos1 - window_size/2))
        window1_end = min(width, int(ideal_pos1 + window_size/2))
        window2_start = max(0, int(ideal_pos2 - window_size/2))
        window2_end = min(width, int(ideal_pos2 + window_size/2))

        # Find the minimum density point in each window
        min_val1 = 1.0
        min_pos1 = window1_start
        for i in range(window1_start, window1_end):
            if v_projection_smooth[i] < min_val1:
                min_val1 = v_projection_smooth[i]
                min_pos1 = i

        min_val2 = 1.0
        min_pos2 = window2_start
        for i in range(window2_start, window2_end):
            if v_projection_smooth[i] < min_val2:
                min_val2 = v_projection_smooth[i]
                min_pos2 = i

        # Check if the minimum values are below a threshold (indicating a white space)
        white_spaces = []
        threshold = 0.3  # Adjust this threshold as needed

        if min_val1 < threshold:
            white_spaces.append(min_pos1)

        if min_val2 < threshold:
            white_spaces.append(min_pos2)

        # If we couldn't find white spaces using the threshold, just use the minimum points
        if not white_spaces:
            white_spaces = [min_pos1, min_pos2]

        # Sort white spaces by position
        white_spaces.sort()

        # Log the detected white spaces
        logger.info(f"Detected {len(white_spaces)} white separator spaces at positions: {white_spaces}")

        return white_spaces

    except Exception as e:
        logger.error(f"Error detecting white separator spaces: {e}")
        return []

def analyze_text_layout_type1(ocr_results: Dict, image_width: int, page_num: int) -> Tuple[List[Dict], List[Tuple[int, int]]]:
    """Analyze text layout to identify text blocks for Type 1 documents.

    This function groups text into lines and classifies text as names or identifiers
    based on horizontal position within a sub-table.

    Args:
        ocr_results: The OCR results from pytesseract for a single sub-table
        image_width: The width of the sub-table image
        page_num: The page number for logging

    Returns:
        Tuple containing:
        - List of text blocks with position, content, and classification
        - Empty list for sub-table boundaries (no longer used in this context)
    """
    try:
        # Extract text blocks from OCR results
        text_blocks = []

        # First, collect all the lines
        lines = []
        for i in range(len(ocr_results['text'])):
            text = ocr_results['text'][i]
            conf = ocr_results['conf'][i]
            line_num = ocr_results['line_num'][i]
            block_num = ocr_results['block_num'][i]

            # Skip empty or low confidence text
            if not text.strip() or conf < 30:
                continue

            # Get bounding box
            left = ocr_results['left'][i]
            top = ocr_results['top'][i]
            width = ocr_results['width'][i]
            height = ocr_results['height'][i]

            # Calculate center coordinates
            center_x = left + width // 2
            center_y = top + height // 2

            # Add to lines
            lines.append({
                'text': text,
                'conf': conf,
                'bbox': (left, top, width, height),
                'center_x': center_x,
                'center_y': center_y,
                'line_num': line_num,
                'block_num': block_num,
                'page_num': page_num
            })

        # Group by line number
        line_groups = {}
        for line in lines:
            line_num = line['line_num']
            if line_num not in line_groups:
                line_groups[line_num] = []
            line_groups[line_num].append(line)

        # Process each line group
        for line_num, line_group in line_groups.items():
            # Sort by x-coordinate
            line_group.sort(key=lambda x: x['center_x'])

            # Check if this is a header line
            is_header = False
            line_text = ' '.join([item['text'] for item in line_group])
            if "Nom et prénom" in line_text or "Identifiant" in line_text:
                is_header = True

            if is_header:
                # Skip header lines
                continue

            # Try to split the line into name and identifier
            if len(line_group) == 1 and ' ' in line_group[0]['text']:
                # If there's only one text block but it contains a space,
                # try to split it into name and identifier
                full_text = line_group[0]['text']

                # Find the position of the identifier (usually starts with a special character or number)
                identifier_start = -1
                for i, char in enumerate(full_text):
                    if char in ['*', '#', '+', '-', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9']:
                        # Check if this is part of a name (e.g., "BEN-AHMED")
                        if i > 0 and full_text[i-1] == '-':
                            continue
                        identifier_start = i
                        break

                if identifier_start > 0:
                    # Split the text into name and identifier
                    name_text = full_text[:identifier_start].strip()
                    identifier_text = full_text[identifier_start:].strip()

                    # Create name block
                    name_block = line_group[0].copy()
                    name_block['text'] = name_text
                    name_block['is_name'] = True
                    name_block['is_identifier'] = False

                    # Create identifier block
                    identifier_block = line_group[0].copy()
                    identifier_block['text'] = identifier_text
                    identifier_block['is_name'] = False
                    identifier_block['is_identifier'] = True

                    # Add to text blocks
                    text_blocks.append(name_block)
                    text_blocks.append(identifier_block)
                else:
                    # If we couldn't find a good split point, just add the whole text as a name
                    name_block = line_group[0].copy()
                    name_block['is_name'] = True
                    name_block['is_identifier'] = False
                    text_blocks.append(name_block)
            else:
                # If there are multiple text blocks in the line, we need to be more careful about how we group them
                # Instead of combining all blocks, we'll try to identify which ones are names and which are identifiers

                # First, check if any block contains a pattern typical of identifiers
                identifier_blocks = []
                name_blocks = []

                for block in line_group:
                    text = block['text']
                    # Check if the text contains patterns typical of identifiers
                    if any(char in text for char in ['*', '#', '+', '-']) or text.isdigit():
                        identifier_blocks.append(block)
                    else:
                        name_blocks.append(block)

                # If we couldn't clearly separate, use position-based approach as fallback
                if not identifier_blocks and not name_blocks:
                    # Use the middle of the image as boundary
                    boundary_x = image_width // 2

                    for block in line_group:
                        if block['center_x'] < boundary_x:
                            name_blocks.append(block)
                        else:
                            identifier_blocks.append(block)

                # Create name block if we have name blocks
                if name_blocks:
                    # Sort name blocks by x-coordinate to maintain proper order
                    name_blocks.sort(key=lambda x: x['center_x'])

                    # Use the first name block as base and update its text
                    name_block = name_blocks[0].copy()
                    name_block['text'] = ' '.join([block['text'] for block in name_blocks])
                    name_block['is_name'] = True
                    name_block['is_identifier'] = False
                    text_blocks.append(name_block)

                # Create identifier block if we have identifier blocks
                if identifier_blocks:
                    # Sort identifier blocks by x-coordinate to maintain proper order
                    identifier_blocks.sort(key=lambda x: x['center_x'])

                    # Use the first identifier block as base and update its text
                    identifier_block = identifier_blocks[0].copy()
                    identifier_block['text'] = ' '.join([block['text'] for block in identifier_blocks])
                    identifier_block['is_name'] = False
                    identifier_block['is_identifier'] = True
                    text_blocks.append(identifier_block)

                # If we didn't find any name or identifier blocks, fall back to the old approach
                if not name_blocks and not identifier_blocks:
                    mid_point = len(line_group) // 2

                    # Combine name blocks
                    name_text = ' '.join([item['text'] for item in line_group[:mid_point]])
                    name_block = line_group[0].copy()
                    name_block['text'] = name_text
                    name_block['is_name'] = True
                    name_block['is_identifier'] = False
                    text_blocks.append(name_block)

                    # Combine identifier blocks if any
                    if line_group[mid_point:]:
                        identifier_text = ' '.join([item['text'] for item in line_group[mid_point:]])
                        identifier_block = line_group[mid_point].copy()
                        identifier_block['text'] = identifier_text
                        identifier_block['is_name'] = False
                        identifier_block['is_identifier'] = True
                        text_blocks.append(identifier_block)

        # Find the natural column boundary within this sub-table
        # Calculate the average x-coordinate of all blocks
        x_coords = [block['center_x'] for block in text_blocks]
        if not x_coords:
            return text_blocks, []

        # Find the natural division between columns
        # We'll use a simple approach: find the largest gap in x-coordinates
        x_coords.sort()
        max_gap = 0
        boundary_x = 0

        for i in range(1, len(x_coords)):
            gap = x_coords[i] - x_coords[i-1]
            if gap > max_gap:
                max_gap = gap
                boundary_x = (x_coords[i] + x_coords[i-1]) // 2

        # If we couldn't find a good boundary, use the middle of the image
        if boundary_x == 0:
            # Get the min and max x-coordinates for all blocks
            min_x = min(block['bbox'][0] for block in text_blocks)
            max_x = max(block['bbox'][0] + block['bbox'][2] for block in text_blocks)
            boundary_x = (min_x + max_x) // 2

        logger.info(f"Found natural column boundary at x={boundary_x}")

        # Classify blocks based on this boundary
        for block in text_blocks:
            center_x = block['center_x']
            if center_x < boundary_x:
                block['is_name'] = True
                block['is_identifier'] = False
            else:
                block['is_name'] = False
                block['is_identifier'] = True

        return text_blocks, []

    except Exception as e:
        logger.error(f"Error analyzing text layout on page {page_num}: {e}")
        return [], []

def crop_image_into_sub_tables(image: np.ndarray, sub_table_boundaries: List[Tuple[int, int]], page_num: int, pre_ocr_tables_dir: Optional[Path] = None) -> List[np.ndarray]:
    """Crop the image into sub-tables based on the detected boundaries.

    Args:
        image: The original image
        sub_table_boundaries: List of (left_x, right_x) tuples defining sub-table boundaries
        page_num: The page number for logging
        pre_ocr_tables_dir: Directory to save pre-OCR table images (optional)

    Returns:
        List of cropped sub-table images
    """
    try:
        # Get image dimensions
        height, width = image.shape[:2]

        # Crop the image into sub-tables
        sub_table_images = []

        for i, (left_x, right_x) in enumerate(sub_table_boundaries, 1):
            # Ensure we have valid coordinates
            left_x = max(0, int(left_x))
            right_x = min(width, int(right_x))

            # Crop the image with high quality
            # Use a deep copy to ensure no reference issues and maintain full quality
            sub_table_img = np.array(image[:, left_x:right_x], copy=True)

            # Create only the essential versions for OCR
            # 1. Convert to grayscale if it's a color image
            if len(sub_table_img.shape) == 3:
                gray = cv2.cvtColor(sub_table_img, cv2.COLOR_BGR2GRAY)
            else:
                gray = sub_table_img.copy()

            # 2. Create an inverted version (often better for OCR)
            inverted = cv2.bitwise_not(gray)

            # Save only the essential versions of the cropped image if a directory is provided
            if pre_ocr_tables_dir:
                pre_ocr_tables_dir.mkdir(parents=True, exist_ok=True)

                # Save the original cropped image with high quality
                original_path = pre_ocr_tables_dir / f"page{page_num}_type1_sub_table_{i}_cropped.png"
                cv2.imwrite(str(original_path), sub_table_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])  # Use highest quality
                logger.info(f"Saved original sub-table {i} image to {original_path}")

                # Save only the inverted version which often works best for OCR
                inverted_path = pre_ocr_tables_dir / f"page{page_num}_type1_sub_table_{i}_inverted.png"
                cv2.imwrite(str(inverted_path), inverted, [cv2.IMWRITE_PNG_COMPRESSION, 0])

            # Add only the essential versions to the result list
            sub_table_images.append({
                'original': sub_table_img,
                'inverted': inverted,
                'index': i
            })

        return sub_table_images

    except Exception as e:
        logger.error(f"Error cropping image into sub-tables: {e}")
        # Return the original image as a fallback
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        return [{
            'original': image,
            'inverted': cv2.bitwise_not(gray),
            'index': 0
        }]

def process_single_sub_table(sub_table_img_dict: Dict, page_num: int, sub_table_idx: int) -> List[Dict]:
    """Process a single sub-table and extract name-identifier pairs.

    This function handles the complete processing of a single sub-table:
    1. Performs OCR on the sub-table image
    2. Analyzes the text layout to identify names and identifiers
    3. Groups names and identifiers into pairs

    Args:
        sub_table_img_dict: Dictionary containing different versions of the sub-table image
        page_num: The page number
        sub_table_idx: The sub-table index

    Returns:
        List of dictionaries with extracted name-identifier pairs
    """
    try:
        # Perform OCR on this sub-table using all image versions
        sub_table_ocr_results = perform_ocr_for_type1(sub_table_img_dict, page_num)

        # Get the width from the original image
        img_width = sub_table_img_dict['original'].shape[1]

        # Analyze text layout for this sub-table
        sub_table_text_blocks, _ = analyze_text_layout_type1(sub_table_ocr_results, img_width, page_num)

        # Set the sub-table number for all blocks
        for block in sub_table_text_blocks:
            block['sub_table'] = sub_table_idx

        # Group names and identifiers into pairs
        return group_names_and_identifiers_type1(sub_table_text_blocks, page_num)

    except Exception as e:
        logger.error(f"Error processing sub-table {sub_table_idx} on page {page_num}: {e}")
        return []

def group_names_and_identifiers_type1(text_blocks: List[Dict], page_num: int) -> List[Dict]:
    """Group names and identifiers into pairs based on vertical alignment.

    Args:
        text_blocks: List of text blocks with position and classification
        page_num: The page number for logging

    Returns:
        List of name-identifier pairs
    """
    try:
        # Separate names and identifiers
        names = [block for block in text_blocks if block.get('is_name', False)]
        identifiers = [block for block in text_blocks if block.get('is_identifier', False)]

        # Sort by vertical position (top to bottom)
        names.sort(key=lambda x: x['center_y'])
        identifiers.sort(key=lambda x: x['center_y'])

        # Group names and identifiers based on vertical alignment
        name_id_pairs = []

        # Process each name
        for name_block in names:
            name_text = name_block['text']
            name_y = name_block['center_y']
            sub_table = name_block.get('sub_table', 0)

            # Find the closest identifier by vertical position
            closest_id_block = None
            min_distance = float('inf')

            for id_block in identifiers:
                # Only consider identifiers from the same sub-table
                if id_block.get('sub_table', 0) != sub_table:
                    continue

                id_y = id_block['center_y']
                distance = abs(id_y - name_y)

                if distance < min_distance:
                    min_distance = distance
                    closest_id_block = id_block

            # Create a pair
            pair = {
                'text': name_text,
                'identifier': closest_id_block['text'] if closest_id_block else "",
                'bbox': name_block['bbox'],
                'conf': name_block['conf'],
                'page_num': page_num,
                'sub_table': sub_table
            }

            name_id_pairs.append(pair)

        return name_id_pairs

    except Exception as e:
        logger.error(f"Error grouping names and identifiers on page {page_num}: {e}")
        return []
