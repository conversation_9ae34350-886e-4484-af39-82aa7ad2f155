"""Type 3 document post-processing functions for JortExtractor.

This module contains functions for cleaning, standardizing, and formatting
data extracted from Type 3 documents.

Type 3 documents are single column format with names listed one per line.
"""

import pandas as pd
import re
import logging
from typing import List, Dict, Optional, Union

logger = logging.getLogger(__name__)

def clean_type3_data(df: pd.DataFrame) -> pd.DataFrame:
    """Clean and standardize Type 3 extracted data.
    
    Args:
        df: DataFrame with extracted Type 3 data
        
    Returns:
        Cleaned DataFrame
    """
    try:
        if df.empty:
            logger.warning("Empty DataFrame provided for Type3 cleaning")
            return df
            
        logger.info(f"Cleaning Type3 data with {len(df)} rows")
        
        # Make a copy to avoid modifying the original
        cleaned_df = df.copy()
        
        # Clean the text column (names)
        if 'text' in cleaned_df.columns:
            cleaned_df['text'] = cleaned_df['text'].apply(clean_type3_name)
            
            # Remove empty names after cleaning
            cleaned_df = cleaned_df[cleaned_df['text'].str.strip() != '']
            
            # Remove duplicates while preserving order
            cleaned_df = cleaned_df.drop_duplicates(subset=['text'], keep='first')
        
        # Sort by page and line number if available
        sort_columns = []
        if 'page' in cleaned_df.columns:
            sort_columns.append('page')
        if 'line_number' in cleaned_df.columns:
            sort_columns.append('line_number')
            
        if sort_columns:
            cleaned_df = cleaned_df.sort_values(sort_columns)
            
        # Reset index
        cleaned_df = cleaned_df.reset_index(drop=True)
        
        logger.info(f"Type3 data cleaned: {len(cleaned_df)} rows remaining")
        return cleaned_df
        
    except Exception as e:
        logger.error(f"Error cleaning Type3 data: {e}")
        return df

def clean_type3_name(name: Union[str, float]) -> str:
    """Clean and standardize a single name from Type 3 document.
    
    Args:
        name: Raw name string or NaN value
        
    Returns:
        Cleaned name string
    """
    try:
        # Handle NaN values
        if pd.isna(name) or not name:
            return ""
            
        name = str(name).strip()
        
        if not name:
            return ""
        
        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', name)
        
        # Remove common OCR artifacts and unwanted characters
        cleaned = re.sub(r'[|_~`@#$%^&*()+=\[\]{}\\;:"<>?/]', '', cleaned)
        
        # Remove standalone numbers (likely page numbers or artifacts)
        if re.match(r'^\d+$', cleaned.strip()):
            return ""
        
        # Remove lines that are mostly punctuation
        if len(re.sub(r'[^\w\s]', '', cleaned)) < len(cleaned) * 0.5:
            return ""
        
        # Fix common OCR character mistakes for French text
        char_fixes = {
            '0': 'O',  # Zero to O in names
            '1': 'I',  # One to I in names (context dependent)
            '5': 'S',  # Five to S in names (context dependent)
            '8': 'B',  # Eight to B in names (context dependent)
        }
        
        # Apply character fixes only if the result looks like a name
        words = cleaned.split()
        fixed_words = []
        
        for word in words:
            # Only apply fixes to words that look like names (contain letters)
            if any(c.isalpha() for c in word):
                fixed_word = word
                for old_char, new_char in char_fixes.items():
                    # Only replace if it's at the beginning or middle of a word
                    # and the word contains other letters
                    if old_char in word and len(re.sub(r'[0-9]', '', word)) > 1:
                        fixed_word = fixed_word.replace(old_char, new_char)
                fixed_words.append(fixed_word)
            else:
                # Skip words that are only numbers or punctuation
                if any(c.isalpha() for c in word):
                    fixed_words.append(word)
        
        cleaned = ' '.join(fixed_words)
        
        # Proper case formatting for names
        cleaned = format_name_case(cleaned)
        
        # Remove any remaining non-printable characters
        cleaned = ''.join(char for char in cleaned if char.isprintable())
        
        # Final validation - must contain at least one letter
        if not any(c.isalpha() for c in cleaned):
            return ""
        
        # Must be reasonable length for a name
        if len(cleaned) < 2 or len(cleaned) > 100:
            return ""
        
        return cleaned.strip()
        
    except Exception as e:
        logger.error(f"Error cleaning Type3 name '{name}': {e}")
        return str(name).strip() if name else ""

def format_name_case(name: str) -> str:
    """Format name with proper capitalization.
    
    Args:
        name: Name string to format
        
    Returns:
        Properly formatted name
    """
    try:
        if not name:
            return ""
        
        words = name.split()
        formatted_words = []
        
        for word in words:
            if not word:
                continue
                
            # Handle special prefixes and particles
            lower_case_words = {
                'de', 'du', 'des', 'le', 'la', 'les', 'ben', 'ibn', 'el', 'al',
                'von', 'van', 'da', 'di', 'del', 'della', 'dello'
            }
            
            if word.lower() in lower_case_words:
                formatted_words.append(word.lower())
            else:
                # Capitalize first letter, keep rest as is (to preserve intentional casing)
                if len(word) > 1:
                    formatted_words.append(word[0].upper() + word[1:].lower())
                else:
                    formatted_words.append(word.upper())
        
        return ' '.join(formatted_words)
        
    except Exception as e:
        logger.error(f"Error formatting name case '{name}': {e}")
        return name

def format_type3_output(df: pd.DataFrame) -> pd.DataFrame:
    """Format Type 3 data for final output.
    
    Args:
        df: Cleaned DataFrame with Type 3 data
        
    Returns:
        Formatted DataFrame ready for output
    """
    try:
        if df.empty:
            logger.warning("Empty DataFrame provided for Type3 formatting")
            return pd.DataFrame(columns=['Nom et prénom'])
        
        logger.info(f"Formatting Type3 output with {len(df)} rows")
        
        # Create output DataFrame with standard column name
        output_df = pd.DataFrame()
        
        if 'text' in df.columns:
            output_df['Nom et prénom'] = df['text']
        else:
            logger.warning("No 'text' column found in Type3 data")
            output_df['Nom et prénom'] = ""
        
        # Add metadata columns if available
        metadata_columns = ['page', 'line_number', 'original_text']
        for col in metadata_columns:
            if col in df.columns:
                output_df[col] = df[col]
        
        # Remove any rows with empty names
        output_df = output_df[output_df['Nom et prénom'].str.strip() != '']
        
        # Sort by page and line number if available
        sort_columns = []
        if 'page' in output_df.columns:
            sort_columns.append('page')
        if 'line_number' in output_df.columns:
            sort_columns.append('line_number')
            
        if sort_columns:
            output_df = output_df.sort_values(sort_columns)
        
        # Reset index
        output_df = output_df.reset_index(drop=True)
        
        logger.info(f"Type3 output formatted: {len(output_df)} rows")
        return output_df
        
    except Exception as e:
        logger.error(f"Error formatting Type3 output: {e}")
        # Return minimal DataFrame on error
        return pd.DataFrame(columns=['Nom et prénom'])

def validate_type3_names(names: List[str]) -> List[str]:
    """Validate a list of Type 3 names and remove invalid entries.
    
    Args:
        names: List of name strings
        
    Returns:
        List of validated names
    """
    try:
        validated_names = []
        
        for name in names:
            cleaned_name = clean_type3_name(name)
            if cleaned_name:
                validated_names.append(cleaned_name)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_names = []
        for name in validated_names:
            if name.lower() not in seen:
                seen.add(name.lower())
                unique_names.append(name)
        
        logger.info(f"Validated {len(unique_names)} unique names from {len(names)} input names")
        return unique_names
        
    except Exception as e:
        logger.error(f"Error validating Type3 names: {e}")
        return []
