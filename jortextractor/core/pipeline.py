"""Enhanced PDF table extraction pipeline for Jort documents."""
import logging
import re
import os
import json
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Union
import numpy as np
import pandas as pd
from pdf2image import convert_from_path
import cv2
import pytesseract

# Import from utils package
from jortextractor.utils.config import load_config, validate_config
from jortextractor.utils.post_processor import filter_name_entries, clean_and_standardize_names

# Import from core package
from jortextractor.core.image_processor import (
    preprocess_image,
    detect_tables,
    extract_table_images,
    optimize_for_name_ocr
)
from jortextractor.core.name_validator import (
    validate_and_correct_name,
    normalize_text
)
from jortextractor.core.pipeline_type1_helpers import (
    detect_big_table_boundaries,
    preprocess_image_for_type1,
    perform_ocr_for_type1,
    detect_white_separator_spaces,
    analyze_text_layout_type1,
    crop_image_into_sub_tables,
    group_names_and_identifiers_type1,
    process_single_sub_table
)
from jortextractor.core.type1_postprocessing import clean_type1_data, format_type1_output, clean_name
from jortextractor.core.document_classifier import DocumentClassifier, DOCUMENT_TYPE1, DOCUMENT_TYPE2, DOCUMENT_TYPE3, DOCUMENT_TYPE4

class JortPDFProcessor:
    """Main class for processing Jort PDF documents to extract tables."""

    def __init__(self, config_path: Optional[str] = None):
        """Initialize the processor with configuration.

        Args:
            config_path: Path to the configuration file (optional)
        """
        self.logger = logging.getLogger(__name__)

        # Load configuration
        if config_path:
            self.config = load_config(config_path)
        else:
            self.config = {}

        # Set up temporary directories for debugging
        self.temp_dir = None
        if self.config.get('save_debug_images', False):
            temp_dir = self.config.get('debug_images_dir', 'temp_images')
            self.temp_dir = Path(temp_dir)
            self.temp_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Saving debug table images to: {self.temp_dir}")

        # Set up directories for pre-OCR images
        self.pre_ocr_tables_dir = None
        if self.config.get('save_pre_ocr_tables', False):
            pre_ocr_tables_dir = self.config.get('pre_ocr_tables_dir', 'pre-ocr-tables')
            self.pre_ocr_tables_dir = Path(pre_ocr_tables_dir)
            self.pre_ocr_tables_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Saving pre-OCR table images to: {self.pre_ocr_tables_dir}")

        self.pre_ocr_columns_dir = None
        if self.config.get('save_pre_ocr_columns', False):
            pre_ocr_columns_dir = self.config.get('pre_ocr_columns_dir', 'pre-ocr-columns')
            self.pre_ocr_columns_dir = Path(pre_ocr_columns_dir)
            self.pre_ocr_columns_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Saving pre-OCR column images to: {self.pre_ocr_columns_dir}")

        # Initialize document classifier
        self.document_classifier = DocumentClassifier()

    def _save_pre_ocr_table_image(self, image: np.ndarray, page_num: int, table_name: str, suffix: str = "") -> None:
        """Save a pre-OCR table image for debugging.

        Args:
            image: The image to save
            page_num: The page number
            table_name: The table name or identifier
            suffix: Optional suffix for the filename
        """
        if self.pre_ocr_tables_dir:
            filename = f"page{page_num}_{table_name}"
            if suffix:
                filename += f"_{suffix}"
            filename += ".png"

            filepath = self.pre_ocr_tables_dir / filename

            # Save with highest quality (no compression)
            cv2.imwrite(str(filepath), image, [cv2.IMWRITE_PNG_COMPRESSION, 0])

    def _save_pre_ocr_column_image(self, image: np.ndarray, page_num: int, table_name: str, column_idx: str, suffix: str = "") -> None:
        """Save a pre-OCR column image for debugging.

        Args:
            image: The image to save
            page_num: The page number
            table_name: The table name or identifier
            column_idx: The column index or identifier
            suffix: Optional suffix for the filename
        """
        if self.pre_ocr_columns_dir:
            filename = f"page{page_num}_{table_name}_column{column_idx}"
            if suffix:
                filename += f"_{suffix}"
            filename += ".png"

            filepath = self.pre_ocr_columns_dir / filename

            # Save with highest quality (no compression)
            cv2.imwrite(str(filepath), image, [cv2.IMWRITE_PNG_COMPRESSION, 0])

    def process_pdf(self, pdf_path: Union[str, Path], document_type: Optional[str] = None, ground_truth_path: Optional[Union[str, Path]] = None, test_ocr: bool = False) -> Dict[str, Union[pd.DataFrame, dict, str]]:
        """Process a PDF document to extract tables.

        Args:
            pdf_path: Path to the PDF file
            document_type: Type of document (optional, will be auto-detected if not provided)
            ground_truth_path: Path to ground truth CSV for comparison (optional)
            test_ocr: Whether to test OCR accuracy (optional)

        Returns:
            Dictionary with extracted data and processing status
        """
        try:
            # Convert paths to Path objects
            if isinstance(pdf_path, str):
                pdf_path = Path(pdf_path)

            self.logger.info(f"Processing {pdf_path}")

            # Determine document type
            if document_type:
                self.logger.info(f"Using document type: {document_type}")
            else:
                # Auto-detect document type
                document_type = self.document_classifier.classify_document(pdf_path)
                self.logger.info(f"Detected document type: {document_type}")

            # Process based on document type
            if document_type == DOCUMENT_TYPE1:
                self.logger.info(f"Using specialized processing for Type1 document.")
                return self._process_type1_pdf(pdf_path, ground_truth_path, test_ocr)
            elif document_type == DOCUMENT_TYPE2:
                self.logger.info(f"Using specialized processing for Type2 document.")
                # Fallback to default processing for Type2 documents
                self.logger.info(f"Type2 specialized processing not implemented yet, using default processing.")
                return self._process_default_pdf(pdf_path, ground_truth_path, test_ocr)
            elif document_type == DOCUMENT_TYPE4:
                self.logger.info(f"Using specialized processing for Type4 document.")
                # Fallback to default processing for Type4 documents
                self.logger.info(f"Type4 specialized processing not implemented yet, using default processing.")
                return self._process_default_pdf(pdf_path, ground_truth_path, test_ocr)
            else:
                # Default processing for other document types
                self.logger.info(f"Using default processing for {document_type} document.")
                return self._process_default_pdf(pdf_path, ground_truth_path, test_ocr)

        except Exception as e:
            self.logger.error(f"Error processing PDF: {e}")
            return {'status': 'error', 'message': str(e)}

    def _compare_with_ground_truth(self, df_extracted: pd.DataFrame, ground_truth_path: Union[str, Path]) -> Dict:
        """Compare extracted data with ground truth.

        Args:
            df_extracted: DataFrame with extracted data
            ground_truth_path: Path to ground truth CSV

        Returns:
            Dictionary with comparison statistics
        """
        # Convert to Path object if it's a string
        if isinstance(ground_truth_path, str):
            ground_truth_path = Path(ground_truth_path)
        try:
            # Read ground truth data
            if "type1" in ground_truth_path.name:
                df_truth = pd.read_csv(ground_truth_path, header=0)
                # Check for different possible column names in type1 ground truth
                name_column = None
                for possible_name in ["Nom et prénom", "name", "Name", "NOM"]:
                    if possible_name in df_truth.columns:
                        name_column = possible_name
                        break

                if name_column is None:
                    # If no specific name column is found, use the first column
                    name_column = df_truth.columns[0]
                    self.logger.warning(f"No standard name column found in ground truth, using first column: {name_column}")

                # For Type 1 documents, check if we're using the new format with 'text' column
                extracted_column = 'text' if 'text' in df_extracted.columns else 'Nom et prénom'

                # If we're using the new format, rename the column to match the ground truth
                if extracted_column == 'text' and name_column != 'text':
                    df_extracted = df_extracted.rename(columns={extracted_column: name_column})
                    extracted_column = name_column
            else:
                # For other document types, use the first column of the ground truth
                df_truth = pd.read_csv(ground_truth_path, header=0)
                # Use the first column of the ground truth
                name_column = df_truth.columns[0]
                # Use the 'text' column from extracted data if it exists, otherwise use the first column
                extracted_column = 'text' if 'text' in df_extracted.columns else df_extracted.columns[0]

            # Get the list of names from both dataframes
            truth_names = df_truth[name_column].dropna().tolist()
            extracted_names = df_extracted[extracted_column].dropna().tolist()

            # Debug logging
            self.logger.info(f"Ground truth column: {name_column}, Extracted column: {extracted_column}")
            self.logger.info(f"Ground truth names count: {len(truth_names)}, Extracted names count: {len(extracted_names)}")
            self.logger.info(f"First 5 ground truth names: {truth_names[:5]}")
            self.logger.info(f"First 5 extracted names: {extracted_names[:5]}")

            # Normalize names for comparison
            normalized_truth = [normalize_text(name) for name in truth_names]
            normalized_extracted = [normalize_text(name) for name in extracted_names]

            # Debug logging for normalized names
            self.logger.info(f"First 5 normalized ground truth names: {normalized_truth[:5]}")
            self.logger.info(f"First 5 normalized extracted names: {normalized_extracted[:5]}")

            # Calculate exact matches
            exact_matches = set(normalized_truth).intersection(set(normalized_extracted))
            num_exact_matches = len(exact_matches)

            # Calculate fuzzy matches for names that weren't exact matches
            fuzzy_matches = []
            missed_names = []

            # Calculate Levenshtein distance for each pair of names
            import Levenshtein

            # For each ground truth name that wasn't an exact match
            for truth_idx, truth_name in enumerate(normalized_truth):
                if truth_name in exact_matches:
                    continue

                # Find the closest match in the extracted names
                best_match = None
                best_distance = float('inf')

                for extracted_name in normalized_extracted:
                    distance = Levenshtein.distance(truth_name, extracted_name)

                    # If this is a better match than what we've seen so far
                    if distance < best_distance:
                        best_distance = distance
                        best_match = extracted_name

                # If the best match is close enough (within 30% of the name length)
                max_distance = max(3, int(len(truth_name) * 0.3))
                if best_match and best_distance <= max_distance:
                    fuzzy_matches.append((truth_name, best_match, best_distance))
                else:
                    missed_names.append(truth_name)

            # Calculate statistics
            num_fuzzy_matches = len(fuzzy_matches)
            num_matched = num_exact_matches + num_fuzzy_matches
            num_missed = len(truth_names) - num_matched
            num_false_positives = len(extracted_names) - num_matched

            # Calculate precision, recall, and F1 score
            precision = num_matched / len(extracted_names) if extracted_names else 0
            recall = num_matched / len(truth_names) if truth_names else 0
            f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

            # Calculate average character accuracy
            total_chars = sum(len(name) for name in truth_names)
            total_errors = sum(match[2] for match in fuzzy_matches) + sum(len(name) for name in missed_names)
            char_accuracy = 1 - (total_errors / total_chars) if total_chars > 0 else 0

            # Count correctable matches (1-2 character errors)
            correctable_matches = sum(1 for _, _, dist in fuzzy_matches if dist <= 2)
            potential_recall = (num_matched + correctable_matches) / len(truth_names) if truth_names else 0

            # Prepare examples of near matches and false positives
            near_match_examples = []
            for truth_name, extracted_name, distance in sorted(fuzzy_matches, key=lambda x: x[2])[:5]:
                # Get the original (non-normalized) names
                orig_truth = truth_names[normalized_truth.index(truth_name)]
                orig_extracted = extracted_names[normalized_extracted.index(extracted_name)]
                near_match_examples.append(f"'{orig_extracted}' → '{orig_truth}' (Levenshtein: {distance})")

            false_positive_examples = []
            for extracted_name in normalized_extracted:
                if extracted_name not in normalized_truth and extracted_name not in [match[1] for match in fuzzy_matches]:
                    # Get the original (non-normalized) name
                    orig_extracted = extracted_names[normalized_extracted.index(extracted_name)]
                    false_positive_examples.append(f"'{orig_extracted}'")
                    if len(false_positive_examples) >= 5:
                        break

            # Prepare the comparison result
            comparison_result = {
                'precision': round(precision * 100, 1),
                'recall': round(recall * 100, 1),
                'f1_score': round(f1_score * 100, 1),
                'matched': f"{num_matched}/{len(extracted_names)} extracted",
                'exact_matches': num_exact_matches,
                'fuzzy_matches': num_fuzzy_matches,
                'ground_truth_total': len(truth_names),
                'missed_names': num_missed,
                'false_positives': num_false_positives,
                'char_accuracy': round(char_accuracy * 100, 1),
                'correctable_matches': correctable_matches,
                'potential_recall': round(potential_recall * 100, 1),
                'near_match_examples': near_match_examples,
                'false_positive_examples': false_positive_examples
            }

            return comparison_result

        except Exception as e:
            self.logger.error(f"Error comparing with ground truth: {e}")
            return {
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'matched': "0/0",
                'error': str(e)
            }

    def _process_type1_pdf(self, pdf_path: Path, ground_truth_path: Optional[Path] = None, test_ocr: bool = False) -> Dict[str, Union[pd.DataFrame, dict, str]]:
        """Process a PDF in type1 format (two columns with hidden table separator).

        Type 1 processing pipeline:
        1. Detect the big table boundaries in each page
        2. Preprocess the cropped big table image to enhance text visibility
        3. Detect white separator spaces to define sub-table boundaries
        4. Crop the big table image into sub-tables
        5. Perform OCR on each cropped sub-table separately
        6. Group names and identifiers into pairs
        7. Clean and standardize the data
        8. Format the output to match the ground truth format

        Args:
            pdf_path: Path to the PDF file
            ground_truth_path: Path to ground truth CSV for comparison (optional)
            test_ocr: Whether to test OCR accuracy (optional)

        Returns:
            Dictionary with extracted data and processing status
        """
        try:
            # Convert PDF to images with high quality settings
            dpi = self.config.get('dpi', 300)
            self.logger.info(f"Converting PDF to images with DPI={dpi}")
            images = convert_from_path(
                pdf_path,
                dpi=dpi,
                thread_count=4,  # Use multiple threads for faster processing
                use_cropbox=True,  # Use cropbox instead of mediabox for more accurate dimensions
                strict=False  # Continue even if there are minor errors in the PDF
            )

            # Process each page
            extracted_data_for_df = []

            for page_num, image in enumerate(images, 1):
                img_np = np.array(image)
                img_cv = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)

                # Step 1: Detect the big table boundaries in the page
                self.logger.info(f"Detecting big table boundaries on page {page_num}")
                x, y, x_max, y_max = detect_big_table_boundaries(img_cv)
                w = x_max - x
                h = y_max - y
                self.logger.info(f"Detected big table at x={x}, y={y}, width={w}, height={h}")

                # Crop the big table from the page
                big_table_img = img_cv[y:y+h, x:x+w]

                if self.pre_ocr_tables_dir:
                    self._save_pre_ocr_table_image(big_table_img, page_num, "type1_big_table", "cropped")
                    # Save a visualization of the detected boundaries on the original image
                    boundary_vis = img_cv.copy()
                    cv2.rectangle(boundary_vis, (x, y), (x + w, y + h), (0, 255, 0), 3)
                    self._save_pre_ocr_table_image(boundary_vis, page_num, "type1_big_table", "boundaries")

                # Step 2: Preprocess the cropped big table image to enhance text visibility
                self.logger.info(f"Preprocessing page {page_num}")
                preprocessed_versions = preprocess_image_for_type1(big_table_img)

                # Only save the original high-quality image for further processing
                if self.pre_ocr_tables_dir:
                    self._save_pre_ocr_table_image(preprocessed_versions['original'], page_num, "type1_big_table", "cropped")

                # Use the original image for further processing to maintain quality
                preprocessed_img = preprocessed_versions['original']

                # Step 3: Detect white separator spaces to define sub-table boundaries
                self.logger.info(f"Detecting white separator spaces in the big table")
                white_spaces = detect_white_separator_spaces(preprocessed_img)

                # If we found exactly 2 white spaces, use them to define the 3 sub-table boundaries
                if len(white_spaces) == 2:
                    # Sort white spaces by position
                    white_spaces.sort()

                    # Define sub-table boundaries using the white spaces
                    # Each boundary is in the middle of the white space
                    sub_table_boundaries = [
                        (0, white_spaces[0]),                    # Sub-table 1: from left edge to first white space
                        (white_spaces[0], white_spaces[1]),      # Sub-table 2: between the two white spaces
                        (white_spaces[1], preprocessed_img.shape[1])  # Sub-table 3: from second white space to right edge
                    ]

                    self.logger.info(f"Using detected white spaces at positions {white_spaces} to define sub-table boundaries")
                elif len(white_spaces) > 2:
                    # If we found more than 2 white spaces, find the 2 that are most evenly distributed
                    # Ideally, they should divide the image into 3 roughly equal parts

                    # Calculate the ideal positions for 2 white spaces (at 1/3 and 2/3 of the image width)
                    image_width = preprocessed_img.shape[1]
                    ideal_pos1 = image_width / 3
                    ideal_pos2 = 2 * image_width / 3

                    # Find the white spaces closest to the ideal positions
                    best_spaces = []
                    for ideal_pos in [ideal_pos1, ideal_pos2]:
                        closest_space = min(white_spaces, key=lambda x: abs(x - ideal_pos))
                        best_spaces.append(closest_space)

                    # Ensure the spaces are unique and sorted
                    best_spaces = sorted(set(best_spaces))

                    if len(best_spaces) == 2:
                        # Define sub-table boundaries using the best white spaces
                        sub_table_boundaries = [
                            (0, best_spaces[0]),                    # Sub-table 1: from left edge to first white space
                            (best_spaces[0], best_spaces[1]),       # Sub-table 2: between the two white spaces
                            (best_spaces[1], preprocessed_img.shape[1])  # Sub-table 3: from second white space to right edge
                        ]

                        self.logger.info(f"Using best white spaces at positions {best_spaces} to define sub-table boundaries")
                else:
                    # If we couldn't find white spaces, use default boundaries
                    image_width = preprocessed_img.shape[1]
                    sub_table_boundaries = [
                        (0, image_width // 3),                    # Sub-table 1: from left edge to 1/3
                        (image_width // 3, 2 * image_width // 3), # Sub-table 2: from 1/3 to 2/3
                        (2 * image_width // 3, image_width)       # Sub-table 3: from 2/3 to right edge
                    ]
                    self.logger.info(f"Using default sub-table boundaries at positions {[b[1] for b in sub_table_boundaries[:-1]]}")

                # Step 4: Crop the big table image into sub-tables
                self.logger.info(f"Cropping big table into {len(sub_table_boundaries)} sub-tables")
                sub_table_images = crop_image_into_sub_tables(preprocessed_img, sub_table_boundaries, page_num, self.pre_ocr_tables_dir)

                # Step 5: Process each sub-table independently and sequentially
                self.logger.info(f"Processing {len(sub_table_images)} sub-tables sequentially")

                # Process each sub-table independently
                for sub_table_idx, sub_table_img_dict in enumerate(sub_table_images, 1):
                    self.logger.info(f"Processing sub-table {sub_table_idx}")

                    # Process this sub-table completely (OCR, layout analysis, grouping)
                    sub_table_pairs = process_single_sub_table(sub_table_img_dict, page_num, sub_table_idx)

                    # Add the extracted pairs to the result in the order they were processed
                    for pair_idx, pair in enumerate(sub_table_pairs, 1):
                        name = pair['text']
                        identifier = pair['identifier']
                        bbox = pair['bbox']
                        sub_table = pair.get('sub_table', 0)

                        # Add to extracted data
                        extracted_data_for_df.append({
                            'page': page_num,
                            'table_num': f'type1_sub_table_{sub_table}_pair_{pair_idx}',
                            'text': name,
                            'identifier': identifier,
                            'bbox': str(bbox),
                            'sub_table': sub_table
                        })

            # Create DataFrame from extracted data
            df_extracted = pd.DataFrame(extracted_data_for_df)

            # Clean and standardize the data
            if not df_extracted.empty:
                # Apply Type 1 specific post-processing
                df_extracted = clean_type1_data(df_extracted)

                # Format the output to match the ground truth format
                df_formatted = format_type1_output(df_extracted)
            else:
                df_formatted = df_extracted

            # Save to CSV
            output_filename = pdf_path.stem + '.csv'
            output_dir = Path(self.config.get('output_dir', 'output'))
            output_path = output_dir / output_filename

            # Ensure the output directory exists
            output_dir.mkdir(parents=True, exist_ok=True)

            # For Type1, we want to include the identifier column in the output
            # Save without headers as requested
            self.logger.info(f"Saving extracted data to {output_path}")
            df_formatted.to_csv(output_path, index=False, header=False)

            result = {
                'extracted_data': df_extracted,
                'formatted_data': df_formatted,
                'output_path': str(output_path),
                'status': 'success'
            }

            if ground_truth_path:
                try:
                    ground_truth_path_obj = Path(ground_truth_path) if isinstance(ground_truth_path, str) else ground_truth_path
                    if ground_truth_path_obj.exists():
                        self.logger.info(f"Comparing with ground truth: {ground_truth_path}")
                        try:
                            # Use the formatted DataFrame for comparison with ground truth
                            comparison_result = self._compare_with_ground_truth(df_formatted, ground_truth_path)
                            result = {**comparison_result, 'extracted_data': df_extracted, 'formatted_data': df_formatted, 'status': 'success'}

                            # If pre-OCR tables were saved and compare_with_ocr_space is enabled, process with OCR.space
                            if self.pre_ocr_tables_dir and self.config.get('compare_with_ocr_space', False):
                                # Find all the cropped sub-table images
                                sub_table_images = list(self.pre_ocr_tables_dir.glob(f"page*_type1_sub_table_*_cropped.png"))
                                if sub_table_images:
                                    self.logger.info(f"Processing with OCR.space using {len(sub_table_images)} sub-table images")

                                    # Process Type1 document with OCR.space
                                    ocr_space_extracted_data = []
                                    for page_num in range(1, len(images) + 1):
                                        # Get sub-table images for this page
                                        page_sub_table_images = [img for img in sub_table_images if f"page{page_num}_" in str(img)]

                                        # Sort the sub-table images by their index to ensure proper ordering
                                        page_sub_table_images.sort(key=lambda x: int(str(x).split('sub_table_')[1].split('_')[0]))

                                        if page_sub_table_images:
                                            # Process each sub-table independently and sequentially with OCR.space
                                            for sub_table_idx, img_path in enumerate(page_sub_table_images, 1):
                                                self.logger.info(f"Processing sub-table {sub_table_idx} with OCR.space: {img_path}")
                                                sub_table_data = self._process_single_sub_table_with_ocr_space(img_path, page_num, sub_table_idx)
                                                ocr_space_extracted_data.extend(sub_table_data)

                                    # Create DataFrame from OCR.space extracted data
                                    df_ocr_space = pd.DataFrame(ocr_space_extracted_data)

                                    # Clean and standardize the OCR.space data
                                    if not df_ocr_space.empty:
                                        # Apply Type 1 specific post-processing
                                        df_ocr_space = clean_type1_data(df_ocr_space)

                                        # Format the output to match the ground truth format
                                        df_ocr_space_formatted = format_type1_output(df_ocr_space)
                                    else:
                                        df_ocr_space_formatted = df_ocr_space

                                    # Compare OCR.space results with ground truth
                                    ocr_space_comparison = self._compare_with_ground_truth_ocr_space(df_ocr_space_formatted, ground_truth_path)

                                    # Save OCR.space results to CSV
                                    output_filename = pdf_path.stem + '_ocr_space.csv'
                                    output_dir = Path(self.config.get('output_dir', 'output'))
                                    ocr_space_output_path = output_dir / output_filename

                                    # Ensure the output directory exists
                                    output_dir.mkdir(parents=True, exist_ok=True)

                                    # Save OCR.space results without headers as requested
                                    self.logger.info(f"Saving OCR.space extracted data to {ocr_space_output_path}")
                                    df_ocr_space_formatted.to_csv(ocr_space_output_path, index=False, header=False)

                                    # Add OCR.space results to the result
                                    result['ocr_space_data'] = df_ocr_space
                                    result['ocr_space_formatted_data'] = df_ocr_space_formatted
                                    result['ocr_space_output_path'] = str(ocr_space_output_path)
                                    result['ocr_space_comparison'] = ocr_space_comparison
                        except Exception as e:
                            self.logger.error(f"Ground truth comparison error: {e}")
                            result['comparison_stats'] = {
                                'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0,
                                'total_extracted': len(df_extracted) if not df_extracted.empty else 0,
                                'total_truth': 0, 'matched': 0
                            }
                except Exception as e:
                    self.logger.error(f"Error checking ground truth path: {e}")

            return result

        except Exception as e:
            self.logger.error(f"Error processing type1 PDF {pdf_path}: {e}")
            return {'extracted_data': pd.DataFrame(), 'status': f'error: {str(e)}'}

    def _compare_with_ocr_space(self, image_paths: List[Path]) -> Dict[str, Union[float, int, List[Dict]]]:
        """Compare our OCR results with OCR.space for benchmarking purposes.

        This method is used solely for benchmarking and comparison. The OCR.space API
        is NEVER used as an alternative to our own OCR implementation in the main
        processing pipeline.

        Args:
            image_paths: List of paths to pre-OCR images

        Returns:
            Dictionary with comparison metrics and detailed results
        """
        try:
            # Import OCR.space client
            from jortextractor.benchmarks.ocr_space_client import OCRSpaceClient
            import time

            # Create OCR.space client
            ocr_space_client = OCRSpaceClient()

            # Initialize results
            detailed_results = []
            total_our_texts = 0
            total_ocr_space_texts = 0
            total_matched = 0

            # Process each image
            for img_path in image_paths:
                self.logger.info(f"Comparing OCR for image: {img_path}")

                # Get OCR.space result
                ocr_space_result = ocr_space_client.process_file(img_path)
                ocr_space_texts = ocr_space_client.extract_text(ocr_space_result)

                # Get our OCR result using our standard pipeline
                img = cv2.imread(str(img_path))

                # Apply our preprocessing pipeline
                preprocessed_versions = preprocess_image_for_type1(img)

                # Perform OCR on essential versions only
                all_our_texts = []
                # Only use the most effective versions for OCR
                for version_name in ['original', 'inverted']:
                    # Skip if this version is not available
                    if version_name not in preprocessed_versions:
                        continue

                    version_img = preprocessed_versions[version_name]

                    # Try different PSM modes
                    for psm_mode in [6, 4]:  # Limit to the most effective PSM modes
                        custom_config = f"--psm {psm_mode} --oem 3 -l eng+fra --dpi 300"
                        text = pytesseract.image_to_string(version_img, config=custom_config).strip()
                        lines = [line.strip() for line in text.split('\n') if line.strip()]
                        all_our_texts.extend(lines)

                # Remove duplicates
                our_texts = list(set(all_our_texts))

                # Normalize texts for comparison
                def normalize_text(text):
                    # Convert to uppercase
                    text = text.upper()
                    # Remove accents
                    import unicodedata
                    text = unicodedata.normalize('NFKD', text).encode('ASCII', 'ignore').decode('ASCII')
                    # Remove special characters and extra spaces
                    import re
                    text = re.sub(r'[^A-Z0-9]', ' ', text)
                    text = re.sub(r'\s+', ' ', text).strip()
                    return text

                our_texts_norm = [normalize_text(text) for text in our_texts]
                ocr_space_texts_norm = [normalize_text(text) for text in ocr_space_texts]

                # Find matches
                matched_texts = []
                matched = 0
                for i, our_text in enumerate(our_texts_norm):
                    if our_text in ocr_space_texts_norm:
                        matched += 1
                        matched_texts.append(our_texts[i])

                # Calculate precision and recall
                precision = matched / len(our_texts) if our_texts else 0
                recall = matched / len(ocr_space_texts) if ocr_space_texts else 0
                f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

                # Add to detailed results
                detailed_results.append({
                    'image_path': str(img_path),
                    'our_texts': our_texts,
                    'ocr_space_texts': ocr_space_texts,
                    'matched_texts': matched_texts,
                    'precision': round(precision * 100, 1),
                    'recall': round(recall * 100, 1),
                    'f1_score': round(f1_score * 100, 1),
                    'total_our': len(our_texts),
                    'total_ocr_space': len(ocr_space_texts),
                    'matched': matched
                })

                # Update totals
                total_our_texts += len(our_texts)
                total_ocr_space_texts += len(ocr_space_texts)
                total_matched += matched

                # Wait to avoid hitting API rate limits
                time.sleep(1)

            # Calculate overall metrics
            overall_precision = total_matched / total_our_texts if total_our_texts else 0
            overall_recall = total_matched / total_ocr_space_texts if total_ocr_space_texts else 0
            overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0

            # Return overall metrics and detailed results
            return {
                'precision': round(overall_precision * 100, 1),
                'recall': round(overall_recall * 100, 1),
                'f1_score': round(overall_f1 * 100, 1),
                'total_our': total_our_texts,
                'total_ocr_space': total_ocr_space_texts,
                'matched': total_matched,
                'detailed_results': detailed_results
            }

        except Exception as e:
            self.logger.error(f"Error comparing with OCR.space: {e}")
            return {
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'total_our': 0,
                'total_ocr_space': 0,
                'matched': 0,
                'detailed_results': []
            }

    def _process_single_sub_table_with_ocr_space(self, img_path: Path, page_num: int, sub_table_idx: int) -> List[Dict]:
        """Process a single Type1 document sub-table image with OCR.space.

        This method is used solely for benchmarking and comparison. The OCR.space API
        is NEVER used as an alternative to our own OCR implementation in the main
        processing pipeline.

        Args:
            img_path: Path to the pre-OCR sub-table image
            page_num: The page number
            sub_table_idx: The sub-table index

        Returns:
            List of dictionaries with extracted text and identifiers
        """
        try:
            # Import OCR.space client
            from jortextractor.benchmarks.ocr_space_client import OCRSpaceClient
            import time
            import re

            # Create OCR.space client
            ocr_space_client = OCRSpaceClient()

            # Initialize results
            extracted_data = []

            # Get OCR.space result
            ocr_space_result = ocr_space_client.process_file(img_path)
            if not ocr_space_result:
                self.logger.warning(f"OCR.space returned no result for {img_path}")
                return []

            # Save the raw OCR.space response to a file for debugging
            output_dir = Path("output/ocr_space_raw_responses")
            output_dir.mkdir(parents=True, exist_ok=True)
            output_file = output_dir / f"page{page_num}_sub_table_{sub_table_idx}_raw_response.json"
            with open(output_file, 'w') as f:
                json.dump(ocr_space_result, f, indent=2)
            self.logger.info(f"Saved raw OCR.space response to {output_file}")

            # Also save the ParsedText to a separate file for easier inspection
            if 'ParsedResults' in ocr_space_result and ocr_space_result['ParsedResults']:
                parsed_text_file = output_dir / f"page{page_num}_sub_table_{sub_table_idx}_parsed_text.txt"
                with open(parsed_text_file, 'w') as f:
                    for i, result in enumerate(ocr_space_result['ParsedResults']):
                        if 'ParsedText' in result:
                            f.write(f"=== Result {i+1} ===\n")
                            f.write(result['ParsedText'])
                            f.write("\n\n")
                self.logger.info(f"Saved parsed text to {parsed_text_file}")

            extracted_pairs = ocr_space_client.extract_text(ocr_space_result)
            if not extracted_pairs:
                self.logger.warning(f"OCR.space extracted no text from {img_path}")
                return []

            # Save the extracted pairs to a file for debugging
            extracted_pairs_file = output_dir / f"page{page_num}_sub_table_{sub_table_idx}_extracted_pairs.json"
            with open(extracted_pairs_file, 'w') as f:
                json.dump(extracted_pairs, f, indent=2)
            self.logger.info(f"Saved extracted pairs to {extracted_pairs_file}")

            # Convert the extracted pairs directly to the expected format
            extracted_data = []
            for pair_idx, pair in enumerate(extracted_pairs, 1):
                name = pair.get('text', '')
                identifier = pair.get('identifier', '')

                # Skip empty pairs
                if not name or not identifier:
                    continue

                # Add to extracted data
                extracted_data.append({
                    'page': page_num,
                    'table_num': f'type1_sub_table_{sub_table_idx}_pair_{pair_idx}',
                    'text': name,
                    'identifier': identifier,
                    'bbox': '(0, 0, 0, 0)',  # Placeholder for bbox
                    'sub_table': sub_table_idx
                })

            # Wait to avoid hitting API rate limits
            time.sleep(1)

            # Return the extracted data
            return extracted_data

        except Exception as e:
            self.logger.error(f"Error processing sub-table {sub_table_idx} with OCR.space: {e}")
            return []

    def _process_type1_with_ocr_space(self, sub_table_images: List[Path], page_num: int) -> List[Dict]:
        """Process Type1 document sub-table images with OCR.space.

        This method is used solely for benchmarking and comparison. The OCR.space API
        is NEVER used as an alternative to our own OCR implementation in the main
        processing pipeline.

        Args:
            sub_table_images: List of paths to pre-OCR sub-table images
            page_num: The page number

        Returns:
            List of dictionaries with extracted text and identifiers
        """
        try:
            # Initialize results
            extracted_data = []

            # Process each sub-table image
            for sub_table_idx, img_path in enumerate(sub_table_images, 1):
                sub_table_data = self._process_single_sub_table_with_ocr_space(img_path, page_num, sub_table_idx)
                extracted_data.extend(sub_table_data)

            # Add default column names if the data is empty
            if not extracted_data:
                self.logger.warning("No data extracted from OCR.space results, adding empty row with column names")
                extracted_data.append({
                    'page': page_num,
                    'table_num': 'empty',
                    'text': '',
                    'identifier': '',
                    'bbox': '(0, 0, 0, 0)',
                    'sub_table': 0
                })

            return extracted_data

        except Exception as e:
            self.logger.error(f"Error processing Type1 with OCR.space: {e}")
            # Return an empty list with the expected column structure
            return [{
                'page': page_num,
                'table_num': 'error',
                'text': '',
                'identifier': '',
                'bbox': '(0, 0, 0, 0)',
                'sub_table': 0
            }]

    def _compare_with_ground_truth_ocr_space(self, df_ocr_space: pd.DataFrame, ground_truth_path: Union[str, Path]) -> Dict:
        """Compare OCR.space extracted data with ground truth.

        Args:
            df_ocr_space: DataFrame with OCR.space extracted data
            ground_truth_path: Path to ground truth CSV

        Returns:
            Dictionary with comparison statistics
        """
        # This method is similar to _compare_with_ground_truth but specifically for OCR.space results
        # Convert to Path object if it's a string
        if isinstance(ground_truth_path, str):
            ground_truth_path = Path(ground_truth_path)
        try:
            # Check if the OCR.space DataFrame is empty
            if df_ocr_space.empty:
                self.logger.warning("OCR.space DataFrame is empty, cannot compare with ground truth")
                return {
                    'precision': 0.0,
                    'recall': 0.0,
                    'f1_score': 0.0,
                    'matched': "0/0",
                    'error': "OCR.space DataFrame is empty"
                }

            # Read ground truth data
            if "type1" in ground_truth_path.name:
                df_truth = pd.read_csv(ground_truth_path, header=0)
                # Check for different possible column names in type1 ground truth
                name_column = None
                for possible_name in ["Nom et prénom", "name", "Name", "NOM"]:
                    if possible_name in df_truth.columns:
                        name_column = possible_name
                        break

                if name_column is None:
                    # If no specific name column is found, use the first column
                    name_column = df_truth.columns[0]
                    self.logger.warning(f"No standard name column found in ground truth, using first column: {name_column}")

                # For Type 1 documents, check if we're using the new format with 'text' column
                extracted_column = 'text' if 'text' in df_ocr_space.columns else 'Nom et prénom'

                # If we're using the new format, rename the column to match the ground truth
                if extracted_column == 'text' and name_column != 'text':
                    df_ocr_space = df_ocr_space.rename(columns={extracted_column: name_column})
                    extracted_column = name_column
            else:
                # For other document types, assume the ground truth has a 'text' column
                df_truth = pd.read_csv(ground_truth_path, header=0)
                name_column = 'text'
                extracted_column = 'text'

            # Check if the extracted column exists in the OCR.space DataFrame
            if extracted_column not in df_ocr_space.columns:
                self.logger.warning(f"Column '{extracted_column}' not found in OCR.space DataFrame")
                return {
                    'precision': 0.0,
                    'recall': 0.0,
                    'f1_score': 0.0,
                    'matched': "0/0",
                    'error': f"Column '{extracted_column}' not found in OCR.space DataFrame"
                }

            # Get the list of names from both dataframes
            truth_names = df_truth[name_column].dropna().tolist()
            extracted_names = df_ocr_space[extracted_column].dropna().tolist()

            # Normalize names for comparison
            normalized_truth = [normalize_text(name) for name in truth_names]
            normalized_extracted = [normalize_text(name) for name in extracted_names]

            # Calculate exact matches
            exact_matches = set(normalized_truth).intersection(set(normalized_extracted))
            num_exact_matches = len(exact_matches)

            # Calculate fuzzy matches for names that weren't exact matches
            fuzzy_matches = []
            missed_names = []

            # Calculate Levenshtein distance for each pair of names
            import Levenshtein

            # For each ground truth name that wasn't an exact match
            for truth_idx, truth_name in enumerate(normalized_truth):
                if truth_name in exact_matches:
                    continue

                # Find the closest match in the extracted names
                best_match = None
                best_distance = float('inf')

                for extracted_name in normalized_extracted:
                    distance = Levenshtein.distance(truth_name, extracted_name)

                    # If this is a better match than what we've seen so far
                    if distance < best_distance:
                        best_distance = distance
                        best_match = extracted_name

                # If the best match is close enough (within 30% of the name length)
                max_distance = max(3, int(len(truth_name) * 0.3))
                if best_match and best_distance <= max_distance:
                    fuzzy_matches.append((truth_name, best_match, best_distance))
                else:
                    missed_names.append(truth_name)

            # Calculate statistics
            num_fuzzy_matches = len(fuzzy_matches)
            num_matched = num_exact_matches + num_fuzzy_matches
            num_missed = len(truth_names) - num_matched
            num_false_positives = len(extracted_names) - num_matched

            # Calculate precision, recall, and F1 score
            precision = num_matched / len(extracted_names) if extracted_names else 0
            recall = num_matched / len(truth_names) if truth_names else 0
            f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

            # Calculate average character accuracy
            total_chars = sum(len(name) for name in truth_names)
            total_errors = sum(match[2] for match in fuzzy_matches) + sum(len(name) for name in missed_names)
            char_accuracy = 1 - (total_errors / total_chars) if total_chars > 0 else 0

            # Count correctable matches (1-2 character errors)
            correctable_matches = sum(1 for _, _, dist in fuzzy_matches if dist <= 2)
            potential_recall = (num_matched + correctable_matches) / len(truth_names) if truth_names else 0

            # Prepare examples of near matches and false positives
            near_match_examples = []
            for truth_name, extracted_name, distance in sorted(fuzzy_matches, key=lambda x: x[2])[:5]:
                # Get the original (non-normalized) names
                orig_truth = truth_names[normalized_truth.index(truth_name)]
                orig_extracted = extracted_names[normalized_extracted.index(extracted_name)]
                near_match_examples.append(f"'{orig_extracted}' → '{orig_truth}' (Levenshtein: {distance})")

            false_positive_examples = []
            for extracted_name in normalized_extracted:
                if extracted_name not in normalized_truth and extracted_name not in [match[1] for match in fuzzy_matches]:
                    # Get the original (non-normalized) name
                    orig_extracted = extracted_names[normalized_extracted.index(extracted_name)]
                    false_positive_examples.append(f"'{orig_extracted}'")
                    if len(false_positive_examples) >= 5:
                        break

            # Prepare the comparison result
            comparison_result = {
                'precision': round(precision * 100, 1),
                'recall': round(recall * 100, 1),
                'f1_score': round(f1_score * 100, 1),
                'matched': f"{num_matched}/{len(extracted_names)} extracted",
                'exact_matches': num_exact_matches,
                'fuzzy_matches': num_fuzzy_matches,
                'ground_truth_total': len(truth_names),
                'missed_names': num_missed,
                'false_positives': num_false_positives,
                'char_accuracy': round(char_accuracy * 100, 1),
                'correctable_matches': correctable_matches,
                'potential_recall': round(potential_recall * 100, 1),
                'near_match_examples': near_match_examples,
                'false_positive_examples': false_positive_examples
            }

            return comparison_result

        except Exception as e:
            self.logger.error(f"Error comparing OCR.space with ground truth: {e}")
            return {
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'matched': "0/0",
                'error': str(e)
            }

    def _process_default_pdf(self, pdf_path: Path, ground_truth_path: Optional[Path] = None, test_ocr: bool = False) -> Dict[str, Union[pd.DataFrame, dict, str]]:
        """Process a PDF using the default pipeline.

        Args:
            pdf_path: Path to the PDF file
            ground_truth_path: Path to ground truth CSV for comparison (optional)
            test_ocr: Whether to test OCR accuracy (optional)

        Returns:
            Dictionary with extracted data and processing status
        """
        try:
            # Convert PDF to images
            images = convert_from_path(pdf_path, dpi=self.config.get('dpi', 300))

            # Process each page
            extracted_data_for_df = []

            for page_num, image in enumerate(images, 1):
                img_np = np.array(image)
                img_cv = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)

                # Preprocess the image
                preprocessed = preprocess_image(img_cv)

                # Detect tables
                tables = detect_tables(preprocessed)

                # Extract table images
                table_images = extract_table_images(preprocessed, tables)

                # Process each table
                for table_idx, table_image in enumerate(table_images, 1):
                    # Save pre-OCR image if enabled
                    if self.pre_ocr_tables_dir:
                        self._save_pre_ocr_table_image(table_image, page_num, f"table_{table_idx}", "original")

                    # Apply final optimization before OCR
                    optimized_img = optimize_for_name_ocr(table_image)

                    # Save optimized pre-OCR image if enabled
                    if self.pre_ocr_tables_dir:
                        self._save_pre_ocr_table_image(optimized_img, page_num, f"table_{table_idx}", "optimized")

                    # Perform OCR
                    custom_config = f"--psm 6 --oem 3 -l {self.config.get('ocr_languages','eng+fra')} --dpi 300"
                    ocr_result = pytesseract.image_to_string(optimized_img, config=custom_config)

                    # Process OCR result
                    lines = ocr_result.strip().split('\n')
                    for line in lines:
                        if not line.strip():
                            continue

                        # Validate and correct the name
                        name = validate_and_correct_name(line.strip())
                        if name:
                            extracted_data_for_df.append({
                                'page': page_num,
                                'table_num': f'table_{table_idx}',
                                'text': name,
                                'bbox': str((0, 0, 0, 0))  # Placeholder for bbox
                            })

            # Create DataFrame from extracted data
            df_extracted = pd.DataFrame(extracted_data_for_df)

            # Deduplicate entries based on text content
            if not df_extracted.empty and 'text' in df_extracted.columns:
                # Normalize text for deduplication
                df_extracted['normalized_text'] = df_extracted['text'].apply(normalize_text)

                # Keep track of seen texts to remove duplicates
                seen_texts = set()
                rows_to_keep = []

                for idx, row in df_extracted.iterrows():
                    if row['normalized_text'] not in seen_texts:
                        seen_texts.add(row['normalized_text'])
                        rows_to_keep.append(idx)

                # Filter to keep only unique entries
                df_extracted = df_extracted.loc[rows_to_keep]

                # Remove the temporary normalization column
                df_extracted = df_extracted.drop('normalized_text', axis=1)

                # Apply post-processing to improve precision while maintaining recall
                df_extracted = filter_name_entries(df_extracted)
                df_extracted = clean_and_standardize_names(df_extracted)

            # Save to CSV
            output_filename = pdf_path.stem + '.csv'
            output_dir = Path(self.config.get('output_dir', 'output'))
            output_path = output_dir / output_filename

            # Ensure the output directory exists
            output_dir.mkdir(parents=True, exist_ok=True)

            self.logger.info(f"Saving extracted data to {output_path}")
            df_extracted.to_csv(output_path, index=False)

            result = {
                'extracted_data': df_extracted,
                'output_path': str(output_path),
                'status': 'success'
            }

            if ground_truth_path:
                try:
                    ground_truth_path_obj = Path(ground_truth_path) if isinstance(ground_truth_path, str) else ground_truth_path
                    if ground_truth_path_obj.exists():
                        self.logger.info(f"Comparing with ground truth: {ground_truth_path}")
                        comparison_result = self._compare_with_ground_truth(df_extracted, ground_truth_path)
                        result = {**comparison_result, 'extracted_data': df_extracted, 'status': 'success'}
                except Exception as e:
                    self.logger.error(f"Error checking ground truth path: {e}")

            return result

        except Exception as e:
            self.logger.error(f"Error processing PDF: {e}")
            return {'status': 'error', 'message': str(e)}
