"""Enhanced image processing for OCR optimization."""
import cv2
import numpy as np
from typing import <PERSON><PERSON>, Optional, List

def enhance_image_for_ocr(image: np.ndarray) -> np.ndarray:
    """Apply a series of image processing techniques to optimize for OCR."""
    # Convert to grayscale if needed
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    # Apply bilateral filter to reduce noise while preserving edges
    denoised = cv2.bilateralFilter(gray, 9, 75, 75)

    # Apply adaptive histogram equalization for better contrast
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    enhanced = clahe.apply(denoised)

    # Apply adaptive thresholding
    binary = cv2.adaptiveThreshold(
        enhanced,
        255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY,
        11,
        2
    )

    # Apply morphological operations to clean up the image
    kernel = np.ones((1, 1), np.uint8)
    cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

    return cleaned

def sharpen_text(image: np.ndarray) -> np.ndarray:
    """Apply sharpening to enhance text edges."""
    # Create a sharpening kernel
    kernel = np.array([[-1, -1, -1],
                       [-1,  9, -1],
                       [-1, -1, -1]])

    # Apply the kernel
    sharpened = cv2.filter2D(image, -1, kernel)

    return sharpened

def deskew_image(image: np.ndarray) -> np.ndarray:
    """Deskew the image to straighten text lines."""
    # Convert to binary if not already
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    # Threshold the image
    thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]

    # Calculate skew angle
    coords = np.column_stack(np.where(thresh > 0))
    angle = cv2.minAreaRect(coords)[-1]

    # Adjust angle
    if angle < -45:
        angle = -(90 + angle)
    else:
        angle = -angle

    # Rotate the image to deskew it
    (h, w) = image.shape[:2]
    center = (w // 2, h // 2)
    M = cv2.getRotationMatrix2D(center, angle, 1.0)
    rotated = cv2.warpAffine(
        image,
        M,
        (w, h),
        flags=cv2.INTER_CUBIC,
        borderMode=cv2.BORDER_REPLICATE
    )

    return rotated

def remove_borders(image: np.ndarray, margin: int = 5) -> np.ndarray:
    """Remove black borders from the image."""
    # Convert to binary if not already
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    # Threshold the image
    _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # Find contours
    contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Find the largest contour
    if contours:
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)

        # Add margin
        x = max(0, x - margin)
        y = max(0, y - margin)
        w = min(gray.shape[1] - x, w + 2 * margin)
        h = min(gray.shape[0] - y, h + 2 * margin)

        # Crop the image
        cropped = image[y:y+h, x:x+w]
        return cropped

    return image

def optimize_for_name_ocr(image: np.ndarray) -> np.ndarray:
    """Apply a series of optimizations specifically for name OCR.

    Args:
        image: Input image as numpy array

    Returns:
        Optimized image as numpy array with the same number of channels as the input
    """
    # Check if the image is empty
    if image is None or image.size == 0:
        return np.zeros((100, 100), dtype=np.uint8)  # Return a small empty image

    # Store original image shape and type
    original_shape = image.shape
    is_color = len(original_shape) == 3

    # Convert to grayscale if needed
    if is_color:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    # Apply bilateral filter to reduce noise while preserving edges
    denoised = cv2.bilateralFilter(gray, 9, 75, 75)

    # Apply CLAHE for better contrast
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    enhanced = clahe.apply(denoised)

    # Sharpen the image to enhance text edges
    kernel = np.array([[-1, -1, -1],
                       [-1,  9, -1],
                       [-1, -1, -1]])
    sharpened = cv2.filter2D(enhanced, -1, kernel)

    # Apply Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(sharpened, (3, 3), 0)

    # Apply Otsu's thresholding for better binarization
    _, binary_otsu = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # Apply adaptive thresholding with more conservative parameters
    binary_adaptive = cv2.adaptiveThreshold(
        blurred,
        255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY,
        11,
        2
    )

    # Combine the two binary images (logical OR)
    binary = cv2.bitwise_or(binary_otsu, binary_adaptive)

    # Apply morphological operations to clean up the image
    kernel = np.ones((1, 1), np.uint8)
    cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

    # Dilate slightly to make text more prominent
    kernel = np.ones((1, 1), np.uint8)
    dilated = cv2.dilate(cleaned, kernel, iterations=1)

    # Convert back to color if the input was color
    if is_color:
        result = cv2.cvtColor(dilated, cv2.COLOR_GRAY2BGR)
    else:
        result = dilated

    return result

def detect_text_regions(image: np.ndarray) -> list:
    """Detect regions likely to contain text, focusing on table-like structures.

    Args:
        image: Input image as numpy array

    Returns:
        List of text regions as (x, y, width, height) tuples, prioritizing table-like structures
    """
    try:
        # Check if the image is empty or invalid
        if image is None or image.size == 0:
            return []

        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        height, width = gray.shape

        # Apply adaptive thresholding
        binary = cv2.adaptiveThreshold(
            gray,
            255,
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY_INV,
            11,
            2
        )

        # First, try to detect table-like structures using horizontal and vertical lines
        # This helps identify actual tables rather than general text regions

        # Detect horizontal lines
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (int(width/10), 1))
        detected_horizontal = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)

        # Detect vertical lines
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, int(height/10)))
        detected_vertical = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel, iterations=2)

        # Combine horizontal and vertical lines
        table_mask = cv2.add(detected_horizontal, detected_vertical)

        # Dilate to connect nearby lines
        kernel = np.ones((3, 3), np.uint8)
        table_mask = cv2.dilate(table_mask, kernel, iterations=2)

        # Find contours of potential tables
        table_contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Filter table contours by size
        table_regions = []
        for contour in table_contours:
            x, y, w, h = cv2.boundingRect(contour)
            # Tables are typically larger than small text blocks
            if w > width * 0.1 and h > height * 0.05:
                table_regions.append((x, y, w, h))

        # If we found table regions, prioritize those
        if table_regions:
            return table_regions

        # If no table structures were found, fall back to text region detection
        # Apply morphological operations to group text
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 3))
        dilated = cv2.dilate(binary, kernel, iterations=3)

        # Find contours
        contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Filter contours by size and position
        text_regions = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)

            # Skip very small regions
            if w < 50 or h < 10:
                continue

            # Skip regions at the very top of the page (likely titles)
            if y < height * 0.1 and w > width * 0.5:
                continue

            # Skip regions at the very bottom of the page (likely footers)
            if y > height * 0.9:
                continue

            # Skip very wide regions that span most of the page width (likely descriptions or headers)
            if w > width * 0.8 and h < height * 0.1:
                continue

            text_regions.append((x, y, w, h))

        # If no regions were found or very few, try a different approach for multi-column layouts
        if len(text_regions) < 5:
            try:
                # Try to detect columns
                columns = detect_columns(gray)
                if columns:
                    text_regions.extend(columns)
            except Exception as e:
                # If column detection fails, just continue with what we have
                print(f"Error in column detection: {e}")

        return text_regions

    except Exception as e:
        # If anything goes wrong, return an empty list
        print(f"Error in detect_text_regions: {e}")
        return []

def detect_columns(gray_image: np.ndarray) -> list:
    """Detect columns in a document image.

    Args:
        gray_image: Grayscale image as numpy array

    Returns:
        List of column regions as (x, y, width, height) tuples
    """
    try:
        # Check if the image is empty or invalid
        if gray_image is None or gray_image.size == 0:
            return []

        height, width = gray_image.shape

        # Apply Otsu's thresholding
        _, binary = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Create a vertical projection profile
        vertical_projection = np.sum(binary, axis=0)

        # Use a simple moving average instead of GaussianBlur to avoid OpenCV errors
        kernel_size = width // 100
        kernel_size = max(3, kernel_size)
        kernel_size = kernel_size if kernel_size % 2 == 1 else kernel_size + 1

        # Create a simple moving average
        smoothed = np.convolve(vertical_projection, np.ones(kernel_size)/kernel_size, mode='same')

        # Find peaks in the projection (column centers)
        peaks = []
        threshold = np.mean(smoothed) * 1.5
        for i in range(1, width - 1):
            if smoothed[i] > threshold and smoothed[i] > smoothed[i-1] and smoothed[i] > smoothed[i+1]:
                peaks.append(i)

        # If we found at least 2 peaks, assume they're column centers
        if len(peaks) >= 2:
            # Estimate column width as the average distance between peaks
            avg_distance = np.mean([peaks[i+1] - peaks[i] for i in range(len(peaks)-1)])
            column_width = int(avg_distance * 0.8)  # Use 80% of the distance as column width

            # Create regions for each column
            column_regions = []
            for peak in peaks:
                left = max(0, peak - column_width // 2)
                right = min(width, peak + column_width // 2)
                column_regions.append((left, 0, right - left, height))

            return column_regions

        # If we couldn't find columns, try a simpler approach: divide the page into 3 columns
        if len(peaks) < 2:
            column_width = width // 3
            return [
                (0, 0, column_width, height),
                (column_width, 0, column_width, height),
                (2 * column_width, 0, width - 2 * column_width, height)
            ]

    except Exception as e:
        # If anything goes wrong, return an empty list
        print(f"Error in detect_columns: {e}")
        return []

    return []

def preprocess_image(image: np.ndarray) -> np.ndarray:
    """Preprocess an image for table detection and OCR.

    Args:
        image: Input image as numpy array

    Returns:
        Preprocessed image as numpy array
    """
    # Convert to grayscale if needed
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    # Apply bilateral filter to reduce noise while preserving edges
    denoised = cv2.bilateralFilter(gray, 9, 75, 75)

    # Apply adaptive histogram equalization for better contrast
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    enhanced = clahe.apply(denoised)

    # Apply adaptive thresholding
    binary = cv2.adaptiveThreshold(
        enhanced,
        255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY,
        11,
        2
    )

    # Apply morphological operations to clean up the image
    kernel = np.ones((2, 2), np.uint8)
    processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

    return processed

def detect_tables(image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    """Detect tables in an image.

    Args:
        image: Preprocessed image as numpy array

    Returns:
        List of table bounding boxes as (x, y, width, height) tuples
    """
    # Convert to binary if not already
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    # Apply thresholding if not already binary
    if np.max(gray) > 1:
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    else:
        binary = gray

    # Get image dimensions
    height, width = binary.shape

    # Detect horizontal lines
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (int(width/10), 1))
    detected_horizontal = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)

    # Detect vertical lines
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, int(height/10)))
    detected_vertical = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel, iterations=2)

    # Combine horizontal and vertical lines
    table_mask = cv2.add(detected_horizontal, detected_vertical)

    # Dilate to connect nearby lines
    kernel = np.ones((3, 3), np.uint8)
    table_mask = cv2.dilate(table_mask, kernel, iterations=2)

    # Find contours of potential tables
    contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter contours by size
    tables = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        # Tables are typically larger than small text blocks
        if w > width * 0.1 and h > height * 0.05:
            tables.append((x, y, w, h))

    # If no tables were found using line detection, try text region detection
    if not tables:
        text_regions = detect_text_regions(image)
        tables.extend(text_regions)

    return tables

def extract_table_images(image: np.ndarray, tables: List[Tuple[int, int, int, int]]) -> List[np.ndarray]:
    """Extract table images from the original image.

    Args:
        image: Original image as numpy array
        tables: List of table bounding boxes as (x, y, width, height) tuples

    Returns:
        List of cropped table images
    """
    table_images = []

    for x, y, w, h in tables:
        # Add a small margin around the table
        margin = 5
        x_min = max(0, x - margin)
        y_min = max(0, y - margin)
        x_max = min(image.shape[1], x + w + margin)
        y_max = min(image.shape[0], y + h + margin)

        # Crop the table from the original image
        table_img = image[y_min:y_max, x_min:x_max]

        # Skip empty or invalid images
        if table_img.size > 0 and table_img.shape[0] > 0 and table_img.shape[1] > 0:
            table_images.append(table_img)

    return table_images
