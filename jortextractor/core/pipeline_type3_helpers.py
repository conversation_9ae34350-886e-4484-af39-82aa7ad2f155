"""Type 3 document processing helpers for JortExtractor.

This module contains helper functions specifically designed for processing Type 3 documents,
which are characterized by a single column format with names listed one per line.

Type 3 documents typically have:
- Single column layout
- Names listed one per line
- Simple structure without complex table formatting
- Minimal headers or separators
"""

import cv2
import numpy as np
import pytesseract
from typing import Dict, List, Optional, Tuple, Union
import logging
import re
from pathlib import Path

logger = logging.getLogger(__name__)

def detect_content_boundaries_type3(image: np.ndarray) -> Tuple[int, int, int, int]:
    """Detect the boundaries of the main content area in a Type 3 document.
    
    Type 3 documents have a single column, so we need to find the content boundaries
    to crop out headers, footers, and excessive margins.
    
    Args:
        image: The input image (BGR or grayscale)
        
    Returns:
        Tuple of (x, y, x_max, y_max) coordinates of the content area
    """
    try:
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
            
        height, width = gray.shape
        logger.info(f"Processing Type3 image of size {width}x{height}")
        
        # Apply thresholding to get binary image
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # Calculate horizontal and vertical projections
        h_projection = np.sum(binary, axis=1)  # Sum across width (horizontal projection)
        v_projection = np.sum(binary, axis=0)  # Sum across height (vertical projection)
        
        # Normalize projections
        if np.max(h_projection) > 0:
            h_projection = h_projection / np.max(h_projection)
        if np.max(v_projection) > 0:
            v_projection = v_projection / np.max(v_projection)
        
        # Find vertical boundaries (top and bottom)
        # Look for the first and last significant content
        y_min = 0
        for i in range(height):
            if h_projection[i] > 0.1:  # Threshold for content detection
                y_min = max(0, i - 20)  # Add small margin
                break
                
        y_max = height - 1
        for i in range(height - 1, 0, -1):
            if h_projection[i] > 0.1:
                y_max = min(height - 1, i + 20)  # Add small margin
                break
        
        # Find horizontal boundaries (left and right)
        # For Type 3, content is usually centered or left-aligned
        x_min = 0
        for i in range(width):
            if v_projection[i] > 0.05:  # Lower threshold for single column
                x_min = max(0, i - 10)  # Add small margin
                break
                
        x_max = width - 1
        for i in range(width - 1, 0, -1):
            if v_projection[i] > 0.05:
                x_max = min(width - 1, i + 10)  # Add small margin
                break
        
        logger.info(f"Detected Type3 content boundaries: x={x_min}, y={y_min}, x_max={x_max}, y_max={y_max}")
        
        return x_min, y_min, x_max, y_max
        
    except Exception as e:
        logger.error(f"Error detecting Type3 content boundaries: {e}")
        # Return full image boundaries as fallback
        height, width = image.shape[:2]
        return 0, 0, width - 1, height - 1

def preprocess_image_for_type3(image: np.ndarray) -> Dict[str, np.ndarray]:
    """Preprocess image for Type 3 document OCR.
    
    Type 3 documents benefit from simple preprocessing focused on text clarity.
    
    Args:
        image: Input image (BGR or grayscale)
        
    Returns:
        Dictionary containing different versions of the preprocessed image
    """
    try:
        # Make a deep copy of the original image to avoid modifying it
        original = image.copy()
        
        # Convert to grayscale if needed
        if len(original.shape) == 3:
            gray = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
        else:
            gray = original.copy()
        
        # Create versions for OCR
        versions = {
            'original': original,
            'grayscale': gray
        }
        
        # Create inverted version (often better for OCR)
        inverted = cv2.bitwise_not(gray)
        versions['inverted'] = inverted
        
        # Create enhanced version with slight denoising
        # Apply Gaussian blur to reduce noise
        denoised = cv2.GaussianBlur(gray, (3, 3), 0)
        versions['denoised'] = denoised
        
        # Create high contrast version
        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        versions['enhanced'] = enhanced
        
        logger.info(f"Created {len(versions)} image versions for Type3 processing")
        return versions
        
    except Exception as e:
        logger.error(f"Error preprocessing Type3 image: {e}")
        # Return minimal versions as fallback
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return {
                'original': image.copy(),
                'grayscale': gray,
                'inverted': cv2.bitwise_not(gray)
            }
        else:
            return {
                'original': image.copy(),
                'grayscale': image.copy(),
                'inverted': cv2.bitwise_not(image.copy())
            }

def perform_ocr_for_type3(image_dict: Dict[str, np.ndarray], page_num: int) -> Dict[str, str]:
    """Perform OCR on Type 3 document images.
    
    Type 3 documents are single column, so we use OCR configurations optimized for
    single column text extraction.
    
    Args:
        image_dict: Dictionary containing different versions of the preprocessed image
        page_num: The page number for logging
        
    Returns:
        Dictionary containing OCR results from different image versions
    """
    try:
        ocr_results = {}
        
        # OCR configuration for single column text
        # PSM 6: Uniform block of text
        # PSM 4: Single column of text of variable sizes
        ocr_configs = {
            'single_column': '--psm 4 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ0123456789 .,;:!?()[]{}"-\'',
            'uniform_block': '--psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ0123456789 .,;:!?()[]{}"-\'',
            'default': '--psm 3'
        }
        
        # Try OCR on different image versions
        for img_name, img in image_dict.items():
            logger.info(f"Performing OCR on {img_name} version for page {page_num}")
            
            # Try different OCR configurations
            for config_name, config in ocr_configs.items():
                try:
                    text = pytesseract.image_to_string(img, config=config, lang='fra+eng')
                    key = f"{img_name}_{config_name}"
                    ocr_results[key] = text.strip()
                    logger.info(f"OCR {key} extracted {len(text.strip())} characters")
                except Exception as e:
                    logger.warning(f"OCR failed for {img_name} with {config_name}: {e}")
        
        # Select the best OCR result based on content length and quality
        best_result = ""
        best_key = ""
        max_length = 0
        
        for key, text in ocr_results.items():
            # Simple heuristic: longer text with reasonable line count is usually better
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            if len(text) > max_length and len(lines) > 2:
                max_length = len(text)
                best_result = text
                best_key = key
        
        if best_result:
            logger.info(f"Best OCR result from {best_key} with {len(best_result)} characters")
            ocr_results['best'] = best_result
        else:
            logger.warning(f"No good OCR results found for page {page_num}")
            ocr_results['best'] = ""
        
        return ocr_results
        
    except Exception as e:
        logger.error(f"Error performing OCR for Type3 on page {page_num}: {e}")
        return {'best': ''}

def extract_names_from_text_type3(ocr_text: str, page_num: int) -> List[Dict[str, str]]:
    """Extract names from OCR text for Type 3 documents.

    Type 3 documents typically have names listed one per line in a single column.

    Args:
        ocr_text: The OCR text result
        page_num: The page number for logging

    Returns:
        List of dictionaries containing extracted names
    """
    try:
        extracted_names = []

        if not ocr_text or not ocr_text.strip():
            logger.warning(f"No OCR text to process for page {page_num}")
            return extracted_names

        # Split text into lines and clean them
        lines = [line.strip() for line in ocr_text.split('\n') if line.strip()]
        logger.info(f"Processing {len(lines)} lines from OCR text for page {page_num}")

        for line_num, line in enumerate(lines, 1):
            # Skip very short lines (likely noise)
            if len(line) < 3:
                continue

            # Skip lines that are mostly numbers (likely page numbers or other metadata)
            if len(re.sub(r'[0-9\s]', '', line)) < len(line) * 0.5:
                continue

            # Skip lines that look like headers or titles
            if any(keyword in line.upper() for keyword in ['LISTE', 'TITULAIRES', 'AYANTS', 'DROIT', 'PAGE', 'JORT']):
                continue

            # Clean the line
            cleaned_line = clean_name_type3(line)

            if cleaned_line and len(cleaned_line) > 2:
                extracted_names.append({
                    'text': cleaned_line,
                    'line_number': line_num,
                    'page': page_num,
                    'original_text': line
                })

        logger.info(f"Extracted {len(extracted_names)} names from page {page_num}")
        return extracted_names

    except Exception as e:
        logger.error(f"Error extracting names from Type3 text on page {page_num}: {e}")
        return []

def clean_name_type3(name: str) -> str:
    """Clean and standardize a name from Type 3 document.

    Args:
        name: Raw name string from OCR

    Returns:
        Cleaned name string
    """
    try:
        if not name:
            return ""

        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', name.strip())

        # Remove common OCR artifacts
        cleaned = re.sub(r'[|_~`]', '', cleaned)

        # Fix common OCR mistakes for French names
        # Replace common character misrecognitions
        replacements = {
            'é': 'é', 'è': 'è', 'ê': 'ê', 'ë': 'ë',
            'à': 'à', 'â': 'â', 'ä': 'ä',
            'ù': 'ù', 'û': 'û', 'ü': 'ü',
            'ô': 'ô', 'ö': 'ö',
            'ç': 'ç',
            'î': 'î', 'ï': 'ï',
            'ÿ': 'ÿ'
        }

        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)

        # Capitalize properly (first letter of each word)
        words = cleaned.split()
        capitalized_words = []

        for word in words:
            if word:
                # Handle special cases like "de", "du", "des", "le", "la", "les"
                if word.lower() in ['de', 'du', 'des', 'le', 'la', 'les', 'ben', 'ibn', 'el']:
                    capitalized_words.append(word.lower())
                else:
                    capitalized_words.append(word.capitalize())

        cleaned = ' '.join(capitalized_words)

        # Remove any remaining non-printable characters
        cleaned = ''.join(char for char in cleaned if char.isprintable())

        return cleaned.strip()

    except Exception as e:
        logger.error(f"Error cleaning name '{name}': {e}")
        return name.strip() if name else ""

def process_type3_page(image: np.ndarray, page_num: int) -> List[Dict[str, str]]:
    """Process a complete Type 3 document page.

    This is the main processing function that orchestrates the entire Type 3 pipeline.

    Args:
        image: The page image
        page_num: The page number

    Returns:
        List of extracted names with metadata
    """
    try:
        logger.info(f"Starting Type3 processing for page {page_num}")

        # Step 1: Detect content boundaries
        x_min, y_min, x_max, y_max = detect_content_boundaries_type3(image)

        # Step 2: Crop the content area
        content_image = image[y_min:y_max+1, x_min:x_max+1]
        logger.info(f"Cropped content area: {content_image.shape}")

        # Step 3: Preprocess the image for OCR
        processed_images = preprocess_image_for_type3(content_image)

        # Step 4: Perform OCR
        ocr_results = perform_ocr_for_type3(processed_images, page_num)

        # Step 5: Extract names from the best OCR result
        best_text = ocr_results.get('best', '')
        extracted_names = extract_names_from_text_type3(best_text, page_num)

        logger.info(f"Type3 processing completed for page {page_num}: {len(extracted_names)} names extracted")
        return extracted_names

    except Exception as e:
        logger.error(f"Error processing Type3 page {page_num}: {e}")
        return []
