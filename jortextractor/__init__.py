"""JortExtractor - Extract and process tables from Tunisian Official Gazette PDFs."""
__version__ = "0.1.0"

# Import core modules
from jortextractor.core import (
    DocumentClassifier,
    optimize_for_name_ocr,
    detect_text_regions,
    detect_columns,
    enhance_image_for_ocr,
    sharpen_text,
    deskew_image,
    remove_borders,
    validate_and_correct_name,
    normalize_text,
    correct_name,
    JortPDFProcessor,
)

# Import utility modules
from jortextractor.utils import (
    load_config,
    filter_name_entries,
    clean_and_standardize_names,
)

# Import benchmarking tools (deprecated)
from jortextractor.benchmarks import benchmark_ocr

# Import parallel processing modules
from jortextractor.parallel import (
    ParallelProcessor,
)

__all__ = [
    # Core
    "DocumentClassifier",
    "optimize_for_name_ocr",
    "detect_text_regions",
    "detect_columns",
    "enhance_image_for_ocr",
    "sharpen_text",
    "deskew_image",
    "remove_borders",
    "validate_and_correct_name",
    "normalize_text",
    "correct_name",
    "JortPDFProcessor",
    # Utils
    "load_config",
    "filter_name_entries",
    "clean_and_standardize_names",
    # Benchmarks (deprecated)
    "benchmark_ocr",
    # Parallel
    "ParallelProcessor",
]
