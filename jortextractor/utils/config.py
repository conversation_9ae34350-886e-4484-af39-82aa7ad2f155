"""Configuration handling for JortExtractor."""
from pathlib import Path
from typing import Dict, Any
import yaml
import logging

logger = logging.getLogger(__name__)

DEFAULT_CONFIG = {
    "input_dir": "./input_pdfs",
    "output_dir": "./output",
    "temp_dir": "./temp_images",
    "dpi": 300,
    "image_format": "png",
    "poppler_path": None,
    "table_min_width": 0.5, # Relative to page width
    "table_min_height": 0.2, # Relative to page height
    "min_text_density": 0.3, # Placeholder, not currently used in pipeline
    "line_threshold": 0.8, # Placeholder, not currently used in pipeline
    "ocr_languages": "eng+fra",
    "ocr_engine_mode": 3, # Default OCR Engine Mode
    "min_confidence": 70, # Min OCR confidence for individual words/lines in some contexts
    "ocr_min_confidence_block": 30, # Min OCR confidence for detecting a text block
    "min_line_length": 3, # Min characters for a line to be considered valid text
    "min_text_dense_width": 100, # Min pixel width for a text-dense region to be a table
    "min_text_dense_height": 100, # Min pixel height for a text-dense region to be a table
    "text_dense_vertical_margin_percent": 0.12, # Vertical margin for filtering text lines (INCREASED from 0.08)
    "text_dense_horizontal_margin_percent": 0.05, # Horizontal margin for filtering text lines
    "ocr_min_confidence_word": 5, # Min confidence for a word from image_to_data (DECREASED from 10 to 5)
    "text_dense_psm": 4, # PSM mode for initial full page OCR in text-dense detection (CHANGED from 11 to 4)
    "line_crop_psm": 6, # PSM mode for OCRing crops from line-based detection
    "adaptive_thresh_block_size": 11, # Block size for adaptive thresholding
    "adaptive_thresh_C": 2, # Constant subtracted from the mean in adaptive thresholding
    "column_split_threshold_factor": 1.5, # Factor of avg word width to detect column gap
    "column_gap_multiplier": 3, # Multiplier for column split gap detection
    "first_column_width_ratio_heuristic": 3.5, # Default restrictive heuristic
    "lenient_column_width_ratio_heuristic": 1.1, # For single wide columns
    "min_lines_for_restrictive_success": 10, # Min lines for restrictive heuristic to be considered successful
    "save_table_images": True, # Save cropped table images for debugging
    "save_pre_ocr_images": True, # Save final cropped table images just before OCR processing
    "pre_ocr_tables_dir": "./pre-ocr-tables", # Directory to save pre-OCR table images
    "pre_ocr_columns_dir": "./pre-ocr-columns", # Directory to save pre-OCR column images
    "max_line_tables_for_list_heuristic": 4, # If more line tables than this, prefer text-dense (REVERTED to 4)
    "min_name_words": 1, # Min words for a line to be considered a name (REVERTED from 2 to 1)
    "max_name_words": 6, # Max words for an INDIVIDUAL line to be considered a name
    "max_merged_name_words": 6, # Max words for a MERGED line
    "merge_max_y_gap_factor_normal": 1.0,    # Default for normal lines (INCREASED from 0.8 to 1.0)
    "merge_max_x_diff_normal": 40,           # Default for normal lines
    "merge_max_y_gap_factor_prefix": 1.5,    # More lenient if current line ends with a prefix
    "merge_max_x_diff_prefix": 100,          # More lenient if current line ends with a prefix (INCREASED from 75)
    "prevent_merge_if_both_long_words": 2,
    "common_non_name_keywords": [
        "liste", "titulaires", "comptes", "ayants", "droit", "union", "internationale",
        "banques", "informe", "personnes", "concernées", "articles", "loi", "finances",
        "année", "rappelé", "réclamer", "montants", "disponibles", "susceptible",
        "frappé", "déchéance", "compter", "juin", "l'absence", "régularisation",
        "demande", "expresse", "restitution", "délais", "légaux", "information",
        "complémentaire", "invitons", "contacter", "votre", "agence", "bancaire",
        "journal", "officiel", "république", "tunisienne", "annonces", "légales",
        "réglementaires", "judiciaires", "page"
    ],
    "fuzzy_match_threshold": 50, # Threshold for fuzzy matching names (LOWERED from 60 to 50)
    "required_fields": ["Name"],
    "optional_fields": ["Identifier", "Address"],
    "name_patterns": ["nom", "prénom", "name"],
    "identifier_patterns": ["cin", "id", "identifier"],
    "max_workers": 4,
    "batch_size": 10,
    "log_level": "INFO",
    "log_file": "jortextractor.log" # Placeholder, logging to console by default in pipeline
}

# Ensure all keys used in pipeline's config.get() calls are in DEFAULT_CONFIG
DEFAULT_CONFIG.setdefault('min_table_width', 50) # Absolute pixel value if not relative
DEFAULT_CONFIG.setdefault('min_table_height', 50) # Absolute pixel value if not relative


def load_config(config_path = None) -> Dict[str, Any]:
    """Load configuration from YAML file.

    Args:
        config_path: Path to config file. If None, uses defaults.

    Returns:
        Dictionary of configuration values
    """
    if config_path:
        try:
            # Convert to Path object if it's a string
            if isinstance(config_path, str):
                config_path = Path(config_path)

            if config_path.exists():
                with open(config_path) as f:
                    config = yaml.safe_load(f) or {}
                # Merge with defaults
                return {**DEFAULT_CONFIG, **config}
        except Exception as e:
            logger.warning(f"Failed to load config from {config_path}: {e}")
            logger.info("Using default configuration")
            return DEFAULT_CONFIG
    return DEFAULT_CONFIG

def validate_config(config: Dict[str, Any]) -> bool:
    """Validate configuration values.

    Args:
        config: Configuration dictionary to validate

    Returns:
        True if config is valid, False otherwise
    """
    try:
        # Validate required paths
        # Ensure directories exist or can be created
        for dir_key in ["input_dir", "output_dir", "temp_dir"]:
            if dir_key in config:
                Path(config[dir_key]).mkdir(parents=True, exist_ok=True)

        # Validate numerical values (add more as needed)
        if "dpi" in config: assert 100 <= config["dpi"] <= 600, "DPI out of range"
        # table_min_width/height in DEFAULT_CONFIG are relative, pipeline uses absolute if not overridden
        # No direct validation for them here as pipeline might use different interpretation
        if "min_confidence" in config: assert 0 <= config["min_confidence"] <= 100, "min_confidence out of range"
        if "ocr_min_confidence_block" in config: assert 0 <= config["ocr_min_confidence_block"] <= 100, "ocr_min_confidence_block out of range"
        if "min_line_length" in config: assert config["min_line_length"] >= 0, "min_line_length must be non-negative"

        return True
    except AssertionError as e:
        logger.error(f"Invalid configuration value: {e}")
        return False
    except Exception as e:
        logger.error(f"Error during configuration validation: {e}")
        return False
