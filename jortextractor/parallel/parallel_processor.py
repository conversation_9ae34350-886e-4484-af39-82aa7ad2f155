"""Parallel processing module for JortExtractor."""
import os
import time
import logging
import psutil
from pathlib import Path
from typing import List, Dict, Optional, Union, Callable
import pandas as pd
import concurrent.futures
from concurrent.futures import ProcessPoolExecutor
from jortextractor.core.pipeline import JortPDFProcessor

class ParallelProcessor:
    """Process multiple PDFs in parallel with resource monitoring."""

    def __init__(self,
                 config_path: Optional[Path] = None,
                 max_workers: Optional[int] = None,
                 memory_limit_percent: float = 80.0,
                 temp_dir: Optional[Path] = None):
        """Initialize the parallel processor.

        Args:
            config_path: Path to configuration file
            max_workers: Maximum number of worker processes (default: CPU count)
            memory_limit_percent: Memory usage limit as percentage (default: 80%)
            temp_dir: Directory for temporary files
        """
        self.logger = logging.getLogger(__name__)
        self.config_path = config_path

        # Set maximum number of workers
        cpu_count = os.cpu_count() or 1
        self.max_workers = min(max_workers or cpu_count, cpu_count)
        self.logger.info(f"Using maximum {self.max_workers} worker processes")

        # Set memory limit
        self.memory_limit_percent = memory_limit_percent
        total_memory = psutil.virtual_memory().total
        self.memory_limit_bytes = total_memory * (memory_limit_percent / 100.0)
        self.logger.info(f"Memory limit set to {memory_limit_percent}% "
                         f"({self.memory_limit_bytes / (1024**3):.2f} GB)")

        # Set temporary directory
        self.temp_dir = temp_dir
        if self.temp_dir:
            self.temp_dir.mkdir(parents=True, exist_ok=True)

    def process_pdfs(self,
                    pdf_paths: List[Path],
                    ground_truth_paths: Optional[List[Path]] = None,
                    output_dir: Optional[Path] = None,
                    callback: Optional[Callable[[Dict], None]] = None) -> List[Dict]:
        """Process multiple PDFs in parallel.

        Args:
            pdf_paths: List of paths to PDF files
            ground_truth_paths: Optional list of paths to ground truth files
            output_dir: Optional directory for output files
            callback: Optional callback function for progress updates

        Returns:
            List of processing results
        """
        # Create output directory if specified
        if output_dir:
            output_dir.mkdir(parents=True, exist_ok=True)

        # Prepare arguments for each PDF
        args_list = []
        for i, pdf_path in enumerate(pdf_paths):
            gt_path = ground_truth_paths[i] if ground_truth_paths and i < len(ground_truth_paths) else None
            args_list.append((pdf_path, gt_path, output_dir))

        # Start processing
        start_time = time.time()
        self.logger.info(f"Starting parallel processing of {len(pdf_paths)} PDFs")

        # Process PDFs with dynamic worker adjustment
        results = self._process_with_resource_monitoring(args_list, callback)

        # Calculate statistics
        end_time = time.time()
        total_time = end_time - start_time
        avg_time_per_pdf = total_time / len(pdf_paths) if pdf_paths else 0

        self.logger.info(f"Completed processing {len(pdf_paths)} PDFs in {total_time:.2f} seconds "
                         f"(average: {avg_time_per_pdf:.2f} seconds per PDF)")

        return results

    def _process_with_resource_monitoring(self,
                                         args_list: List[tuple],
                                         callback: Optional[Callable] = None) -> List[Dict]:
        """Process PDFs with dynamic worker adjustment based on resource monitoring.

        Args:
            args_list: List of argument tuples (pdf_path, ground_truth_path, output_dir)
            callback: Optional callback function for progress updates

        Returns:
            List of processing results
        """
        results = []
        pending_args = list(args_list)
        active_workers = 0
        max_active_workers = self.max_workers

        # Create a process pool with maximum workers
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {}

            while pending_args or futures:
                # Check system resources
                mem_usage = psutil.virtual_memory()
                cpu_usage = psutil.cpu_percent(interval=0.1)

                # Adjust max_active_workers based on memory usage
                if mem_usage.percent > self.memory_limit_percent:
                    # Reduce workers if memory usage is too high
                    max_active_workers = max(1, max_active_workers - 1)
                    self.logger.warning(f"Memory usage high ({mem_usage.percent}%). "
                                       f"Reducing workers to {max_active_workers}")
                elif mem_usage.percent < self.memory_limit_percent * 0.8 and max_active_workers < self.max_workers:
                    # Increase workers if memory usage is low
                    max_active_workers = min(self.max_workers, max_active_workers + 1)
                    self.logger.info(f"Memory usage acceptable ({mem_usage.percent}%). "
                                    f"Increasing workers to {max_active_workers}")

                # Submit new tasks if resources allow
                while pending_args and len(futures) < max_active_workers:
                    args = pending_args.pop(0)
                    pdf_path, gt_path, output_dir = args

                    # Create a new processor for each task to avoid shared state
                    processor = JortPDFProcessor(config_path=self.config_path,
                                               temp_dir=self.temp_dir)

                    # Submit the task
                    future = executor.submit(self._process_single_pdf, processor, pdf_path, gt_path, output_dir)
                    futures[future] = pdf_path
                    active_workers += 1

                    self.logger.info(f"Submitted {pdf_path.name} for processing "
                                    f"(active: {active_workers}/{max_active_workers})")

                # Check for completed tasks
                done, _ = concurrent.futures.wait(
                    futures.keys(),
                    timeout=1.0,
                    return_when=concurrent.futures.FIRST_COMPLETED
                )

                # Process completed tasks
                for future in done:
                    pdf_path = futures[future]
                    try:
                        result = future.result()
                        results.append(result)

                        # Call callback if provided
                        if callback:
                            callback(result)

                        self.logger.info(f"Completed processing {pdf_path.name}")
                    except Exception as e:
                        self.logger.error(f"Error processing {pdf_path.name}: {e}")
                        results.append({
                            'pdf_path': pdf_path,
                            'status': 'error',
                            'error': str(e)
                        })

                    # Remove from active futures
                    del futures[future]
                    active_workers -= 1

                # Short sleep to prevent CPU hogging
                time.sleep(0.1)

        return results

    @staticmethod
    def _process_single_pdf(processor: JortPDFProcessor,
                           pdf_path: Path,
                           ground_truth_path: Optional[Path],
                           output_dir: Optional[Path]) -> Dict:
        """Process a single PDF file.

        Args:
            processor: JortPDFProcessor instance
            pdf_path: Path to PDF file
            ground_truth_path: Optional path to ground truth file
            output_dir: Optional directory for output files

        Returns:
            Processing result dictionary
        """
        try:
            # Process the PDF
            result = processor.process_pdf(pdf_path, ground_truth_path)

            # Add PDF path to result
            result['pdf_path'] = pdf_path

            # Save output if directory specified
            if output_dir and 'extracted_data' in result:
                output_path = output_dir / f"{pdf_path.stem}.csv"
                processor.save_to_csv(result['extracted_data'], output_path)
                result['output_path'] = output_path

            return result
        except Exception as e:
            # Return error information
            return {
                'pdf_path': pdf_path,
                'status': 'error',
                'error': str(e)
            }
