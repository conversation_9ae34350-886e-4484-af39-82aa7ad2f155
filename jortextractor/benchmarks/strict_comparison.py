"""
Strict comparison module for testing OCR results against ground truth data.

This module provides functions for strict comparison of OCR results with ground truth data,
with detailed error reporting and metrics.
"""

import re
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import Levenshtein
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("strict_comparison")

class StrictComparison:
    """Class for strict comparison of OCR results with ground truth data."""

    def __init__(self, case_sensitive: bool = False, ignore_spaces: bool = False, 
                 ignore_punctuation: bool = False, levenshtein_threshold: int = 3):
        """Initialize the strict comparison.

        Args:
            case_sensitive: Whether to perform case-sensitive comparison
            ignore_spaces: Whether to ignore spaces in comparison
            ignore_punctuation: Whether to ignore punctuation in comparison
            levenshtein_threshold: Maximum Levenshtein distance to consider as a near match
        """
        self.case_sensitive = case_sensitive
        self.ignore_spaces = ignore_spaces
        self.ignore_punctuation = ignore_punctuation
        self.levenshtein_threshold = levenshtein_threshold

    def _normalize_text(self, text: str) -> str:
        """Normalize text based on comparison settings.

        Args:
            text: Text to normalize

        Returns:
            Normalized text
        """
        if not text:
            return ""

        # Convert to lowercase if not case sensitive
        if not self.case_sensitive:
            text = text.upper()

        # Remove spaces if ignore_spaces is True
        if self.ignore_spaces:
            text = re.sub(r'\s+', '', text)

        # Remove punctuation if ignore_punctuation is True
        if self.ignore_punctuation:
            text = re.sub(r'[^\w\s]', '', text)

        return text

    def compare_dataframes(self, ocr_df: pd.DataFrame, gt_df: pd.DataFrame, 
                          name_col: str = "Nom et prénom", id_col: str = "Identifiant") -> Dict[str, Any]:
        """Compare OCR DataFrame with ground truth DataFrame.

        Args:
            ocr_df: DataFrame with OCR results
            gt_df: DataFrame with ground truth data
            name_col: Column name for person names
            id_col: Column name for identifiers

        Returns:
            Dictionary with comparison metrics and detailed error reports
        """
        # Check if DataFrames are empty
        if ocr_df.empty or gt_df.empty:
            logger.warning("One or both DataFrames are empty")
            return {
                'exact_matches': 0,
                'name_only_matches': 0,
                'id_only_matches': 0,
                'near_matches': 0,
                'total_ocr': 0,
                'total_gt': 0,
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'errors': [],
                'error_summary': {}
            }

        # Ensure columns exist
        if name_col not in ocr_df.columns or name_col not in gt_df.columns:
            logger.error(f"Name column '{name_col}' not found in one or both DataFrames")
            return {
                'error': f"Name column '{name_col}' not found in one or both DataFrames"
            }

        if id_col not in ocr_df.columns or id_col not in gt_df.columns:
            logger.error(f"ID column '{id_col}' not found in one or both DataFrames")
            return {
                'error': f"ID column '{id_col}' not found in one or both DataFrames"
            }

        # Make copies to avoid modifying originals
        ocr_copy = ocr_df.copy()
        gt_copy = gt_df.copy()

        # Initialize counters and error lists
        exact_matches = 0
        name_only_matches = 0
        id_only_matches = 0
        near_matches = 0
        errors = []
        error_types = {
            'name_mismatch': 0,
            'id_mismatch': 0,
            'missing_from_ocr': 0,
            'extra_in_ocr': 0
        }

        # Track matched rows to avoid duplicate matches
        matched_ocr_indices = set()
        matched_gt_indices = set()

        # First pass: Find exact matches (both name and ID match exactly)
        for gt_idx, gt_row in gt_copy.iterrows():
            gt_name = str(gt_row[name_col])
            gt_id = str(gt_row[id_col])
            gt_name_norm = self._normalize_text(gt_name)
            gt_id_norm = self._normalize_text(gt_id)

            for ocr_idx, ocr_row in ocr_copy.iterrows():
                if ocr_idx in matched_ocr_indices:
                    continue

                ocr_name = str(ocr_row[name_col])
                ocr_id = str(ocr_row[id_col])
                ocr_name_norm = self._normalize_text(ocr_name)
                ocr_id_norm = self._normalize_text(ocr_id)

                # Check for exact match
                if gt_name_norm == ocr_name_norm and gt_id_norm == ocr_id_norm:
                    exact_matches += 1
                    matched_ocr_indices.add(ocr_idx)
                    matched_gt_indices.add(gt_idx)
                    break

        # Second pass: Find name-only matches
        for gt_idx, gt_row in gt_copy.iterrows():
            if gt_idx in matched_gt_indices:
                continue

            gt_name = str(gt_row[name_col])
            gt_id = str(gt_row[id_col])
            gt_name_norm = self._normalize_text(gt_name)

            for ocr_idx, ocr_row in ocr_copy.iterrows():
                if ocr_idx in matched_ocr_indices:
                    continue

                ocr_name = str(ocr_row[name_col])
                ocr_id = str(ocr_row[id_col])
                ocr_name_norm = self._normalize_text(ocr_name)

                # Check for name-only match
                if gt_name_norm == ocr_name_norm:
                    name_only_matches += 1
                    matched_ocr_indices.add(ocr_idx)
                    matched_gt_indices.add(gt_idx)
                    
                    # Record error for ID mismatch
                    errors.append({
                        'type': 'id_mismatch',
                        'gt_name': gt_name,
                        'gt_id': gt_id,
                        'ocr_name': ocr_name,
                        'ocr_id': ocr_id,
                        'levenshtein_distance': Levenshtein.distance(gt_id, ocr_id)
                    })
                    error_types['id_mismatch'] += 1
                    break

        # Third pass: Find ID-only matches
        for gt_idx, gt_row in gt_copy.iterrows():
            if gt_idx in matched_gt_indices:
                continue

            gt_name = str(gt_row[name_col])
            gt_id = str(gt_row[id_col])
            gt_id_norm = self._normalize_text(gt_id)

            for ocr_idx, ocr_row in ocr_copy.iterrows():
                if ocr_idx in matched_ocr_indices:
                    continue

                ocr_name = str(ocr_row[name_col])
                ocr_id = str(ocr_row[id_col])
                ocr_id_norm = self._normalize_text(ocr_id)

                # Check for ID-only match
                if gt_id_norm == ocr_id_norm:
                    id_only_matches += 1
                    matched_ocr_indices.add(ocr_idx)
                    matched_gt_indices.add(gt_idx)
                    
                    # Record error for name mismatch
                    errors.append({
                        'type': 'name_mismatch',
                        'gt_name': gt_name,
                        'gt_id': gt_id,
                        'ocr_name': ocr_name,
                        'ocr_id': ocr_id,
                        'levenshtein_distance': Levenshtein.distance(gt_name, ocr_name)
                    })
                    error_types['name_mismatch'] += 1
                    break

        # Fourth pass: Find near matches using Levenshtein distance
        for gt_idx, gt_row in gt_copy.iterrows():
            if gt_idx in matched_gt_indices:
                continue

            gt_name = str(gt_row[name_col])
            gt_id = str(gt_row[id_col])

            best_match_idx = None
            best_match_distance = float('inf')

            for ocr_idx, ocr_row in ocr_copy.iterrows():
                if ocr_idx in matched_ocr_indices:
                    continue

                ocr_name = str(ocr_row[name_col])
                ocr_id = str(ocr_row[id_col])

                # Calculate Levenshtein distance for name
                name_distance = Levenshtein.distance(gt_name, ocr_name)
                
                # Only consider as a near match if distance is below threshold
                if name_distance <= self.levenshtein_threshold:
                    if name_distance < best_match_distance:
                        best_match_distance = name_distance
                        best_match_idx = ocr_idx

            # If we found a near match
            if best_match_idx is not None:
                near_matches += 1
                matched_ocr_indices.add(best_match_idx)
                matched_gt_indices.add(gt_idx)
                
                ocr_name = str(ocr_copy.loc[best_match_idx, name_col])
                ocr_id = str(ocr_copy.loc[best_match_idx, id_col])
                
                # Record error for near match
                errors.append({
                    'type': 'near_match',
                    'gt_name': gt_name,
                    'gt_id': gt_id,
                    'ocr_name': ocr_name,
                    'ocr_id': ocr_id,
                    'levenshtein_distance': best_match_distance
                })

        # Record missing entries (in ground truth but not in OCR)
        for gt_idx, gt_row in gt_copy.iterrows():
            if gt_idx not in matched_gt_indices:
                errors.append({
                    'type': 'missing_from_ocr',
                    'gt_name': str(gt_row[name_col]),
                    'gt_id': str(gt_row[id_col]),
                    'ocr_name': None,
                    'ocr_id': None
                })
                error_types['missing_from_ocr'] += 1

        # Record extra entries (in OCR but not in ground truth)
        for ocr_idx, ocr_row in ocr_copy.iterrows():
            if ocr_idx not in matched_ocr_indices:
                errors.append({
                    'type': 'extra_in_ocr',
                    'gt_name': None,
                    'gt_id': None,
                    'ocr_name': str(ocr_row[name_col]),
                    'ocr_id': str(ocr_row[id_col])
                })
                error_types['extra_in_ocr'] += 1

        # Calculate metrics
        total_gt = len(gt_copy)
        total_ocr = len(ocr_copy)
        
        # Only exact matches count for precision and recall
        precision = exact_matches / total_ocr if total_ocr > 0 else 0
        recall = exact_matches / total_gt if total_gt > 0 else 0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

        # Return detailed results
        return {
            'exact_matches': exact_matches,
            'name_only_matches': name_only_matches,
            'id_only_matches': id_only_matches,
            'near_matches': near_matches,
            'total_ocr': total_ocr,
            'total_gt': total_gt,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'errors': errors,
            'error_summary': error_types
        }

    def generate_report(self, comparison_result: Dict[str, Any], output_path: Optional[Path] = None) -> str:
        """Generate a detailed report from comparison results.

        Args:
            comparison_result: Dictionary with comparison results
            output_path: Path to save the report (optional)

        Returns:
            Report as a string
        """
        if 'error' in comparison_result:
            report = f"Error: {comparison_result['error']}\n"
            return report

        # Extract metrics
        exact_matches = comparison_result['exact_matches']
        name_only_matches = comparison_result['name_only_matches']
        id_only_matches = comparison_result['id_only_matches']
        near_matches = comparison_result['near_matches']
        total_ocr = comparison_result['total_ocr']
        total_gt = comparison_result['total_gt']
        precision = comparison_result['precision'] * 100
        recall = comparison_result['recall'] * 100
        f1_score = comparison_result['f1_score'] * 100
        errors = comparison_result['errors']
        error_summary = comparison_result['error_summary']

        # Build report
        report = "=== Strict Comparison Report ===\n\n"
        
        # Summary metrics
        report += "Summary Metrics:\n"
        report += f"  Exact Matches: {exact_matches}/{total_gt} ({exact_matches/total_gt*100:.1f}% of ground truth)\n"
        report += f"  Name-Only Matches: {name_only_matches}\n"
        report += f"  ID-Only Matches: {id_only_matches}\n"
        report += f"  Near Matches: {near_matches}\n"
        report += f"  Total OCR Entries: {total_ocr}\n"
        report += f"  Total Ground Truth Entries: {total_gt}\n"
        report += f"  Precision: {precision:.1f}%\n"
        report += f"  Recall: {recall:.1f}%\n"
        report += f"  F1 Score: {f1_score:.1f}%\n\n"
        
        # Error summary
        report += "Error Summary:\n"
        report += f"  Name Mismatches: {error_summary.get('name_mismatch', 0)}\n"
        report += f"  ID Mismatches: {error_summary.get('id_mismatch', 0)}\n"
        report += f"  Missing from OCR: {error_summary.get('missing_from_ocr', 0)}\n"
        report += f"  Extra in OCR: {error_summary.get('extra_in_ocr', 0)}\n\n"
        
        # Detailed errors (limit to first 20 for readability)
        report += "Detailed Errors (first 20):\n"
        for i, error in enumerate(errors[:20]):
            report += f"  Error {i+1}:\n"
            report += f"    Type: {error['type']}\n"
            
            if error['gt_name'] is not None:
                report += f"    Ground Truth Name: {error['gt_name']}\n"
            if error['gt_id'] is not None:
                report += f"    Ground Truth ID: {error['gt_id']}\n"
            
            if error['ocr_name'] is not None:
                report += f"    OCR Name: {error['ocr_name']}\n"
            if error['ocr_id'] is not None:
                report += f"    OCR ID: {error['ocr_id']}\n"
            
            if 'levenshtein_distance' in error:
                report += f"    Levenshtein Distance: {error['levenshtein_distance']}\n"
            
            report += "\n"
        
        if len(errors) > 20:
            report += f"  ... and {len(errors) - 20} more errors\n\n"
        
        # Save report if output path is provided
        if output_path:
            with open(output_path, 'w') as f:
                f.write(report)
            logger.info(f"Report saved to {output_path}")
        
        return report
