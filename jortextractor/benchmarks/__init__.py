"""
DEPRECATED: Benchmarking tools for JortExtractor.

This module is deprecated and will be removed in a future version.
Please use the tests.benchmarks module instead for all benchmarking functionality.
"""

import warnings

warnings.warn(
    "The jortextractor.benchmarks module is deprecated and will be removed in a future version. "
    "Please use tests.benchmarks instead for all benchmarking functionality.",
    DeprecationWarning,
    stacklevel=2
)

# Create a simple function to benchmark OCR
def benchmark_ocr(image_path, output_dir="ocr_benchmark_results", compare_with_ocr_space=False):
    """
    DEPRECATED: Benchmark OCR performance on an image.

    This function is deprecated and will be removed in a future version.
    Please use tests.benchmarks.benchmark_ocr instead.
    """
    warnings.warn(
        "The jortextractor.benchmarks.benchmark_ocr function is deprecated. "
        "Please use tests.benchmarks.benchmark_ocr instead.",
        DeprecationWarning,
        stacklevel=2
    )
    # Import here to avoid circular imports
    from tests.benchmarks import OCRBenchmark
    benchmark = OCRBenchmark(output_dir=output_dir, compare_with_ocr_space=compare_with_ocr_space)
    return benchmark.benchmark_image(image_path)

# Define __all__ for backward compatibility
__all__ = [
    "benchmark_ocr",
]

# Add a note that these classes should be imported from tests.benchmarks
def __getattr__(name):
    if name in ["OCRBenchmark", "OCRSpaceClient", "compare_with_ground_truth"]:
        warnings.warn(
            f"The jortextractor.benchmarks.{name} is deprecated. "
            f"Please import {name} from tests.benchmarks instead.",
            DeprecationWarning,
            stacklevel=2
        )
        # Import on demand to avoid circular imports
        import importlib
        module = importlib.import_module("tests.benchmarks")
        return getattr(module, name)
    raise AttributeError(f"module 'jortextractor.benchmarks' has no attribute '{name}'")
