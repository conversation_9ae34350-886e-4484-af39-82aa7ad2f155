"""
OCR.space API client for benchmarking and comparing OCR results.

IMPORTANT: This module is for BENCHMARKING ONLY. It is never used as an alternative
to our own OCR implementation. The purpose of this module is to provide a way to
compare our OCR results with a commercial OCR service to identify areas for improvement.

Under no circumstances should this be used as a fallback or alternative to our
own OCR implementation, as that would defeat the purpose of this project.
"""
import os
import json
import logging
import requests
import tempfile
import cv2
import numpy as np
from typing import Dict, List, Optional, Union, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

class OCRSpaceClient:
    """Client for the OCR.space API for benchmarking purposes only.

    This class provides methods to interact with the OCR.space API for the sole purpose
    of benchmarking our own OCR implementation. It is never used as an alternative
    to our own OCR implementation in the main processing pipeline.

    WARNING: This is for testing and benchmarking only!
    """

    def __init__(self, api_key: str = 'K888957'):
        """Initialize the OCR.space API client.

        Args:
            api_key: OCR.space API key (using the standard API key)
        """
        self.api_key = api_key
        # Use the POST endpoint as specified in the website
        self.base_url = 'https://api.ocr.space/parse/image'

    def _resize_image_for_ocr_space(self, file_path: Path, max_size_kb: int = 1024) -> Tuple[Path, bool]:
        """Optimize an image to fit within the OCR.space file size limit.

        Args:
            file_path: Path to the image file
            max_size_kb: Maximum file size in KB (OCR.space API limit is 1MB)

        Returns:
            Tuple of (path to optimized image, whether optimization was needed)
        """
        file_path = Path(file_path)
        if not file_path.exists():
            logger.error(f"File {file_path} does not exist")
            return file_path, False

        # Check current file size
        file_size_kb = os.path.getsize(file_path) / 1024

        # If file is already smaller than 1MB, return the original path
        if file_size_kb < max_size_kb:
            logger.info(f"File {file_path} is {file_size_kb:.1f} KB (< 1MB), no optimization needed")
            return file_path, False

        logger.info(f"Image {file_path} is {file_size_kb:.1f} KB (>= 1MB), optimizing for OCR.space")

        # Read the image
        img = cv2.imread(str(file_path))
        if img is None:
            logger.error(f"Could not read image {file_path}")
            return file_path, False

        # Create a temporary file for the optimized image
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            temp_path = Path(temp_file.name)

        # First try: optimize the PNG without resizing using lossless compression
        # Try different compression levels to find the optimal one that keeps the file under 1MB
        for compression_level in [0, 1, 2, 3, 4, 5]:
            cv2.imwrite(str(temp_path), img, [cv2.IMWRITE_PNG_COMPRESSION, compression_level])
            optimized_size_kb = os.path.getsize(temp_path) / 1024

            if optimized_size_kb <= max_size_kb:
                logger.info(f"Optimized image with lossless compression level {compression_level}, new size: {optimized_size_kb:.1f} KB")
                return temp_path, True

        # If lossless compression alone doesn't work, we need to resize
        # Calculate the scaling factor based on file size
        # Use a less aggressive scaling factor to preserve image quality
        target_size_kb = max_size_kb * 0.98  # 2% safety margin
        scale_factor = np.sqrt(target_size_kb / file_size_kb)

        # Resize the image with high-quality interpolation
        new_width = int(img.shape[1] * scale_factor)
        new_height = int(img.shape[0] * scale_factor)

        # Use INTER_LANCZOS4 for downscaling (highest quality)
        resized_img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

        # Apply a slight sharpening to enhance text edges after resizing
        # This helps maintain text clarity after resizing
        kernel = np.array([[-1, -1, -1],
                          [-1,  9, -1],
                          [-1, -1, -1]]) / 5.0  # Mild sharpening
        sharpened_img = cv2.filter2D(resized_img, -1, kernel)

        # Save the resized image with lossless compression
        # Try different compression levels to find the optimal one
        for compression_level in [0, 1, 2, 3, 4, 5]:
            cv2.imwrite(str(temp_path), sharpened_img, [cv2.IMWRITE_PNG_COMPRESSION, compression_level])
            new_size_kb = os.path.getsize(temp_path) / 1024

            if new_size_kb <= max_size_kb:
                logger.info(f"Resized image to {new_width}x{new_height} with lossless compression level {compression_level}, new size: {new_size_kb:.1f} KB")
                return temp_path, True

        # If we still can't get under 1MB, use a more aggressive compression
        cv2.imwrite(str(temp_path), sharpened_img, [cv2.IMWRITE_PNG_COMPRESSION, 9])  # Maximum compression
        final_size_kb = os.path.getsize(temp_path) / 1024

        if final_size_kb <= max_size_kb:
            logger.info(f"Resized image to {new_width}x{new_height} with maximum compression, new size: {final_size_kb:.1f} KB")
            return temp_path, True

        # If we still can't get under 1MB, try more aggressive resizing
        scale_factor *= 0.9  # Reduce by another 10%
        new_width = int(img.shape[1] * scale_factor)
        new_height = int(img.shape[0] * scale_factor)

        final_img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

        # Apply sharpening to the final image
        final_img = cv2.filter2D(final_img, -1, kernel)

        # Save with lossless compression
        cv2.imwrite(str(temp_path), final_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
        final_size_kb = os.path.getsize(temp_path) / 1024

        logger.info(f"Final resize to {new_width}x{new_height} with lossless compression, new size: {final_size_kb:.1f} KB")
        return temp_path, True

    def process_file(self, file_path: Union[str, Path]) -> Optional[Dict]:
        """Process a local image file with OCR.space API.

        Args:
            file_path: Path to the image file

        Returns:
            OCR result as a dictionary
        """
        file_path = Path(file_path)

        try:
            # Resize the image if needed to fit within OCR.space file size limits (1MB)
            temp_file_created = False
            resized_path = file_path

            try:
                # Only optimize if larger than or equal to 1MB
                resized_path, temp_file_created = self._resize_image_for_ocr_space(file_path)
            except Exception as e:
                logger.error(f"Error optimizing image: {e}")
                # Continue with the original file if there's an error
                resized_path = file_path
                temp_file_created = False

            logger.info(f"Sending request to OCR.space API for file: {resized_path}")
            logger.info(f"File size: {os.path.getsize(resized_path) / 1024:.1f} KB")

            # Prepare headers
            headers = {
                'apikey': self.api_key
            }

            try:
                # Open the file and prepare the request
                with open(resized_path, 'rb') as f:
                    # Prepare files and form data
                    files = {
                        'file': (resized_path.name, f, 'image/png')
                    }

                    # Form data with the specific parameters requested
                    data = {
                        'language': 'fre',  # French
                        'isTable': 'true',  # Detect table
                        'OCREngine': '2',   # OCR Engine 2
                        'FileType': '.png', # PNG file type
                        'scale': 'false',   # No auto enlarge
                        'isOverlayRequired': 'true',
                        'IsCreateSearchablePDF': 'false',
                        'isSearchablePdfHideTextLayer': 'true',
                        'detectOrientation': 'false'
                    }

                    # Send the request
                    response = requests.post(
                        self.base_url,
                        headers=headers,
                        files=files,
                        data=data,
                        timeout=30  # Add timeout to avoid hanging
                    )

                logger.info(f"Response status code: {response.status_code}")
            except Exception as e:
                logger.error(f"Exception during request: {e}")
                return None

            if response.status_code != 200:
                logger.error(f"OCR.space API error: {response.status_code} - {response.text}")
                return None

            response_content = response.content.decode()
            logger.info(f"OCR.space API response: {response_content[:200]}...")  # Log first 200 chars

            try:
                result = json.loads(response_content)
                if 'ParsedResults' in result and result['ParsedResults']:
                    logger.info(f"OCR.space API parsed {len(result['ParsedResults'])} results")
                    for i, parsed_result in enumerate(result['ParsedResults']):
                        if 'ParsedText' in parsed_result:
                            text_preview = parsed_result['ParsedText'][:100].replace('\n', ' ')
                            logger.info(f"Result {i+1} text preview: {text_preview}...")
                else:
                    logger.warning("OCR.space API returned no parsed results")
                return result
            except json.JSONDecodeError as e:
                logger.error(f"Error decoding OCR.space API response: {e}")
                return None
        except Exception as e:
            logger.error(f"Error processing file with OCR.space API: {e}")
            return None
        finally:
            # Clean up temporary file if created
            if temp_file_created and resized_path.exists():
                try:
                    os.unlink(resized_path)
                    logger.debug(f"Deleted temporary file {resized_path}")
                except Exception as e:
                    logger.warning(f"Error deleting temporary file {resized_path}: {e}")

# URL processing method removed as we're focusing on file processing only

    def extract_text(self, ocr_result: Optional[Dict]) -> List[Dict]:
        """Extract text from OCR.space API result.

        Args:
            ocr_result: OCR.space API result dictionary

        Returns:
            List of dictionaries with 'text' and 'identifier' keys
        """
        if not ocr_result or 'ParsedResults' not in ocr_result:
            return []

        extracted_data = []
        for result in ocr_result['ParsedResults']:
            if result.get('ParsedText'):
                # Split by lines and filter out empty lines
                lines = [line.strip() for line in result['ParsedText'].split('\n') if line.strip()]

                # Skip the first line as it's the header (column names)
                if lines and ('Nom et prénom' in lines[0] or 'Identifiant' in lines[0]):
                    lines = lines[1:]

                # Process lines to handle multi-line names
                processed_lines = []
                i = 0
                while i < len(lines):
                    parts = lines[i].split('\t')

                    # If we have a line with only a name (no identifier or empty identifier)
                    if len(parts) == 1 or (len(parts) >= 2 and not parts[1].strip()):
                        # This is likely part of a multi-line name
                        current_name = parts[0].strip()
                        combined_name = current_name
                        next_identifier = ""

                        # Look ahead up to 3 lines to find an identifier
                        j = 1
                        while i + j < len(lines) and j <= 3:
                            next_parts = lines[i + j].split('\t')

                            # If this line has an identifier, use it
                            if len(next_parts) >= 2 and next_parts[1].strip():
                                next_name = next_parts[0].strip()
                                next_identifier = next_parts[1].strip()

                                # Combine all names up to this point
                                combined_name = f"{combined_name} {next_name}"

                                # Add the combined entry
                                processed_lines.append(f"{combined_name}\t{next_identifier}")

                                # Skip all the lines we've processed
                                i += j + 1
                                break
                            else:
                                # This line also has no identifier, add it to the combined name
                                if len(next_parts) >= 1:
                                    combined_name = f"{combined_name} {next_parts[0].strip()}"
                                j += 1

                        # If we didn't find an identifier, just add the combined name without an identifier
                        if not next_identifier and j > 1:
                            # We combined some lines but didn't find an identifier
                            # Skip the lines we've processed
                            i += j
                            continue
                        elif not next_identifier:
                            # We didn't find any lines to combine, just add this line as is
                            processed_lines.append(lines[i])
                            i += 1
                            continue
                    else:
                        # This line has both a name and an identifier, add it as is
                        processed_lines.append(lines[i])
                        i += 1

                # Now process the cleaned lines
                for line in processed_lines:
                    # OCR.space uses tabs to separate columns
                    parts = line.split('\t')

                    if len(parts) >= 2:
                        # If we have at least 2 parts, assume the first is the name and the second is the identifier
                        name = parts[0].strip()
                        identifier = parts[1].strip()

                        # Remove any spaces between asterisks and numbers in the identifier
                        if identifier:
                            # Replace spaces between asterisks and numbers
                            import re
                            identifier = re.sub(r'(\*+)\s+(\d+)', r'\1\2', identifier)

                        # Skip empty names or identifiers
                        if name and identifier:
                            extracted_data.append({
                                'text': name,
                                'identifier': identifier
                            })
                    elif len(parts) == 1 and parts[0]:
                        # If we only have one part and it's not a name that should be combined
                        # (we've already handled those cases above)
                        import re
                        text = parts[0]

                        # Check if the text contains patterns typical of identifiers in Type1 documents
                        if re.search(r'[\*\#\+\-0-9]', text):
                            # This looks like an identifier, but we need a name to pair it with
                            # We'll skip it for now
                            pass
                        else:
                            # This is a name without an identifier that couldn't be combined
                            # We'll skip it instead of adding a placeholder
                            pass

        return extracted_data

# compare_with_ground_truth method removed as it should be a general utility function
