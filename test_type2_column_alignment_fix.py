#!/usr/bin/env python3
"""
Test script to verify the fix for type2 document column alignment issues.

This script processes a type2 document with OCR.space and checks if the columns are properly aligned.
"""
import os
import sys
import logging
import subprocess
import pandas as pd
import numpy as np
from pathlib import Path
import json
import shutil

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

# Define test parameters
TEST_PDF = "data/examples/type2-information-in-multiple-columns.pdf"
GROUND_TRUTH = "data/examples/type2-information-in-multiple-columns-manually-extracted-table-full.csv"
OUTPUT_DIR = Path("test_output/type2_column_alignment_fix")

def setup_test_environment():
    """Set up the test environment by creating necessary directories."""
    # Create output directory
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    logger.info(f"Set up test environment in {OUTPUT_DIR}")

def run_generate_subtables(pdf_path, page_num, output_dir):
    """
    Run the generate_subtables script.

    Args:
        pdf_path: Path to the PDF file
        page_num: Page number to process
        output_dir: Output directory

    Returns:
        Path to the output directory
    """
    # Create the output directory
    test_output_dir = Path(output_dir)
    test_output_dir.mkdir(parents=True, exist_ok=True)

    # Run the run_standalone.py script
    cmd = [
        "python3", "Subprojects/JORTPROC_GenerateSubtacles/run_standalone.py",
        str(pdf_path), str(page_num), "type2",
        "--output_dir", str(test_output_dir)
    ]

    logger.info(f"Running command: {' '.join(cmd)}")
    subprocess.run(cmd, check=True)

    return test_output_dir

def run_ocr_space(input_dir, output_file):
    """
    Run OCR.space on the generated subtables.

    Args:
        input_dir: Directory containing the subtable images
        output_file: Path to the output CSV file

    Returns:
        Path to the output CSV file
    """
    # Run the OCR.space script
    cmd = [
        "python3", "Subprojects/JORTPROC_OCRSpace/run_standalone.py",
        str(input_dir), "--headers", "NOMS & PRENOMS,CIN,ADRESSE",
        "--output_file", str(output_file)
    ]

    logger.info(f"Running command: {' '.join(cmd)}")
    subprocess.run(cmd, check=True)

    return output_file

def analyze_column_alignment(ocr_results_file):
    """
    Analyze the column alignment in the OCR results.

    Args:
        ocr_results_file: Path to the OCR results CSV file

    Returns:
        Dictionary with analysis results
    """
    # Read the OCR results
    df = pd.read_csv(ocr_results_file)

    # Count non-empty values in each column
    column_counts = {}
    for col in df.columns:
        # Count non-empty and non-NA values
        count = df[col].replace(['', 'NA', 'na', 'N/A', 'n/a', '#NA#', '#NA', 'NA#', '#NA*', '#NAH', '-'], np.nan).dropna().shape[0]
        column_counts[col] = count

    # Calculate the maximum difference between column counts
    max_diff = max(column_counts.values()) - min(column_counts.values())

    # Check if all columns have the same number of rows
    # For this test, we consider it aligned if the difference is small (<=5)
    # This is because some rows might have legitimate empty cells
    is_aligned = max_diff <= 5

    return {
        'column_counts': column_counts,
        'max_difference': max_diff,
        'is_aligned': is_aligned,
        'total_rows': len(df)
    }

def compare_with_ground_truth(ocr_results_file, ground_truth_file):
    """
    Compare the OCR results with the ground truth data.

    Args:
        ocr_results_file: Path to the OCR results CSV file
        ground_truth_file: Path to the ground truth CSV file

    Returns:
        Dictionary with comparison results
    """
    # Read the OCR results and ground truth
    ocr_df = pd.read_csv(ocr_results_file)
    gt_df = pd.read_csv(ground_truth_file)

    # Clean up column names to ensure they match
    ocr_df.columns = [col.strip() for col in ocr_df.columns]
    gt_df.columns = [col.strip() for col in gt_df.columns]

    # Ensure both dataframes have the same columns
    common_columns = list(set(ocr_df.columns) & set(gt_df.columns))

    # Calculate match statistics for each column
    column_stats = {}
    for col in common_columns:
        # Clean up values for comparison
        ocr_values = ocr_df[col].astype(str).str.strip().replace('', np.nan).dropna().tolist()
        gt_values = gt_df[col].astype(str).str.strip().replace('', np.nan).dropna().tolist()

        # Count matches
        matches = sum(1 for val in ocr_values if val in gt_values)

        # Calculate precision and recall
        precision = matches / len(ocr_values) if ocr_values else 0
        recall = matches / len(gt_values) if gt_values else 0
        f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

        column_stats[col] = {
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'ocr_count': len(ocr_values),
            'gt_count': len(gt_values),
            'matches': matches
        }

    # Calculate overall statistics
    overall_precision = sum(stats['precision'] for stats in column_stats.values()) / len(column_stats)
    overall_recall = sum(stats['recall'] for stats in column_stats.values()) / len(column_stats)
    overall_f1 = sum(stats['f1_score'] for stats in column_stats.values()) / len(column_stats)

    return {
        'column_stats': column_stats,
        'overall_precision': overall_precision,
        'overall_recall': overall_recall,
        'overall_f1': overall_f1
    }

def main():
    """Main entry point for the script."""
    # Set up the test environment
    setup_test_environment()

    # Run the test
    logger.info("=== Running test with fixed code ===")

    # Create a test-specific output directory
    test_dir = OUTPUT_DIR / "fixed"
    test_dir.mkdir(parents=True, exist_ok=True)

    # Run generate_subtables
    subtables_dir = run_generate_subtables(TEST_PDF, 1, test_dir)

    # Find the column files
    column_files = list(subtables_dir.glob("page1/page1_table1_column*_inverted_lined.png"))
    if not column_files:
        logger.error(f"No column files found in {subtables_dir}")
        return 1

    # Run OCR.space on the generated subtables
    ocr_output_file = test_dir / "ocr_results.csv"
    run_ocr_space(subtables_dir / "page1", ocr_output_file)

    # Analyze column alignment
    alignment_results = analyze_column_alignment(ocr_output_file)

    # Compare with ground truth
    comparison_results = compare_with_ground_truth(ocr_output_file, GROUND_TRUTH)

    # Combine results
    results = {
        'alignment': alignment_results,
        'comparison': comparison_results
    }

    # Save results
    with open(test_dir / "results.json", 'w') as f:
        json.dump(results, f, indent=2)

    logger.info(f"Test results saved to {test_dir / 'results.json'}")

    # Print a summary of the results
    logger.info("=== Test Results ===")
    logger.info(f"Aligned: {alignment_results['is_aligned']}, Max Difference: {alignment_results['max_difference']}")
    logger.info(f"F1 Score: {comparison_results['overall_f1']:.4f}")

    # Check if the test passed
    if alignment_results['is_aligned']:
        logger.info("✅ TEST PASSED: Columns are properly aligned")
    else:
        logger.info("❌ TEST FAILED: Columns are not properly aligned")

    return 0

if __name__ == "__main__":
    sys.exit(main())
