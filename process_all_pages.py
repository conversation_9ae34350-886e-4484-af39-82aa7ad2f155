#!/usr/bin/env python3
"""
Process all pages of a PDF file using the JortDataExtractor.

This script processes all pages of a PDF file using the JortDataExtractor subprojects.
"""
import argparse
import logging
import os
import sys
from pathlib import Path
import subprocess
from pdf2image import convert_from_path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def get_num_pages(pdf_path):
    """Get the number of pages in a PDF file."""
    try:
        # Convert the first page to get the total number of pages
        images = convert_from_path(pdf_path, first_page=1, last_page=1)
        # The total number of pages is stored in the PDF info
        info = convert_from_path(pdf_path, first_page=1, last_page=1, output_folder=None, fmt='jpg', 
                                 thread_count=1, userpw=None, use_cropbox=False, strict=False, 
                                 transparent=False, output_file=None, poppler_path=None, grayscale=False, 
                                 size=None, paths_only=False, use_pdftocairo=False, timeout=600, 
                                 hide_annotations=False, dpi=200, single_file=False, jpegopt=None, 
                                 pngopt=None, tiffopt=None, all_files=False, output_folder_path=None, 
                                 single_file_path=None, get_info_only=True)
        return info['Pages']
    except Exception as e:
        logger.error(f"Error getting number of pages: {e}")
        # Default to 1 page if we can't determine the number of pages
        return 1

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Process all pages of a PDF file using the JortDataExtractor")
    parser.add_argument("pdf_path", help="Path to the PDF file")
    parser.add_argument("-d", "--doc-type", help="Document type (type1, type2, type3, type4)", default="type1")
    parser.add_argument("--test-ocr", action="store_true", help="Use OCR.space API for processing")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--output", help="Output directory for extracted data", default="output")
    parser.add_argument("--api-key", default="K888957", help="OCR.space API key (only used if test-ocr is True)")

    args = parser.parse_args()

    # Set up logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

    try:
        # Get the number of pages in the PDF
        pdf_path = Path(args.pdf_path)
        num_pages = get_num_pages(pdf_path)
        logger.info(f"PDF has {num_pages} pages")

        # Process each page
        for page_num in range(1, num_pages + 1):
            logger.info(f"Processing page {page_num} of {num_pages}")
            
            # Build the command
            cmd = [
                "python3", "subprojects_main.py",
                str(pdf_path),
                "-d", args.doc_type,
                "--output", args.output,
                "--page", str(page_num),
                "--api-key", args.api_key
            ]
            
            if args.test_ocr:
                cmd.append("--test-ocr")
                
            if args.debug:
                cmd.append("--debug")
                
            # Run the command
            logger.info(f"Running command: {' '.join(cmd)}")
            subprocess.run(cmd, check=True)
            
            logger.info(f"Finished processing page {page_num}")
            
        logger.info(f"Finished processing all {num_pages} pages")
        
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
