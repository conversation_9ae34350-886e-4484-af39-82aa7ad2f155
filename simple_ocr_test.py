#!/usr/bin/env python3
import requests
import os
from pathlib import Path

# File to process
file_path = Path('pre-ocr-tables/page1_type1_sub_table_1_inverted_lined.png')
print(f"Processing file: {file_path}")
print(f"File exists: {file_path.exists()}")
print(f"File size: {file_path.stat().st_size / 1024:.1f} KB")

# API endpoint
url = 'https://api.ocr.space/parse/image'


# Prepare form data
with open(file_path, 'rb') as f:
    files = {
        'file': (file_path.name, f, 'image/png')
    }
    
    # Form data
    data = {
        'language': 'fre',
        'isOverlayRequired': 'true',
        'FileType': '.png',
        'IsCreateSearchablePDF': 'false',
        'isSearchablePdfHideTextLayer': 'true',
        'detectOrientation': 'false',
        'isTable': 'true',
        'OCREngine': '2',
        'scale': 'true',
        'url': ''
    }
    
    # Send request
    print("Sending request...")
    response = requests.post(url, headers=headers, files=files, data=data)
    
    # Print response
    print(f"Response status code: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        if 'ParsedResults' in result and result['ParsedResults']:
            for i, parsed_result in enumerate(result['ParsedResults']):
                if 'ParsedText' in parsed_result:
                    print(f"Result {i+1} text preview: {parsed_result['ParsedText'][:100]}...")
        else:
            print("No parsed results")
            print(f"Response: {response.text[:500]}...")
    else:
        print(f"Error: {response.text}")

print("Done")
