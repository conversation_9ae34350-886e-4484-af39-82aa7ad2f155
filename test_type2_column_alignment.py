#!/usr/bin/env python3
"""
Test script for type2 document column alignment with OCR.space.

This script tests the empty cell detection and #NA# filling for type2 documents
to ensure that all columns have the same number of rows after OCR processing.
"""
import os
import sys
import logging
import shutil
import subprocess
import pandas as pd
import numpy as np
import cv2
from pathlib import Path
import re
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

# Define test parameters
TEST_PDF = "data/examples/type2-information-in-multiple-columns.pdf"
GROUND_TRUTH = "data/examples/type2-information-in-multiple-columns-manually-extracted-table-full.csv"
OUTPUT_DIR = Path("test_output/type2_column_alignment_test")
RESULTS_FILE = OUTPUT_DIR / "test_results.json"

def setup_test_environment():
    """Set up the test environment by creating necessary directories."""
    # Create output directory
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    # Create a directory to store test results
    (OUTPUT_DIR / "results").mkdir(exist_ok=True)

    logger.info(f"Set up test environment in {OUTPUT_DIR}")

def run_generate_subtables(pdf_path, page_num, output_dir, empty_cell_threshold=0.022):
    """
    Run the generate_subtables script with a specific empty cell threshold.

    Args:
        pdf_path: Path to the PDF file
        page_num: Page number to process
        output_dir: Output directory
        empty_cell_threshold: Threshold for empty cell detection (default: 0.022)

    Returns:
        Path to the output directory
    """
    # Create a temporary copy of the generate_subtables.py file with modified threshold
    original_file = Path("Subprojects/JORTPROC_GenerateSubtacles/generate_subtables.py")
    temp_file = Path("Subprojects/JORTPROC_GenerateSubtacles/generate_subtables_temp.py")

    # Read the original file
    with open(original_file, 'r') as f:
        content = f.read()

    # Replace the empty cell threshold
    content = re.sub(
        r'if white_pixel_count < cell_area \* ([\d\.]+):  # Slightly more aggressive than 2%',
        f'if white_pixel_count < cell_area * {empty_cell_threshold}:  # Slightly more aggressive than 2%',
        content
    )

    # Write the modified file
    with open(temp_file, 'w') as f:
        f.write(content)

    # Create the output directory
    test_output_dir = Path(output_dir)
    test_output_dir.mkdir(parents=True, exist_ok=True)

    # Temporarily replace the original generate_subtables.py with our modified version
    backup_file = original_file.with_suffix('.py.bak')
    shutil.copy(original_file, backup_file)
    shutil.copy(temp_file, original_file)

    try:
        # Run the run_standalone.py script which uses generate_subtables.py
        cmd = [
            "python3", "Subprojects/JORTPROC_GenerateSubtacles/run_standalone.py",
            str(pdf_path), str(page_num), "type2",
            "--output_dir", str(test_output_dir)
        ]

        logger.info(f"Running command: {' '.join(cmd)}")
        subprocess.run(cmd, check=True)
    finally:
        # Restore the original file
        shutil.copy(backup_file, original_file)
        backup_file.unlink()
        temp_file.unlink()

    return test_output_dir

def run_ocr_space(input_dir, output_file):
    """
    Run OCR.space on the generated subtables.

    Args:
        input_dir: Directory containing the subtable images
        output_file: Path to the output CSV file

    Returns:
        Path to the output CSV file
    """
    # Run the OCR.space script
    cmd = [
        "python3", "Subprojects/JORTPROC_OCRSpace/run_standalone.py",
        str(input_dir), "--headers", "NOMS & PRENOMS,CIN,ADRESSE",
        "--output_file", str(output_file)
    ]

    logger.info(f"Running command: {' '.join(cmd)}")
    subprocess.run(cmd, check=True)

    return output_file

def analyze_column_alignment(ocr_results_file):
    """
    Analyze the column alignment in the OCR results.

    Args:
        ocr_results_file: Path to the OCR results CSV file

    Returns:
        Dictionary with analysis results
    """
    # Read the OCR results
    df = pd.read_csv(ocr_results_file)

    # Count non-empty values in each column
    column_counts = {}
    for col in df.columns:
        # Count non-empty and non-NA values
        count = df[col].replace(['', 'NA', 'na', 'N/A', 'n/a', '#NA#', '#NA', 'NA#'], np.nan).dropna().shape[0]
        column_counts[col] = count

    # Calculate the maximum difference between column counts
    max_diff = max(column_counts.values()) - min(column_counts.values())

    # Check if all columns have the same number of rows
    is_aligned = max_diff == 0

    return {
        'column_counts': column_counts,
        'max_difference': max_diff,
        'is_aligned': is_aligned,
        'total_rows': len(df)
    }

def compare_with_ground_truth(ocr_results_file, ground_truth_file):
    """
    Compare the OCR results with the ground truth data.

    Args:
        ocr_results_file: Path to the OCR results CSV file
        ground_truth_file: Path to the ground truth CSV file

    Returns:
        Dictionary with comparison results
    """
    # Read the OCR results and ground truth
    ocr_df = pd.read_csv(ocr_results_file)
    gt_df = pd.read_csv(ground_truth_file)

    # Clean up column names to ensure they match
    ocr_df.columns = [col.strip() for col in ocr_df.columns]
    gt_df.columns = [col.strip() for col in gt_df.columns]

    # Ensure both dataframes have the same columns
    common_columns = list(set(ocr_df.columns) & set(gt_df.columns))

    # Calculate match statistics for each column
    column_stats = {}
    for col in common_columns:
        # Clean up values for comparison
        ocr_values = ocr_df[col].astype(str).str.strip().replace('', np.nan).dropna().tolist()
        gt_values = gt_df[col].astype(str).str.strip().replace('', np.nan).dropna().tolist()

        # Count matches
        matches = sum(1 for val in ocr_values if val in gt_values)

        # Calculate precision and recall
        precision = matches / len(ocr_values) if ocr_values else 0
        recall = matches / len(gt_values) if gt_values else 0
        f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

        column_stats[col] = {
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'ocr_count': len(ocr_values),
            'gt_count': len(gt_values),
            'matches': matches
        }

    # Calculate overall statistics
    overall_precision = sum(stats['precision'] for stats in column_stats.values()) / len(column_stats)
    overall_recall = sum(stats['recall'] for stats in column_stats.values()) / len(column_stats)
    overall_f1 = sum(stats['f1_score'] for stats in column_stats.values()) / len(column_stats)

    return {
        'column_stats': column_stats,
        'overall_precision': overall_precision,
        'overall_recall': overall_recall,
        'overall_f1': overall_f1
    }

def run_test_with_threshold(threshold):
    """
    Run a complete test with a specific empty cell threshold.

    Args:
        threshold: Threshold for empty cell detection

    Returns:
        Dictionary with test results
    """
    logger.info(f"=== Running test with threshold {threshold} ===")

    # Create a test-specific output directory
    test_dir = OUTPUT_DIR / f"threshold_{threshold:.4f}"
    test_dir.mkdir(parents=True, exist_ok=True)

    # Run generate_subtables with the specified threshold
    subtables_dir = run_generate_subtables(TEST_PDF, 1, test_dir, threshold)

    # Find the column files
    column_files = list(subtables_dir.glob("page1/page1_table1_column*_inverted_lined.png"))
    if not column_files:
        logger.error(f"No column files found in {subtables_dir}")
        return None

    # Run OCR.space on the generated subtables
    ocr_output_file = test_dir / "ocr_results.csv"
    run_ocr_space(subtables_dir / "page1", ocr_output_file)

    # Analyze column alignment
    alignment_results = analyze_column_alignment(ocr_output_file)

    # Compare with ground truth
    comparison_results = compare_with_ground_truth(ocr_output_file, GROUND_TRUTH)

    # Combine results
    results = {
        'threshold': threshold,
        'alignment': alignment_results,
        'comparison': comparison_results
    }

    # Save results
    with open(test_dir / "results.json", 'w') as f:
        json.dump(results, f, indent=2)

    logger.info(f"Test results saved to {test_dir / 'results.json'}")

    return results

def main():
    """Main entry point for the script."""
    # Set up the test environment
    setup_test_environment()

    # Define thresholds to test
    thresholds = [0.015, 0.018, 0.020, 0.022, 0.025, 0.030]

    # Run tests with different thresholds
    all_results = {}
    for threshold in thresholds:
        results = run_test_with_threshold(threshold)
        if results:
            all_results[str(threshold)] = results

    # Save all results
    with open(RESULTS_FILE, 'w') as f:
        json.dump(all_results, f, indent=2)

    # Find the best threshold based on column alignment and F1 score
    best_threshold = None
    best_score = -1

    for threshold, results in all_results.items():
        # Calculate a combined score based on alignment and F1 score
        alignment_score = 1.0 if results['alignment']['is_aligned'] else 0.0
        f1_score = results['comparison']['overall_f1']

        # Weighted score (alignment is more important)
        combined_score = (0.7 * alignment_score) + (0.3 * f1_score)

        if combined_score > best_score:
            best_score = combined_score
            best_threshold = threshold

    logger.info(f"Best threshold: {best_threshold} with score {best_score:.4f}")

    # Print a summary of all results
    logger.info("=== Summary of Results ===")
    for threshold, results in all_results.items():
        alignment = results['alignment']
        comparison = results['comparison']
        logger.info(f"Threshold: {threshold}")
        logger.info(f"  Aligned: {alignment['is_aligned']}, Max Difference: {alignment['max_difference']}")
        logger.info(f"  F1 Score: {comparison['overall_f1']:.4f}")

    return 0

if __name__ == "__main__":
    sys.exit(main())
