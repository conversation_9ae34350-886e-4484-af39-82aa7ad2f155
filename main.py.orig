#!/usr/bin/env python3
"""Main entry point for the JortDataExtractor application."""

import argparse
import logging
import sys
from pathlib import Path
from jortextractor.core import JortPDFProcessor
from jortextractor.utils.config import load_config
from jortextractor.benchmarks.strict_comparison import <PERSON><PERSON>t<PERSON>om<PERSON><PERSON>

def main():
    """Main entry point for the application."""
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Extract data from Jort PDF documents.')
    parser.add_argument('input', help='Path to PDF file or directory containing PDF files')
    parser.add_argument('-o', '--output', help='Output directory for extracted data')
    parser.add_argument('-c', '--config', help='Path to configuration file', default='config/config.yaml')
    parser.add_argument('-d', '--doc-type', help='Document type (type1, type2, type3, type4)', default=None)
    parser.add_argument('-t', '--truth', help='Path to ground truth CSV for comparison (automatically triggers OCR.space comparison)')
    parser.add_argument('--test-ocr', action='store_true', help='[DEPRECATED] This flag is no longer needed as OCR.space comparison is automatically triggered when ground truth is provided')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    parser.add_argument('--save-tables', action='store_true', help='Save detected tables as images')
    parser.add_argument('--save-columns', action='store_true', help='Save detected columns as images')
    parser.add_argument('--one-line-per-subtable', action='store_true', help='Extract only one line from each sub-table (used for testing against ground truth)')
    parser.add_argument('--raw-ocr', action='store_true', help='Get raw OCR data without text processing for Type1 documents')

    args = parser.parse_args()

    # Set up logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

    # Update config with command line arguments
    config = load_config(args.config)
    if args.save_tables:
        config['save_pre_ocr_tables'] = True
    if args.save_columns:
        config['save_pre_ocr_columns'] = True
    if args.output:
        config['output_dir'] = args.output

    # Initialize processor with config
    try:
        # Initialize processor with config
        processor = JortPDFProcessor(args.config)

        # Process single file or directory
        input_path = Path(args.input)
        if input_path.is_file():
            # Process the PDF file
            print(f"Processing {input_path}...")

            # Process with ground truth if provided
            truth_path = None
            if args.truth:
                truth_file = Path(args.truth)
                if truth_file.exists():
                    truth_path = truth_file
                    print(f"Using ground truth file: {truth_file}")
                else:
                    print(f"Warning: Ground truth file {args.truth} not found")

            # For type1 documents, use our improved approach
            if args.doc_type == 'type1':
                print("Using simplified approach for type1 document")
                # Import required libraries
                import cv2
                import pytesseract
                import pandas as pd
                import glob
                import re
                import os
                import numpy as np
                import matplotlib.pyplot as plt

                # First, let's use the processor to generate the cropped tables
                # This will save the cropped tables to the pre-ocr-tables directory
                # Note: test_ocr flag is no longer needed as OCR.space comparison is triggered by ground truth
                processor.process_pdf(input_path, args.doc_type, None, test_ocr=False)

                # No text cleaning function needed anymore

                # Function to determine skew angle of an image
                def determine_skew(image):
                    """Determine the skew angle of an image.

                    Args:
                        image: The input image

                    Returns:
                        Skew angle in degrees
                    """
                    # Apply Canny edge detection
                    edges = cv2.Canny(image, 50, 150, apertureSize=3)

                    # Use Hough Line Transform to detect lines
                    lines = cv2.HoughLines(edges, 1, np.pi/180, 100)

                    # If no lines are detected, return 0
                    if lines is None:
                        return 0

                    # Calculate the angles of the lines
                    angles = []
                    for line in lines:
                        rho, theta = line[0]
                        # Convert theta to degrees and normalize to [-90, 90]
                        angle = np.degrees(theta) - 90
                        if angle < -45:
                            angle += 90
                        elif angle > 45:
                            angle -= 90
                        angles.append(angle)

                    # Use the median angle as the skew angle
                    if angles:
                        skew_angle = np.median(angles)
                        # Only correct if the skew is significant (more than 0.5 degrees)
                        if abs(skew_angle) < 0.5:
                            skew_angle = 0
                    else:
                        skew_angle = 0

                    return skew_angle

                # Function to detect table structure and add borders
                def detect_table_structure(image, img_path):
                    """Detect table structure and add borders to close the table.

                    Args:
                        image: The input image (grayscale or BGR)
                        img_path: Path to the image file (for saving debug images)

                    Returns:
                        Tuple containing:
                        - Image with detected lines drawn
                        - Column boundary position
                        - List of row boundary positions
                    """
                    # Make a copy to avoid modifying the original
                    img_copy = image.copy()

                    # Convert to grayscale if needed
                    if len(img_copy.shape) == 3:
                        gray = cv2.cvtColor(img_copy, cv2.COLOR_BGR2GRAY)
                    else:
                        gray = img_copy.copy()

                    # Apply binary thresholding for boundary detection only
                    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

                    # Create a copy of the original grayscale image for drawing lines
                    # This preserves the original image quality instead of using the binary version
                    lined_image = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)

                    # Get image dimensions
                    height, width = binary.shape

                    # 1. Detect column boundary (vertical line)
                    # Create horizontal projection (sum of white pixels in each column)
                    h_projection = np.sum(binary, axis=0)

                    # Smooth the projection to reduce noise
                    h_projection_smooth = np.convolve(h_projection, np.ones(15)/15, mode='same')

                    # Start from 3/4 of the width (assuming identifier column is on the right side)
                    start_search = int(width * 3/4)

                    # Find the minimum value in the projection between 1/4 and 3/4 of the width
                    min_value = float('inf')
                    min_index = start_search

                    for i in range(start_search, int(width * 1/4), -1):
                        if h_projection_smooth[i] < min_value:
                            min_value = h_projection_smooth[i]
                            min_index = i

                    # Set the column boundary at the minimum point
                    column_boundary = min_index

                    # 2. Detect row boundaries (horizontal lines)
                    # Detect horizontal lines using morphological operations
                    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (width // 10, 1))
                    horizontal_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)

                    # Find contours of horizontal lines
                    contours, _ = cv2.findContours(horizontal_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                    # Extract y-coordinates of horizontal lines
                    row_boundaries = []
                    for contour in contours:
                        x, y, w, h = cv2.boundingRect(contour)
                        # Only consider lines that span at least half the width
                        if w > width // 2:
                            # Use the middle of the line as the boundary
                            row_y = y + h // 2
                            row_boundaries.append(row_y)

                    # Sort row boundaries by y-coordinate
                    row_boundaries.sort()

                    # 3. Draw all lines to create a closed table with 2-pixel thickness
                    # Draw vertical column boundary line
                    cv2.line(lined_image, (column_boundary, 0), (column_boundary, height), (255, 255, 255), 2)

                    # Draw horizontal row boundary lines
                    for row_y in row_boundaries:
                        cv2.line(lined_image, (0, row_y), (width, row_y), (255, 255, 255), 2)

                    # Add borders to close the table (left, right, and bottom)
                    # Left border
                    cv2.line(lined_image, (0, 0), (0, height), (255, 255, 255), 2)

                    # Right border
                    cv2.line(lined_image, (width-2, 0), (width-2, height), (255, 255, 255), 2)

                    # Bottom border
                    cv2.line(lined_image, (0, height-2), (width, height-2), (255, 255, 255), 2)

                    # Save the lined image with high quality for better OCR results
                    lined_image_path = img_path.replace('.png', '_lined.png')
                    # Use PNG compression level 0 (no compression) for maximum quality
                    cv2.imwrite(lined_image_path, lined_image, [cv2.IMWRITE_PNG_COMPRESSION, 0])

                    print(f"Detected column boundary at x={column_boundary} and {len(row_boundaries)} row boundaries")
                    print(f"Added borders to close the table and saved high-quality image to {lined_image_path}")

                    return lined_image, column_boundary, row_boundaries

                # Function to process a single inverted subtable image
                def process_inverted_subtable(img_path):
                    print(f"Processing {img_path}")
                    # Read the image
                    img = cv2.imread(img_path)

                    # 1. Convert to grayscale if not already
                    if len(img.shape) == 3:
                        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                    else:
                        gray = img

                    # Get image dimensions
                    height, width = gray.shape

                    # 2. Apply Otsu's thresholding for global binarization
                    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

                    # 3. Invert the image (since we're already working with inverted images)
                    inverted = cv2.bitwise_not(gray)

                    # 4. Detect table structure and add borders
                    lined_image, column_boundary, row_boundaries = detect_table_structure(gray, img_path)

                    # 5. Perform OCR with optimized configuration based on Tesseract discussion
                    # PSM 4 = Assume a single column of text (better for table columns)
                    # OEM 3 = Default, based on what is available (use both Legacy and LSTM engines)
                    # Add specific table recognition parameters from Tesseract discussion
                    config = "--psm 4 --oem 3 -l eng+fra --dpi 400 -c preserve_interword_spaces=1 -c textord_tabfind_find_tables=1 -c textord_tablefind_recognize_tables=1 -c textord_min_linesize=2.5 -c textord_tabfind_vertical_text=1 -c textord_tabfind_force_vertical_text=0"

                    # Use the lined image for OCR instead of creating a separate preprocessed image
                    # This ensures consistency between local OCR and OCR.space
                    lined_image_path = img_path.replace('.png', '_lined.png')
                    lined_img = cv2.imread(lined_image_path)

                    # Convert to grayscale for OCR if needed
                    if len(lined_img.shape) == 3:
                        lined_gray = cv2.cvtColor(lined_img, cv2.COLOR_BGR2GRAY)
                    else:
                        lined_gray = lined_img.copy()

                    # Deskew the lined image if needed
                    from scipy import ndimage
                    angle = determine_skew(lined_gray)
                    if angle != 0:
                        lined_gray = ndimage.rotate(lined_gray, angle, reshape=False, mode='constant', cval=255)

                    # Get bounding boxes for each word using the lined image
                    boxes = pytesseract.image_to_data(lined_gray, config=config, output_type=pytesseract.Output.DICT)

                    # Group words by row using the detected row boundaries
                    rows_text = {}

                    # Add top of image as first boundary and bottom as last boundary
                    all_boundaries = [0] + row_boundaries + [height]

                    # Create row ranges (start_y, end_y) for each row
                    row_ranges = []
                    for i in range(len(all_boundaries) - 1):
                        row_ranges.append((all_boundaries[i], all_boundaries[i+1]))

                    # Initialize rows_text with empty strings for each row
                    for i in range(len(row_ranges)):
                        rows_text[i] = ""

                    # Assign each word to the appropriate row based on its y-coordinate
                    for i in range(len(boxes['text'])):
                        # Skip empty text
                        if not boxes['text'][i].strip():
                            continue

                        # Get word position (y-position only needed for row detection)
                        y = boxes['top'][i] + boxes['height'][i] // 2  # Use middle of word for y-position

                        # Find which row this word belongs to
                        for row_idx, (start_y, end_y) in enumerate(row_ranges):
                            if start_y <= y < end_y:
                                # Add space before word if not first word in row
                                if rows_text[row_idx]:
                                    rows_text[row_idx] += " "
                                rows_text[row_idx] += boxes['text'][i]
                                break

                    # Extract raw text data with column separation
                    data = []

                    # Process each row
                    for row_idx, row_text in rows_text.items():
                        # Skip empty rows and header rows
                        if not row_text.strip() or "Nom et prénom" in row_text or "Identifiant" in row_text:
                            continue

                        if args.raw_ocr:
                            # For raw OCR, just use the entire row text as the name
                            # This preserves the original OCR output without manipulation
                            data.append({'Nom et prénom': row_text, 'Identifiant': ''})
                        else:
                            # Use the column boundary to separate name and identifier
                            # The boundary is in pixels, but we need to estimate the character position
                            # Assuming average character width is about 10-12 pixels
                            char_width = 12
                            char_position = int(column_boundary / char_width)

                            # Ensure char_position is within the line length
                            if char_position >= len(row_text):
                                char_position = max(0, len(row_text) - 1)

                            # Find the nearest space to the right of the estimated position
                            # This helps ensure we don't split in the middle of a word
                            space_pos = row_text.find(' ', char_position)
                            if space_pos == -1:  # No space found to the right
                                # Try to find a space to the left
                                left_space = row_text.rfind(' ', 0, char_position)
                                if left_space != -1:
                                    split_pos = left_space + 1  # Split after the space
                                else:
                                    # No spaces at all, use the character position directly
                                    split_pos = char_position
                            else:
                                split_pos = space_pos + 1  # Split after the space

                            # Split the line into name and identifier
                            name_part = row_text[:split_pos].strip()
                            id_part = row_text[split_pos:].strip()

                            # Extract only the numbers from the identifier and format with "*****" prefix
                            id_numbers = ''.join(c for c in id_part if c.isdigit())
                            if id_numbers:
                                # Limit to 3 digits if there are more
                                if len(id_numbers) > 3:
                                    id_numbers = id_numbers[:3]
                                # Ensure we have exactly 3 digits by padding with zeros if needed
                                elif len(id_numbers) < 3:
                                    id_numbers = id_numbers.zfill(3)

                                # Format with "*****" prefix
                                formatted_id = f"*****{id_numbers}"
                            else:
                                # If no numbers found, use a default placeholder
                                formatted_id = "*****000"

                            # Add the separated data
                            data.append({'Nom et prénom': name_part, 'Identifiant': formatted_id})

                    return data

                # Find all pages with subtables
                all_pages = set()
                for file in os.listdir('pre-ocr-tables'):
                    if file.startswith('page') and 'type1_sub_table' in file and 'inverted.png' in file:
                        page_num = int(re.search(r'page(\d+)', file).group(1))
                        all_pages.add(page_num)

                # Sort pages to maintain order
                all_pages = sorted(list(all_pages))
                print(f"Found subtables on {len(all_pages)} pages")

                # Process each page in order
                all_data = []
                for page_num in all_pages:
                    # Find all inverted subtable images for this page
                    pattern = f'pre-ocr-tables/page{page_num}_type1_sub_table_*_inverted.png'
                    sub_table_images = glob.glob(pattern)

                    # Sort to ensure consistent order
                    sub_table_images.sort(key=lambda x: int(re.search(r'sub_table_(\d+)', x).group(1)))

                    print(f"Found {len(sub_table_images)} inverted sub-table images on page {page_num}")

                    # Process each subtable and append results
                    for img_path in sub_table_images:
                        subtable_data = process_inverted_subtable(img_path)

                        # If one-line-per-subtable flag is set, only take the first line from each subtable
                        if args.one_line_per_subtable and subtable_data:
                            print(f"One-line-per-subtable flag is set. Using only the first line from {img_path}")
                            all_data.append(subtable_data[0])
                        else:
                            all_data.extend(subtable_data)

                # Create DataFrame
                df = pd.DataFrame(all_data)

                # Save to CSV without headers as requested
                output_dir = Path(args.output or processor.config.get("output_dir", "output"))
                output_dir.mkdir(parents=True, exist_ok=True)
                output_path = output_dir / f"{input_path.stem}.csv"
                df.to_csv(output_path, index=False, header=False)

                # Create result dictionary
                result = {
                    'extracted_data': df,
                    'formatted_data': df,
                    'output_path': str(output_path),
                    'status': 'success'
                }

                # If we have ground truth, compare our results with it
                if truth_path:
                    print(f"Comparing with ground truth: {truth_path}")

                    # Read ground truth data
                    gt_df = pd.read_csv(truth_path)

                    # Skip header row if present
                    if "Nom et prénom" in gt_df.columns:
                        gt_data = gt_df[["Nom et prénom", "Identifiant"]]
                    else:
                        # Assume first two columns are name and identifier
                        gt_df.columns = ["Nom et prénom", "Identifiant"] + list(gt_df.columns[2:])
                        gt_data = gt_df[["Nom et prénom", "Identifiant"]]

                    # Clean ground truth data
                    gt_data["Nom et prénom"] = gt_data["Nom et prénom"].apply(lambda x: str(x).strip())

                    # Clean and standardize identifiers in ground truth data
                    def standardize_identifier(id_str):
                        # Convert to string and strip whitespace
                        id_str = str(id_str).strip()

                        # Remove any spaces between asterisks and numbers
                        id_str = re.sub(r'(\*+)\s+(\d+)', r'\1\2', id_str)

                        # Extract only the digits
                        id_numbers = ''.join(c for c in id_str if c.isdigit())

                        if id_numbers:
                            # Limit to 3 digits if there are more
                            if len(id_numbers) > 3:
                                id_numbers = id_numbers[:3]
                            # Ensure we have exactly 3 digits by padding with zeros if needed
                            elif len(id_numbers) < 3:
                                id_numbers = id_numbers.zfill(3)

                            # Format with "*****" prefix (no space)
                            return f"*****{id_numbers}"
                        else:
                            # If no numbers found, use a default placeholder
                            return "*****000"

                    gt_data["Identifiant"] = gt_data["Identifiant"].apply(standardize_identifier)

                    # Remove empty rows
                    gt_data = gt_data[gt_data["Nom et prénom"].str.strip() != ""]

                    # Use our new strict comparison logic
                    print("\n=== Using Strict Comparison Logic ===")

                    # Create a StrictComparison instance with default settings (case-insensitive, spaces and punctuation considered)
                    strict_comparator = StrictComparison(case_sensitive=False, ignore_spaces=False, ignore_punctuation=False)

                    # Compare the DataFrames
                    comparison_result = strict_comparator.compare_dataframes(df, gt_data)

                    # Generate and print a detailed report
                    report = strict_comparator.generate_report(comparison_result)
                    print(report)

                    # Save the report to a file
                    output_dir = Path(args.output or processor.config.get("output_dir", "output"))
                    output_dir.mkdir(parents=True, exist_ok=True)
                    report_path = output_dir / f"{input_path.stem}_strict_comparison_report.txt"
                    with open(report_path, 'w') as f:
                        f.write(report)
                    print(f"Saved strict comparison report to {report_path}")

                    # Extract metrics for the result dictionary
                    precision = comparison_result['precision'] * 100
                    recall = comparison_result['recall'] * 100
                    f1_score = comparison_result['f1_score'] * 100
                    exact_matches = comparison_result['exact_matches']
                    # Note: total_gt is available in comparison_result['total_gt'] if needed
                    total_extracted = comparison_result['total_ocr']

                    # Count Levenshtein Distance 1 matches
                    levenshtein_dist_1 = 0
                    for error in comparison_result['errors']:
                        if 'levenshtein_distance' in error and error['levenshtein_distance'] == 1:
                            levenshtein_dist_1 += 1

                    # Add metrics to result
                    result.update({
                        'precision': precision,
                        'recall': recall,
                        'f1_score': f1_score,
                        'matched': f"{exact_matches}/{total_extracted} extracted",
                        'levenshtein_dist_1': f"{levenshtein_dist_1}/{total_extracted}",
                        'strict_comparison': comparison_result
                    })

                    # Print comparison results
                    print("\n=== Local OCR Results (Strict Comparison) ===")
                    print(f"Precision: {precision:.1f}%")
                    print(f"Recall: {recall:.1f}%")
                    print(f"F1 Score: {f1_score:.1f}%")
                    print(f"Exact Matches: {exact_matches}/{total_extracted} extracted")
                    print(f"Levenshtein Distance 1: {levenshtein_dist_1}/{total_extracted} extracted")

                    # If we have ground truth, also compare with OCR.space
                    if truth_path:
                        print("\nComparing with OCR.space...")

                        # Find all inverted lined subtable images for OCR.space
                        lined_images = []
                        for page_num in all_pages:
                            pattern = f'pre-ocr-tables/page{page_num}_type1_sub_table_*_inverted_lined.png'
                            page_images = glob.glob(pattern)
                            page_images.sort(key=lambda x: int(re.search(r'sub_table_(\d+)', x).group(1)))
                            lined_images.extend(page_images)

                        # Use the processor to get OCR.space results
                        ocr_space_result = processor._process_type1_with_ocr_space(
                            [Path(img_path) for img_path in lined_images],
                            1  # Assuming page 1
                        )

                        # Create DataFrame from OCR.space results
                        ocr_space_data = []
                        for item in ocr_space_result:
                            if 'text' in item and 'identifier' in item:
                                # Extract only the numbers from the identifier and format with "*****" prefix
                                id_part = item['identifier']

                                # Remove any spaces between asterisks and numbers if they still exist
                                import re
                                id_part = re.sub(r'(\*+)\s+(\d+)', r'\1\2', id_part)

                                # For OCR.space, we want to preserve the original ID format as much as possible
                                # The raw OCR.space response already has the correct format "*****XX" or "*****XXX"
                                # We just need to ensure it's valid and handle edge cases

                                # Check if the ID already has the correct format (5 asterisks followed by numbers)
                                if re.match(r'\*{5}\d+', id_part):
                                    # Already in the correct format, use as is
                                    formatted_id = id_part
                                else:
                                    # Extract only the digits
                                    id_numbers = ''.join(c for c in id_part if c.isdigit())

                                    if id_numbers:
                                        # Limit to 3 digits if there are more
                                        if len(id_numbers) > 3:
                                            id_numbers = id_numbers[:3]
                                        # No need to pad with zeros - keep the original format

                                        # Format with "*****" prefix (no space)
                                        formatted_id = f"*****{id_numbers}"
                                    else:
                                        # If no numbers found, use a default placeholder
                                        formatted_id = "*****000"

                                ocr_space_data.append({
                                    'Nom et prénom': item['text'],
                                    'Identifiant': formatted_id
                                })

                        # Create DataFrame from OCR.space results
                        ocr_space_df = pd.DataFrame(ocr_space_data)

                        # Deduplicate entries based on both name and identifier
                        # This will keep only the first occurrence of each unique name-identifier pair
                        ocr_space_df = ocr_space_df.drop_duplicates(subset=['Nom et prénom', 'Identifiant'])

                        # Save OCR.space results
                        ocr_space_output_path = output_dir / f"{input_path.stem}_ocr_space.csv"
                        ocr_space_df.to_csv(ocr_space_output_path, index=False, header=False)

                        # Use our strict comparison logic for OCR.space results
                        print("\n=== Using Strict Comparison Logic for OCR.space Results ===")

                        # Create a StrictComparison instance with the same settings as before
                        ocr_space_comparator = StrictComparison(case_sensitive=False, ignore_spaces=False, ignore_punctuation=False)

                        # Compare the DataFrames
                        ocr_space_comparison = ocr_space_comparator.compare_dataframes(ocr_space_df, gt_data)

                        # Generate and print a detailed report
                        ocr_space_report = ocr_space_comparator.generate_report(ocr_space_comparison)
                        print(ocr_space_report)

                        # Save the report to a file
                        ocr_space_report_path = output_dir / f"{input_path.stem}_ocr_space_strict_comparison_report.txt"
                        with open(ocr_space_report_path, 'w') as f:
                            f.write(ocr_space_report)
                        print(f"Saved OCR.space strict comparison report to {ocr_space_report_path}")

                        # Extract metrics for the result dictionary
                        ocr_space_precision = ocr_space_comparison['precision'] * 100
                        ocr_space_recall = ocr_space_comparison['recall'] * 100
                        ocr_space_f1 = ocr_space_comparison['f1_score'] * 100
                        ocr_space_exact_matches = ocr_space_comparison['exact_matches']
                        ocr_space_total = ocr_space_comparison['total_ocr']

                        # Count Levenshtein Distance 1 matches for OCR.space
                        ocr_space_levenshtein_dist_1 = 0
                        for error in ocr_space_comparison['errors']:
                            if 'levenshtein_distance' in error and error['levenshtein_distance'] == 1:
                                ocr_space_levenshtein_dist_1 += 1

                        # Add OCR.space metrics to result
                        result['ocr_space_comparison'] = {
                            'precision': ocr_space_precision,
                            'recall': ocr_space_recall,
                            'f1_score': ocr_space_f1,
                            'matched': f"{ocr_space_exact_matches}/{ocr_space_total}",
                            'levenshtein_dist_1': f"{ocr_space_levenshtein_dist_1}/{ocr_space_total}",
                            'output_path': str(ocr_space_output_path),
                            'report_path': str(ocr_space_report_path)
                        }

                        # Store the full comparison result for later use
                        result['ocr_space_strict_comparison'] = ocr_space_comparison

                        # Print OCR.space comparison results
                        print("\n=== OCR.space Results (Strict Comparison) ===")
                        print(f"Precision: {ocr_space_precision:.1f}%")
                        print(f"Recall: {ocr_space_recall:.1f}%")
                        print(f"F1 Score: {ocr_space_f1:.1f}%")
                        print(f"Exact Matches: {ocr_space_exact_matches}/{ocr_space_total}")
                        print(f"Levenshtein Distance 1: {ocr_space_levenshtein_dist_1}/{ocr_space_total}")
                        print(f"Output saved to {ocr_space_output_path}")
            else:
                # Use the standard processor for other document types or when testing against ground truth
                result = processor.process_pdf(input_path, args.doc_type, truth_path, test_ocr=args.test_ocr)

            # Get output directory from config or command line
            output_dir = args.output or processor.config.get("output_dir", "output")
            output_path = Path(output_dir)

            # Print results
            if 'status' in result and result['status'] == 'success':
                print(f"Successfully processed {input_path}")
                print(f"Output saved to {output_path}")

                # Print comparison results if available
                if 'precision' in result:
                    print("\n=== Local OCR Results ===")
                    print(f"Precision: {result['precision']}%")
                    print(f"Recall: {result['recall']}%")
                    print(f"F1 Score: {result['f1_score']}%")
                    print(f"Matched: {result['matched']}")

                    # Add Levenshtein Distance 1 count if available
                    if 'strict_comparison' in result:
                        levenshtein_dist_1 = 0
                        for error in result['strict_comparison']['errors']:
                            if 'levenshtein_distance' in error and error['levenshtein_distance'] == 1:
                                levenshtein_dist_1 += 1
                        total_extracted = result['strict_comparison'].get('total_ocr', 0)
                        print(f"Levenshtein Distance 1: {levenshtein_dist_1}/{total_extracted}")

                    # Print OCR.space comparison results if available
                    if 'ocr_space_comparison' in result:
                        print("\n=== OCR.space Results ===")
                        ocr_space = result['ocr_space_comparison']
                        print(f"Precision: {ocr_space['precision']}%")
                        print(f"Recall: {ocr_space['recall']}%")
                        print(f"F1 Score: {ocr_space['f1_score']}%")
                        print(f"Matched: {ocr_space['matched']}")

                        # Add Levenshtein Distance 1 count for OCR.space if available
                        if 'ocr_space_strict_comparison' in result:
                            ocr_space_levenshtein_dist_1 = 0
                            for error in result['ocr_space_strict_comparison']['errors']:
                                if 'levenshtein_distance' in error and error['levenshtein_distance'] == 1:
                                    ocr_space_levenshtein_dist_1 += 1
                            ocr_space_total = result['ocr_space_strict_comparison'].get('total_ocr', 0)
                            print(f"Levenshtein Distance 1: {ocr_space_levenshtein_dist_1}/{ocr_space_total}")

                        print(f"Output saved to {result.get('ocr_space_output_path', 'N/A')}")

                        # Print comparison between local OCR and OCR.space
                        print("\n=== Comparison Summary ===")
                        local_f1 = result['f1_score']
                        ocr_space_f1 = ocr_space['f1_score']
                        diff = local_f1 - ocr_space_f1
                        if diff > 0:
                            print(f"Local OCR outperforms OCR.space by {abs(diff):.1f}% F1 score")
                        elif diff < 0:
                            print(f"OCR.space outperforms Local OCR by {abs(diff):.1f}% F1 score")
                        else:
                            print("Local OCR and OCR.space have identical F1 scores")
            else:
                print(f"Error processing {input_path}: {result.get('message', 'Unknown error')}")

        elif input_path.is_dir():
            # Process all PDF files in the directory
            pdf_files = list(input_path.glob('*.pdf'))
            print(f"Found {len(pdf_files)} PDF files in {input_path}")

            for pdf_file in pdf_files:
                print(f"Processing {pdf_file}...")
                result = processor.process_pdf(pdf_file, args.doc_type, None, test_ocr=args.test_ocr)

                if 'status' in result and result['status'] == 'success':
                    print(f"Successfully processed {pdf_file}")
                else:
                    print(f"Error processing {pdf_file}: {result.get('message', 'Unknown error')}")

        else:
            print(f"Error: {input_path} is not a valid file or directory")

    except Exception as e:
        print(f"Error: {e}")
        return 1

    return 0

if __name__ == '__main__':
    sys.exit(main())
