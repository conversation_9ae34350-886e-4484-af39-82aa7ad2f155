#!/usr/bin/env python3
"""
Comprehensive test script for type2 document processing.
Tests both successful processing and error handling for column mismatch.
"""
import os
import sys
import logging
import shutil
import numpy as np
import cv2
from pathlib import Path

# Add the subproject to the Python path
sys.path.append(str(Path("Subprojects/JORTPROC_OCRSpace")))

# Import from the subproject
from ocr_space import main as ocr_space_main

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def create_test_data_success():
    """Create test data for successful processing."""
    # Create test directory
    test_dir = Path("test_output/type2_test/success_case")
    test_dir.mkdir(parents=True, exist_ok=True)

    # Copy the original column files
    src_dir = Path("test_output/type2_test/subtables_columns/page1")

    # Copy all three columns
    for i in range(1, 4):
        shutil.copy(
            src_dir / f"page1_table1_column{i}_inverted_lined.png",
            test_dir / f"page1_table1_column{i}_inverted_lined.png"
        )

    logger.info(f"Created test data for success case in {test_dir}")
    return test_dir

def create_test_data_error():
    """Create test data with mismatched column counts."""
    # Create test directory
    test_dir = Path("test_output/type2_test/error_case")
    test_dir.mkdir(parents=True, exist_ok=True)

    # Copy the original column files for columns 1 and 2
    src_dir = Path("test_output/type2_test/subtables_columns/page1")

    # Copy column 1 (names)
    shutil.copy(
        src_dir / "page1_table1_column1_inverted_lined.png",
        test_dir / "page1_table1_column1_inverted_lined.png"
    )

    # Copy column 2 (CIN)
    shutil.copy(
        src_dir / "page1_table1_column2_inverted_lined.png",
        test_dir / "page1_table1_column2_inverted_lined.png"
    )

    # Create a small image with just a few rows for column 3
    img = np.ones((300, 200), np.uint8) * 255

    # Add some text to the image
    cv2.putText(img, "ADRESSE", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Address 1", (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Address 2", (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Address 3", (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Address 4", (10, 190), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Address 5", (10, 230), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

    # Invert the image (black text on white background -> white text on black background)
    img = cv2.bitwise_not(img)

    # Save the image
    cv2.imwrite(str(test_dir / "page1_table1_column3_inverted_lined.png"), img)

    logger.info(f"Created test data for error case in {test_dir}")
    return test_dir

def test_success_case():
    """Test successful processing of type2 document."""
    logger.info("=== Testing Success Case ===")

    # Create test data
    test_dir = create_test_data_success()

    # Define output file
    output_file = Path("test_output/type2_test/success_case_results.csv")

    try:
        # Run OCR.space on the test data
        logger.info(f"Running OCR.space on {test_dir}")
        ocr_space_main(
            input_dir=str(test_dir),
            headers="NOMS & PRENOMS,CIN,ADRESSE",
            output_file=str(output_file)
        )

        # Check if output file was created
        if output_file.exists():
            logger.info(f"Output file created: {output_file}")
            with open(output_file, 'r') as f:
                lines = f.readlines()
                logger.info(f"Output file has {len(lines)} lines")
                logger.info(f"First few lines: {lines[:5]}")

            # We don't expect an error file, but we don't fail if one exists
            # since we now just log warnings for column mismatches

            return True
        else:
            logger.error(f"Output file not created: {output_file}")
            return False
    except Exception as e:
        logger.error(f"Error in success case test: {e}")
        return False

def test_mismatch_case():
    """Test handling of column mismatch."""
    logger.info("=== Testing Column Mismatch Case ===")

    # Create test data
    test_dir = create_test_data_error()

    # Define output file
    output_file = Path("test_output/type2_test/mismatch_case_results.csv")

    try:
        # Run OCR.space on the test data
        logger.info(f"Running OCR.space on {test_dir}")
        ocr_space_main(
            input_dir=str(test_dir),
            headers="NOMS & PRENOMS,CIN,ADRESSE",
            output_file=str(output_file)
        )

        # Check if output file was created
        if output_file.exists():
            logger.info(f"Output file created: {output_file}")
            with open(output_file, 'r') as f:
                lines = f.readlines()
                logger.info(f"Output file has {len(lines)} lines")
                logger.info(f"First few lines: {lines[:5]}")

            # Check if the output file has data (not just headers)
            if len(lines) > 1:
                logger.info("Output file contains data as expected")
                return True
            else:
                logger.error("Output file contains only headers")
                return False
        else:
            logger.error(f"Output file not created: {output_file}")
            return False
    except Exception as e:
        logger.error(f"Error in mismatch case test: {e}")
        return False

def main():
    """Main entry point for the test script."""
    # Test success case
    success_result = test_success_case()

    # Test mismatch case
    mismatch_result = test_mismatch_case()

    # Print summary
    logger.info("=== Test Summary ===")
    logger.info(f"Success Case: {'PASSED' if success_result else 'FAILED'}")
    logger.info(f"Mismatch Case: {'PASSED' if mismatch_result else 'FAILED'}")

    # Return overall result
    return success_result and mismatch_result

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
