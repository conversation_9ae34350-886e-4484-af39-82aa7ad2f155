#!/usr/bin/env python3
"""
JortDataExtractor - Subprojects Main Program

This program extracts data from Tunisian Official Gazette (JORT) PDFs.
It uses the following subprojects:
- JORTPROC-GenerateSubtables: Transforms a PDF page into subtables
- JORTPROC-OCRLoc: Processes OCR on images using local OCR code
- JORTPROC-OCRSpace: Processes OCR on images using OCR.space API
"""
import argparse
import logging
import os
import sys
from pathlib import Path
import pandas as pd
from pdf2image import convert_from_path

# Import subprojects
from Subprojects.JORTPROC_GenerateSubtacles.generate_subtables import generate_subtables
from Subprojects.JORTPROC_OCRLoc.ocr_local import process_ocr_local
from Subprojects.JORTPROC_OCRSpace.ocr_space import process_ocr_space

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def process_pdf(pdf_path: str, document_type: str, output_dir: str,
                headers: str, use_ocr_space: bool = False, api_key: str = 'K888957',
                page_num: int = None) -> str:
    """
    Process a PDF page to extract data.

    Args:
        pdf_path: Path to the PDF file
        document_type: Type of document (e.g., 'type1', 'type2')
        output_dir: Directory to save the output files
        headers: Comma-separated list of headers for the CSV file
        use_ocr_space: Whether to use OCR.space API instead of local OCR
        api_key: OCR.space API key (only used if use_ocr_space is True)
        page_num: Page number to process (1-based)

    Returns:
        Path to the output CSV file
    """
    try:
        # Convert paths to Path objects
        pdf_path = Path(pdf_path)
        output_dir = Path(output_dir)

        # Create output directory if it doesn't exist
        output_dir.mkdir(parents=True, exist_ok=True)

        # Process the specified page
        if page_num is None:
            page_num = 1  # Default to page 1 if not specified

        logger.info(f"Processing page {page_num} of {pdf_path}")

        # Step 1: Generate subtables from the PDF page
        logger.info(f"Step 1: Generating subtables from {pdf_path}, page {page_num}")
        subtables_dir = generate_subtables(pdf_path, page_num, document_type, output_dir / f"subtables/page{page_num}")
        logger.info(f"Generated subtables in {subtables_dir}")

        # Step 2: Process OCR on the subtables
        if use_ocr_space:
            logger.info(f"Step 2: Processing OCR using OCR.space API")
            output_file = output_dir / f"{pdf_path.stem}_page{page_num}_ocr_space.csv"
            process_ocr_space(subtables_dir, headers, output_file, api_key)
        else:
            logger.info(f"Step 2: Processing OCR using local OCR")
            output_file = output_dir / f"{pdf_path.stem}_page{page_num}_ocr_local.csv"
            process_ocr_local(subtables_dir, headers, output_file)

        logger.info(f"Successfully processed PDF page {page_num} and saved results to {output_file}")
        return str(output_file)
    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        raise

def get_num_pages(pdf_path):
    """Get the number of pages in a PDF file."""
    try:
        # Use pdf2image's pdfinfo_from_path to get PDF info
        from pdf2image import pdfinfo_from_path
        info = pdfinfo_from_path(pdf_path)
        return info["Pages"]
    except Exception as e:
        logger.error(f"Error getting number of pages: {e}")
        # Default to 1 page if we can't determine the number of pages
        return 1

def main():
    """Main entry point for the program."""
    parser = argparse.ArgumentParser(description="Extract data from Tunisian Official Gazette (JORT) PDFs")
    parser.add_argument("input", help="Path to PDF file or directory containing PDF files")
    parser.add_argument("-o", "--output", help="Output directory for extracted data", default="output")
    parser.add_argument("-d", "--doc-type", help="Document type (type1, type2, type3, type4)", default=None)
    parser.add_argument("--test-ocr", action="store_true", help="Use OCR.space API for comparison")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--page", type=int, default=None, help="Specific page number to process (1-based). If not specified, all pages will be processed.")
    parser.add_argument("--headers", default="Nom et prénom,Identifiant", help="Comma-separated list of headers for the CSV file")
    parser.add_argument("--api-key", default="K888957", help="OCR.space API key (only used if test-ocr is True)")

    args = parser.parse_args()

    # Set up logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

    try:
        # Process single file or directory
        input_path = Path(args.input)

        # Check if document type is specified
        if not args.doc_type:
            logger.error("Document type is required. Please specify with -d or --doc-type")
            sys.exit(1)

        if input_path.is_file():
            # Process the PDF file
            logger.info(f"Processing {input_path}...")

            # Get the number of pages in the PDF
            num_pages = get_num_pages(input_path)
            logger.info(f"PDF has {num_pages} pages")

            # If a specific page is requested, process only that page
            if args.page is not None:
                if args.page < 1 or args.page > num_pages:
                    logger.error(f"Invalid page number: {args.page}. PDF has {num_pages} pages.")
                    sys.exit(1)

                logger.info(f"Processing page {args.page} of {num_pages}")
                output_file = process_pdf(
                    str(input_path),
                    args.doc_type,
                    args.output,
                    args.headers,
                    args.test_ocr,
                    args.api_key,
                    args.page
                )
                logger.info(f"Output saved to {output_file}")
            else:
                # Process all pages
                logger.info(f"Processing all {num_pages} pages")
                output_files = []

                for page_num in range(1, num_pages + 1):
                    logger.info(f"Processing page {page_num} of {num_pages}")
                    output_file = process_pdf(
                        str(input_path),
                        args.doc_type,
                        args.output,
                        args.headers,
                        args.test_ocr,
                        args.api_key,
                        page_num
                    )
                    output_files.append(output_file)
                    logger.info(f"Page {page_num} output saved to {output_file}")

                logger.info(f"Processed all {num_pages} pages. Output files: {', '.join(output_files)}")

        elif input_path.is_dir():
            # Process all PDF files in the directory
            pdf_files = list(input_path.glob('*.pdf'))
            logger.info(f"Found {len(pdf_files)} PDF files in {input_path}")

            for pdf_file in pdf_files:
                logger.info(f"Processing {pdf_file}...")

                # Get the number of pages in the PDF
                num_pages = get_num_pages(pdf_file)
                logger.info(f"PDF has {num_pages} pages")

                # If a specific page is requested, process only that page
                if args.page is not None:
                    if args.page < 1 or args.page > num_pages:
                        logger.warning(f"Invalid page number: {args.page}. PDF has {num_pages} pages. Skipping file.")
                        continue

                    logger.info(f"Processing page {args.page} of {num_pages}")
                    output_file = process_pdf(
                        str(pdf_file),
                        args.doc_type,
                        args.output,
                        args.headers,
                        args.test_ocr,
                        args.api_key,
                        args.page
                    )
                    logger.info(f"Output saved to {output_file}")
                else:
                    # Process all pages
                    logger.info(f"Processing all {num_pages} pages")
                    output_files = []

                    for page_num in range(1, num_pages + 1):
                        logger.info(f"Processing page {page_num} of {num_pages}")
                        output_file = process_pdf(
                            str(pdf_file),
                            args.doc_type,
                            args.output,
                            args.headers,
                            args.test_ocr,
                            args.api_key,
                            page_num
                        )
                        output_files.append(output_file)
                        logger.info(f"Page {page_num} output saved to {output_file}")

                    logger.info(f"Processed all {num_pages} pages of {pdf_file}. Output files: {', '.join(output_files)}")
        else:
            logger.error(f"Input path {input_path} does not exist or is not a file or directory")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
