#!/usr/bin/env python3
"""
Script to visualize the column boundaries in type2 documents.
"""
import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def visualize_column_boundaries(image_path, output_path=None):
    """Visualize the column boundaries in an image."""
    # Load the image
    img = cv2.imread(str(image_path))
    if img is None:
        logger.error(f"Failed to load image: {image_path}")
        return

    # Get the image dimensions
    height, width = img.shape[:2]

    # Create a copy of the image for visualization
    vis_img = img.copy()

    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Apply thresholding to get binary image
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # Detect vertical lines
    # Use a larger kernel for more robust vertical line detection
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, int(binary.shape[0] / 20)))
    vertical_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel, iterations=3)

    # Dilate the lines slightly to connect any broken segments
    dilate_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 5))
    vertical_lines = cv2.dilate(vertical_lines, dilate_kernel, iterations=1)

    # Find contours of vertical lines
    contours, _ = cv2.findContours(vertical_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter contours to keep only long vertical lines
    min_height = binary.shape[0] * 0.3  # Line must be at least 30% of table height (more lenient)
    vertical_line_x = []

    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        # Check if the contour is a long vertical line
        if h > min_height and w < 15:  # Width less than 15 pixels (more lenient)
            # Use the center of the line
            center_x = x + w // 2
            vertical_line_x.append(center_x)
            # Draw the vertical line on the visualization image
            cv2.line(vis_img, (center_x, 0), (center_x, height), (0, 255, 0), 2)

    # If we found more than 2 vertical lines, keep only the most significant ones
    if len(vertical_line_x) > 2:
        # Group nearby lines (within 20 pixels) and use their average position
        grouped_lines = []
        current_group = [vertical_line_x[0]]

        for i in range(1, len(vertical_line_x)):
            if vertical_line_x[i] - current_group[-1] < 20:
                # Add to current group if close enough
                current_group.append(vertical_line_x[i])
            else:
                # Start a new group
                grouped_lines.append(int(sum(current_group) / len(current_group)))
                current_group = [vertical_line_x[i]]

        # Add the last group
        if current_group:
            grouped_lines.append(int(sum(current_group) / len(current_group)))

        vertical_line_x = grouped_lines

    # Sort the x-coordinates
    vertical_line_x.sort()

    # We need to find 2 column boundaries for 3 columns
    # If we found enough vertical lines, use them as boundaries
    if len(vertical_line_x) >= 3:
        # Use the 2nd and 3rd vertical lines as column boundaries
        # (The 1st line is usually the left edge of the table)
        column_boundaries = vertical_line_x[1:3]
    elif len(vertical_line_x) == 2:
        # If we only found 2 vertical lines, use them as boundaries
        column_boundaries = vertical_line_x
    else:
        # If we couldn't find enough vertical lines, use the vertical projection method

        # Calculate vertical projection
        v_projection = np.sum(binary, axis=0)

        # Normalize projection
        if np.max(v_projection) > 0:
            v_projection = v_projection / np.max(v_projection)

        # Apply Gaussian blur to smooth the projection
        v_projection_smooth = np.convolve(v_projection, np.ones(15)/15, mode='same')

        # Find all local minima in the projection
        local_minima = []
        for i in range(1, len(v_projection_smooth) - 1):
            if v_projection_smooth[i] < v_projection_smooth[i-1] and v_projection_smooth[i] < v_projection_smooth[i+1]:
                # Only consider significant valleys (low density)
                if v_projection_smooth[i] < 0.3:  # Threshold for significant valley
                    local_minima.append((i, v_projection_smooth[i]))

        # Sort local minima by their values (lowest first)
        local_minima.sort(key=lambda x: x[1])

        # Take the two most significant valleys
        if len(local_minima) >= 2:
            # Get the indices of the two most significant valleys
            valley_indices = [x[0] for x in local_minima[:2]]
            # Sort them by position
            valley_indices.sort()
            column_boundaries = valley_indices
        else:
            # If we couldn't find enough significant valleys, use default positions
            column_boundaries = [int(width * 1/3), int(width * 2/3)]

    # Ensure we have exactly 2 boundaries
    if len(column_boundaries) < 2:
        column_boundaries = [int(width * 1/3), int(width * 2/3)]
    elif len(column_boundaries) > 2:
        column_boundaries = column_boundaries[:2]

    # Ensure the boundaries are in the correct order
    column_boundaries.sort()

    # Try to read the column boundaries from the file
    boundaries_path = Path(str(image_path).replace("page1_table1_inverted_lined.png", "column_boundaries.txt"))
    if boundaries_path.exists():
        try:
            with open(boundaries_path, 'r') as f:
                boundaries_str = f.read().strip()
                column_boundaries = [int(x) for x in boundaries_str.split(',')]
                logger.info(f"Read column boundaries from file: {column_boundaries}")
        except Exception as e:
            logger.error(f"Error reading column boundaries from file: {e}")
    else:
        logger.warning(f"Column boundaries file not found: {boundaries_path}")
        logger.warning("Using detected boundaries instead")

    # Draw the column boundaries on the image
    for boundary in column_boundaries:
        cv2.line(vis_img, (boundary, 0), (boundary, height), (0, 0, 255), 2)

    # Calculate the column widths
    col_widths = []
    col_edges = [0] + column_boundaries + [width]
    for i in range(len(col_edges) - 1):
        col_widths.append(col_edges[i+1] - col_edges[i])

    # Add text to show the column widths
    for i, width_val in enumerate(col_widths):
        x_pos = col_edges[i] + width_val // 2
        cv2.putText(vis_img, f"Col {i+1}: {width_val}px", (x_pos - 50, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)

    # Plot the vertical projection
    plt.figure(figsize=(15, 10))

    # Plot the image with column boundaries
    plt.subplot(2, 1, 1)
    plt.imshow(cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB))
    plt.title(f"Column Boundaries in {Path(image_path).name}")
    plt.axis('off')

    # Plot the vertical projection
    plt.subplot(2, 1, 2)
    v_projection = np.sum(binary, axis=0)
    if np.max(v_projection) > 0:
        v_projection = v_projection / np.max(v_projection)
    v_projection_smooth = np.convolve(v_projection, np.ones(15)/15, mode='same')

    plt.plot(v_projection_smooth)
    plt.title("Vertical Projection")
    plt.xlabel("X-coordinate")
    plt.ylabel("Normalized Density")

    # Draw vertical lines at the column boundaries
    for boundary in column_boundaries:
        plt.axvline(x=boundary, color='r', linestyle='--')

    # Find local minima for visualization
    local_minima = []
    for i in range(1, len(v_projection_smooth) - 1):
        if v_projection_smooth[i] < v_projection_smooth[i-1] and v_projection_smooth[i] < v_projection_smooth[i+1]:
            # Only consider significant valleys (low density)
            if v_projection_smooth[i] < 0.3:  # Threshold for significant valley
                local_minima.append((i, v_projection_smooth[i]))

    # Sort local minima by their values (lowest first)
    local_minima.sort(key=lambda x: x[1])

    # Draw circles at the local minima
    for i, (idx, val) in enumerate(local_minima):
        if i < 10:  # Only draw the top 10 minima
            plt.plot(idx, val, 'go', markersize=10)
            plt.text(idx + 10, val, f"{i+1}: {val:.2f}")

    plt.tight_layout()

    # Save the visualization
    if output_path:
        plt.savefig(str(output_path))
        logger.info(f"Saved visualization to {output_path}")
    else:
        plt.show()

def main():
    """Main entry point for the script."""
    # Define the paths
    input_dir = Path("test_output/type2_slightly_aggressive/subtables/page1")
    output_dir = Path("test_output/type2_slightly_aggressive/column_boundaries")
    output_dir.mkdir(parents=True, exist_ok=True)

    # Find the inverted lined image
    inverted_lined_path = input_dir / "page1_table1_inverted_lined.png"

    if inverted_lined_path.exists():
        logger.info(f"Visualizing column boundaries in {inverted_lined_path}")
        output_path = output_dir / "column_boundaries_visualization.png"
        visualize_column_boundaries(inverted_lined_path, output_path)
    else:
        logger.error(f"Inverted lined image not found: {inverted_lined_path}")

if __name__ == "__main__":
    main()
