#!/usr/bin/env python3
import logging
import sys
from pathlib import Path
from jortextractor.benchmarks.ocr_space_client import OCRSpaceClient

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[logging.StreamHandler(sys.stdout)])

# Create OCR.space client
client = OCRSpaceClient()

# Process a file
image_path = Path('pre-ocr-tables/page1_type1_sub_table_1_inverted_lined.png')
print(f"Processing file: {image_path}")
print(f"File exists: {image_path.exists()}")
print(f"File size: {image_path.stat().st_size / 1024:.1f} KB")

# Process the file
result = client.process_file(image_path)

# Print the result
if result:
    print("OCR.space result:")
    if 'ParsedResults' in result and result['ParsedResults']:
        for i, parsed_result in enumerate(result['ParsedResults']):
            if 'ParsedText' in parsed_result:
                print(f"Result {i+1} text preview: {parsed_result['ParsedText'][:100]}...")
    else:
        print("No parsed results")
else:
    print("No result returned")

print("Done")
