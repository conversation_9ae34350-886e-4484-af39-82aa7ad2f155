#!/usr/bin/env python3
"""
Test script for processing type2 documents with OCR.space.
"""
import os
import sys
import logging
from pathlib import Path
import tempfile
import cv2
import numpy as np
from pdf2image import convert_from_path

# Add the subproject to the Python path
sys.path.append(str(Path("Subprojects/JORTPROC_OCRSpace")))

# Import from the subproject
from ocr_space import OCRSpaceClient, process_ocr_space

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def convert_pdf_to_images(pdf_path, output_dir, dpi=300):
    """Convert a PDF to images.
    
    Args:
        pdf_path: Path to the PDF file
        output_dir: Directory to save the output images
        dpi: DPI for the conversion (default: 300)
        
    Returns:
        List of paths to the generated images
    """
    # Convert PDF to images
    images = convert_from_path(
        pdf_path,
        dpi=dpi,
        thread_count=4,
        use_cropbox=True,
        strict=False
    )
    
    # Save each image
    image_paths = []
    for i, img in enumerate(images, 1):
        # Convert PIL image to numpy array
        img_np = np.array(img)
        img_cv = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
        
        # Save the image
        img_path = output_dir / f"page{i}.png"
        cv2.imwrite(str(img_path), img_cv, [cv2.IMWRITE_PNG_COMPRESSION, 0])
        logger.info(f"Saved page {i} to {img_path}")
        
        # Create inverted version (often better for OCR)
        if len(img_cv.shape) == 3:
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        else:
            gray = img_cv.copy()
            
        inverted = cv2.bitwise_not(gray)
        
        # Save the inverted image
        inverted_path = output_dir / f"page{i}_inverted_lined.png"
        cv2.imwrite(str(inverted_path), inverted, [cv2.IMWRITE_PNG_COMPRESSION, 0])
        logger.info(f"Saved inverted image to {inverted_path}")
        
        image_paths.append(inverted_path)
    
    return image_paths

def main():
    """Main entry point for the test script."""
    # Define paths
    pdf_path = Path("data/examples/type2-information-in-multiple-columns.pdf")
    output_dir = Path("test_output/type2_test")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Convert PDF to images
    logger.info(f"Converting PDF {pdf_path} to images")
    image_paths = convert_pdf_to_images(pdf_path, output_dir)
    
    # Process OCR with OCR.space
    logger.info(f"Processing OCR with OCR.space")
    output_file = output_dir / "type2_ocr_space_results.csv"
    
    # Define headers based on type2 format
    headers = "NOMS & PRENOMS,CIN,ADRESSE"
    
    # Process OCR
    process_ocr_space(output_dir, headers, output_file)
    
    logger.info(f"Saved OCR results to {output_file}")

if __name__ == "__main__":
    main()
